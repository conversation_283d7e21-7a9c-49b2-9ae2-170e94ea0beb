//
//  InsightGenerationManagerTests.swift
//  BabyPulseTests
//
//  Created for BabyPulse app
//

import XCTest
import SwiftData
@testable import BabyPulse

final class InsightGenerationManagerTests: XCTestCase {
    
    var modelContainer: ModelContainer!
    var modelContext: ModelContext!
    var generationManager: InsightGenerationManager!
    var testBaby: Baby!
    
    override func setUpWithError() throws {
        // Create an in-memory container for testing
        let schema = Schema([
            Baby.self,
            Insight.self,
            FeedingEntry.self,
            SleepEntry.self,
            DiaperEntry.self,
            GrowthEntry.self,
            HealthEntry.self
        ])
        
        modelContainer = try ModelContainer(for: schema, configurations: [.init(isStoredInMemoryOnly: true)])
        modelContext = ModelContext(modelContainer)
        
        // Create a test baby
        testBaby = Baby(name: "Test Baby", dateOfBirth: Date().addingTimeInterval(-30*24*60*60)) // 30 days old
        modelContext.insert(testBaby)
        
        // Initialize the manager
        generationManager = InsightGenerationManager.shared
        generationManager.initialize(with: modelContext)
        
        // Reset the generation state
        try resetGenerationState()
    }
    
    override func tearDownWithError() throws {
        // Clean up
        modelContainer = nil
        modelContext = nil
        generationManager = nil
        testBaby = nil
    }
    
    // MARK: - Helper Methods
    
    private func resetGenerationState() throws {
        // Access the private properties using reflection to reset them for testing
        let mirror = Mirror(reflecting: generationManager)
        
        for child in mirror.children {
            if child.label == "eventsSinceLastRun" {
                if let eventsSinceLastRun = child.value as? [UUID: [Insight.InsightCategory: Int]] {
                    // Clear the events count
                    for (key, _) in eventsSinceLastRun {
                        for category in Insight.InsightCategory.allCases {
                            generationManager.setValue(0, forKeyPath: "eventsSinceLastRun[\(key)][\(category.rawValue)]")
                        }
                    }
                }
            }
            
            if child.label == "lastRunAt" {
                if let lastRunAt = child.value as? [UUID: [Insight.InsightCategory: Date]] {
                    // Reset the last run time to distant past
                    for (key, _) in lastRunAt {
                        for category in Insight.InsightCategory.allCases {
                            generationManager.setValue(Date.distantPast, forKeyPath: "lastRunAt[\(key)][\(category.rawValue)]")
                        }
                    }
                }
            }
        }
    }
    
    private func createTestEntries(category: Insight.InsightCategory, count: Int) {
        for _ in 0..<count {
            switch category {
            case .feeding:
                let entry = FeedingEntry(type: .bottle, startTime: Date(), endTime: Date().addingTimeInterval(15*60))
                entry.baby = testBaby
                modelContext.insert(entry)
                NotificationCenter.default.post(name: .feedingEntryAdded, object: entry)
                
            case .sleep:
                let entry = SleepEntry(startTime: Date().addingTimeInterval(-2*60*60), endTime: Date())
                entry.baby = testBaby
                modelContext.insert(entry)
                NotificationCenter.default.post(name: .sleepEntryAdded, object: entry)
                
            case .diaper:
                let entry = DiaperEntry(type: .wet, timestamp: Date())
                entry.baby = testBaby
                modelContext.insert(entry)
                NotificationCenter.default.post(name: .diaperEntryAdded, object: entry)
                
            case .growth:
                let entry = GrowthEntry(timestamp: Date(), weight: 4.5, height: 55, headCircumference: 38)
                entry.baby = testBaby
                modelContext.insert(entry)
                NotificationCenter.default.post(name: .growthEntryAdded, object: entry)
                
            case .health:
                let entry = HealthEntry(type: .temperature, timestamp: Date(), value: 37.0)
                entry.baby = testBaby
                modelContext.insert(entry)
                NotificationCenter.default.post(name: .healthEntryAdded, object: entry)
                
            case .development:
                // Development entries not implemented yet
                break
            }
        }
    }
    
    // MARK: - Tests
    
    func testEventTracking() throws {
        // Create test entries
        createTestEntries(category: .feeding, count: 2)
        
        // Check if events are being tracked
        let mirror = Mirror(reflecting: generationManager)
        
        for child in mirror.children {
            if child.label == "eventsSinceLastRun" {
                if let eventsSinceLastRun = child.value as? [UUID: [Insight.InsightCategory: Int]] {
                    XCTAssertEqual(eventsSinceLastRun[testBaby.id]?[.feeding], 2, "Should track 2 feeding events")
                }
            }
        }
    }
    
    func testCategoryThresholds() throws {
        // Test that insights are only generated when thresholds are met
        
        // 1. Create just below threshold entries for feeding (threshold is typically 3)
        createTestEntries(category: .feeding, count: 2)
        
        // Check if any insights were generated
        var descriptor = FetchDescriptor<Insight>()
        var insights = try modelContext.fetch(descriptor)
        XCTAssertEqual(insights.count, 0, "No insights should be generated below threshold")
        
        // 2. Add one more entry to meet the threshold
        createTestEntries(category: .feeding, count: 1)
        
        // Wait a moment for async processing
        let expectation = XCTestExpectation(description: "Wait for insight generation")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2)
        
        // Check if insights were generated
        descriptor = FetchDescriptor<Insight>()
        insights = try modelContext.fetch(descriptor)
        
        // Note: This might fail if the threshold in InsightGenerationSettings.plist is different
        // or if the generation is fully async and takes longer
        XCTAssertGreaterThan(insights.count, 0, "Insights should be generated when threshold is met")
    }
    
    func testHashKeyGeneration() throws {
        // Create an insight with known content
        let insight = Insight(
            category: .feeding,
            title: "Test Insight",
            metric: "Test Metric",
            insightContent: "This is a test insight content",
            timestamp: Date(),
            baby: testBaby
        )
        
        // Use the private method to generate a hash key
        let mirror = Mirror(reflecting: generationManager)
        var generateHashKeyMethod: ((String) -> String)?
        
        for child in mirror.children {
            if child.label == "generateHashKey" {
                generateHashKeyMethod = child.value as? ((String) -> String)
            }
        }
        
        // If we couldn't access the method, skip the test
        guard let generateHashKey = generateHashKeyMethod else {
            XCTFail("Could not access generateHashKey method")
            return
        }
        
        // Generate a hash for the insight
        let hashInput = "\(insight.category.rawValue)_\(insight.title)_\(insight.insightContent)"
        let hashKey = generateHashKey(hashInput)
        
        // Verify the hash is not empty and has the expected format (SHA-256 is 64 hex chars)
        XCTAssertEqual(hashKey.count, 64, "SHA-256 hash should be 64 characters long")
        XCTAssertTrue(hashKey.allSatisfy { $0.isHexDigit }, "Hash should contain only hex digits")
        
        // Generate the hash again and verify it's the same (deterministic)
        let hashKey2 = generateHashKey(hashInput)
        XCTAssertEqual(hashKey, hashKey2, "Hash generation should be deterministic")
    }
    
    func testSeverityDetermination() throws {
        // Create insights with different content to test severity determination
        let urgentInsight = Insight(
            category: .health,
            title: "Urgent Test",
            metric: "Test",
            insightContent: "This is an urgent situation that requires immediate attention.",
            timestamp: Date(),
            baby: testBaby
        )
        
        let warningInsight = Insight(
            category: .sleep,
            title: "Warning Test",
            metric: "Test",
            insightContent: "You should monitor this situation closely as it may need attention.",
            timestamp: Date(),
            baby: testBaby
        )
        
        let infoInsight = Insight(
            category: .feeding,
            title: "Info Test",
            metric: "Test",
            insightContent: "Everything is going well with the feeding pattern.",
            timestamp: Date(),
            baby: testBaby
        )
        
        // Use the private method to determine severity
        let mirror = Mirror(reflecting: generationManager)
        var determineSeverityMethod: ((Insight) -> Insight.InsightSeverity)?
        
        for child in mirror.children {
            if child.label == "determineSeverity" {
                determineSeverityMethod = child.value as? ((Insight) -> Insight.InsightSeverity)
            }
        }
        
        // If we couldn't access the method, skip the test
        guard let determineSeverity = determineSeverityMethod else {
            XCTFail("Could not access determineSeverity method")
            return
        }
        
        // Test severity determination
        XCTAssertEqual(determineSeverity(urgentInsight), .urgent, "Should detect urgent severity")
        XCTAssertEqual(determineSeverity(warningInsight), .warning, "Should detect warning severity")
        XCTAssertEqual(determineSeverity(infoInsight), .info, "Should default to info severity")
    }
    
    func testActionItemExtraction() throws {
        // Create an insight with action items in the content
        let insightWithBullets = Insight(
            category: .sleep,
            title: "Sleep Recommendations",
            metric: "Test",
            insightContent: "Your baby's sleep pattern has changed. Here are some recommendations:\n- Maintain a consistent bedtime routine\n- Consider adjusting daytime naps\n- Watch for signs of overtiredness",
            timestamp: Date(),
            baby: testBaby
        )
        
        // Use the private method to extract action items
        let mirror = Mirror(reflecting: generationManager)
        var extractActionItemsMethod: ((String) -> [String])?
        
        for child in mirror.children {
            if child.label == "extractActionItems" {
                extractActionItemsMethod = child.value as? ((String) -> [String])
            }
        }
        
        // If we couldn't access the method, skip the test
        guard let extractActionItems = extractActionItemsMethod else {
            XCTFail("Could not access extractActionItems method")
            return
        }
        
        // Test action item extraction
        let actionItems = extractActionItems(insightWithBullets.insightContent)
        XCTAssertEqual(actionItems.count, 3, "Should extract 3 action items")
        XCTAssertTrue(actionItems.contains("Maintain a consistent bedtime routine"), "Should extract first bullet point")
        XCTAssertTrue(actionItems.contains("Consider adjusting daytime naps"), "Should extract second bullet point")
        XCTAssertTrue(actionItems.contains("Watch for signs of overtiredness"), "Should extract third bullet point")
    }
}
