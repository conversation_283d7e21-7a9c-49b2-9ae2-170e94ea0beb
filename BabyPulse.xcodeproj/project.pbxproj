// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		3A9204BD2DD0780E00C5CCF0 /* MarkdownView in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204BC2DD0780E00C5CCF0 /* MarkdownView */; };
		3A9204C02DD0781800C5CCF0 /* Yams in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204BF2DD0781800C5CCF0 /* Yams */; };
		3A9204C32DD0783A00C5CCF0 /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204C22DD0783A00C5CCF0 /* Auth */; };
		3A9204C52DD0783A00C5CCF0 /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204C42DD0783A00C5CCF0 /* Functions */; };
		3A9204C72DD0783A00C5CCF0 /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204C62DD0783A00C5CCF0 /* PostgREST */; };
		3A9204C92DD0783A00C5CCF0 /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204C82DD0783A00C5CCF0 /* Realtime */; };
		3A9204CB2DD0783A00C5CCF0 /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204CA2DD0783A00C5CCF0 /* Storage */; };
		3A9204CE2DD0785B00C5CCF0 /* ReceiptParser in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204CD2DD0785B00C5CCF0 /* ReceiptParser */; };
		3A9204D62DD078A600C5CCF0 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 3A9204D52DD078A600C5CCF0 /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		3AE56EAD2DB884EF0012C521 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3AE56E922DB884ED0012C521 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3AE56E992DB884ED0012C521;
			remoteInfo = BabyPulse;
		};
		3AE56EB72DB884EF0012C521 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3AE56E922DB884ED0012C521 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3AE56E992DB884ED0012C521;
			remoteInfo = BabyPulse;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3AE56E9A2DB884ED0012C521 /* BabyPulse.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BabyPulse.app; sourceTree = BUILT_PRODUCTS_DIR; };
		3AE56EAC2DB884EF0012C521 /* BabyPulseTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BabyPulseTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3AE56EB62DB884EF0012C521 /* BabyPulseUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BabyPulseUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3AE56E9C2DB884ED0012C521 /* BabyPulse */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BabyPulse;
			sourceTree = "<group>";
		};
		3AE56EAF2DB884EF0012C521 /* BabyPulseTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BabyPulseTests;
			sourceTree = "<group>";
		};
		3AE56EB92DB884EF0012C521 /* BabyPulseUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BabyPulseUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3AE56E972DB884ED0012C521 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3A9204C02DD0781800C5CCF0 /* Yams in Frameworks */,
				3A9204BD2DD0780E00C5CCF0 /* MarkdownView in Frameworks */,
				3A9204CB2DD0783A00C5CCF0 /* Storage in Frameworks */,
				3A9204C52DD0783A00C5CCF0 /* Functions in Frameworks */,
				3A9204C92DD0783A00C5CCF0 /* Realtime in Frameworks */,
				3A9204C32DD0783A00C5CCF0 /* Auth in Frameworks */,
				3A9204CE2DD0785B00C5CCF0 /* ReceiptParser in Frameworks */,
				3A9204D62DD078A600C5CCF0 /* Supabase in Frameworks */,
				3A9204C72DD0783A00C5CCF0 /* PostgREST in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EA92DB884EF0012C521 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EB32DB884EF0012C521 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3AAE87B72DBCAABE00995C9B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3AE56E912DB884ED0012C521 = {
			isa = PBXGroup;
			children = (
				3AE56E9C2DB884ED0012C521 /* BabyPulse */,
				3AE56EAF2DB884EF0012C521 /* BabyPulseTests */,
				3AE56EB92DB884EF0012C521 /* BabyPulseUITests */,
				3AAE87B72DBCAABE00995C9B /* Frameworks */,
				3AE56E9B2DB884ED0012C521 /* Products */,
			);
			sourceTree = "<group>";
		};
		3AE56E9B2DB884ED0012C521 /* Products */ = {
			isa = PBXGroup;
			children = (
				3AE56E9A2DB884ED0012C521 /* BabyPulse.app */,
				3AE56EAC2DB884EF0012C521 /* BabyPulseTests.xctest */,
				3AE56EB62DB884EF0012C521 /* BabyPulseUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3AE56E992DB884ED0012C521 /* BabyPulse */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3AE56EC02DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulse" */;
			buildPhases = (
				3AE56E962DB884ED0012C521 /* Sources */,
				3AE56E972DB884ED0012C521 /* Frameworks */,
				3AE56E982DB884ED0012C521 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3AE56E9C2DB884ED0012C521 /* BabyPulse */,
			);
			name = BabyPulse;
			packageProductDependencies = (
				3A9204BC2DD0780E00C5CCF0 /* MarkdownView */,
				3A9204BF2DD0781800C5CCF0 /* Yams */,
				3A9204C22DD0783A00C5CCF0 /* Auth */,
				3A9204C42DD0783A00C5CCF0 /* Functions */,
				3A9204C62DD0783A00C5CCF0 /* PostgREST */,
				3A9204C82DD0783A00C5CCF0 /* Realtime */,
				3A9204CA2DD0783A00C5CCF0 /* Storage */,
				3A9204CD2DD0785B00C5CCF0 /* ReceiptParser */,
				3A9204D52DD078A600C5CCF0 /* Supabase */,
			);
			productName = BabyPulse;
			productReference = 3AE56E9A2DB884ED0012C521 /* BabyPulse.app */;
			productType = "com.apple.product-type.application";
		};
		3AE56EAB2DB884EF0012C521 /* BabyPulseTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3AE56EC32DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulseTests" */;
			buildPhases = (
				3AE56EA82DB884EF0012C521 /* Sources */,
				3AE56EA92DB884EF0012C521 /* Frameworks */,
				3AE56EAA2DB884EF0012C521 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3AE56EAE2DB884EF0012C521 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3AE56EAF2DB884EF0012C521 /* BabyPulseTests */,
			);
			name = BabyPulseTests;
			packageProductDependencies = (
			);
			productName = BabyPulseTests;
			productReference = 3AE56EAC2DB884EF0012C521 /* BabyPulseTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		3AE56EB52DB884EF0012C521 /* BabyPulseUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3AE56EC62DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulseUITests" */;
			buildPhases = (
				3AE56EB22DB884EF0012C521 /* Sources */,
				3AE56EB32DB884EF0012C521 /* Frameworks */,
				3AE56EB42DB884EF0012C521 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3AE56EB82DB884EF0012C521 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3AE56EB92DB884EF0012C521 /* BabyPulseUITests */,
			);
			name = BabyPulseUITests;
			packageProductDependencies = (
			);
			productName = BabyPulseUITests;
			productReference = 3AE56EB62DB884EF0012C521 /* BabyPulseUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3AE56E922DB884ED0012C521 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					3AE56E992DB884ED0012C521 = {
						CreatedOnToolsVersion = 16.2;
					};
					3AE56EAB2DB884EF0012C521 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 3AE56E992DB884ED0012C521;
					};
					3AE56EB52DB884EF0012C521 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 3AE56E992DB884ED0012C521;
					};
				};
			};
			buildConfigurationList = 3AE56E952DB884ED0012C521 /* Build configuration list for PBXProject "BabyPulse" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 3AE56E912DB884ED0012C521;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				3A9204BB2DD0780E00C5CCF0 /* XCRemoteSwiftPackageReference "MarkdownView" */,
				3A9204BE2DD0781800C5CCF0 /* XCRemoteSwiftPackageReference "Yams" */,
				3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */,
				3A9204CC2DD0785B00C5CCF0 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3AE56E9B2DB884ED0012C521 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3AE56E992DB884ED0012C521 /* BabyPulse */,
				3AE56EAB2DB884EF0012C521 /* BabyPulseTests */,
				3AE56EB52DB884EF0012C521 /* BabyPulseUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3AE56E982DB884ED0012C521 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EAA2DB884EF0012C521 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EB42DB884EF0012C521 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3AE56E962DB884ED0012C521 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EA82DB884EF0012C521 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AE56EB22DB884EF0012C521 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3AE56EAE2DB884EF0012C521 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3AE56E992DB884ED0012C521 /* BabyPulse */;
			targetProxy = 3AE56EAD2DB884EF0012C521 /* PBXContainerItemProxy */;
		};
		3AE56EB82DB884EF0012C521 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3AE56E992DB884ED0012C521 /* BabyPulse */;
			targetProxy = 3AE56EB72DB884EF0012C521 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		3AE56EBE2DB884EF0012C521 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		3AE56EBF2DB884EF0012C521 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3AE56EC12DB884EF0012C521 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"BabyPulse/Preview Content\"";
				DEVELOPMENT_TEAM = 2H749JGYBJ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = BabyPulse;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulse;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		3AE56EC22DB884EF0012C521 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"BabyPulse/Preview Content\"";
				DEVELOPMENT_TEAM = 2H749JGYBJ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = BabyPulse;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulse;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		3AE56EC42DB884EF0012C521 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BabyPulse.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/BabyPulse";
			};
			name = Debug;
		};
		3AE56EC52DB884EF0012C521 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BabyPulse.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/BabyPulse";
			};
			name = Release;
		};
		3AE56EC72DB884EF0012C521 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BabyPulse;
			};
			name = Debug;
		};
		3AE56EC82DB884EF0012C521 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.babypulse.BabyPulseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BabyPulse;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3AE56E952DB884ED0012C521 /* Build configuration list for PBXProject "BabyPulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3AE56EBE2DB884EF0012C521 /* Debug */,
				3AE56EBF2DB884EF0012C521 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3AE56EC02DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3AE56EC12DB884EF0012C521 /* Debug */,
				3AE56EC22DB884EF0012C521 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3AE56EC32DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulseTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3AE56EC42DB884EF0012C521 /* Debug */,
				3AE56EC52DB884EF0012C521 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3AE56EC62DB884EF0012C521 /* Build configuration list for PBXNativeTarget "BabyPulseUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3AE56EC72DB884EF0012C521 /* Debug */,
				3AE56EC82DB884EF0012C521 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		3A9204BB2DD0780E00C5CCF0 /* XCRemoteSwiftPackageReference "MarkdownView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/keitaoouchi/MarkdownView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.9.1;
			};
		};
		3A9204BE2DD0781800C5CCF0 /* XCRemoteSwiftPackageReference "Yams" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/jpsim/Yams.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.3.1;
			};
		};
		3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase-community/supabase-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
		3A9204CC2DD0785B00C5CCF0 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.23.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		3A9204BC2DD0780E00C5CCF0 /* MarkdownView */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204BB2DD0780E00C5CCF0 /* XCRemoteSwiftPackageReference "MarkdownView" */;
			productName = MarkdownView;
		};
		3A9204BF2DD0781800C5CCF0 /* Yams */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204BE2DD0781800C5CCF0 /* XCRemoteSwiftPackageReference "Yams" */;
			productName = Yams;
		};
		3A9204C22DD0783A00C5CCF0 /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		3A9204C42DD0783A00C5CCF0 /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		3A9204C62DD0783A00C5CCF0 /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		3A9204C82DD0783A00C5CCF0 /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		3A9204CA2DD0783A00C5CCF0 /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		3A9204CD2DD0785B00C5CCF0 /* ReceiptParser */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204CC2DD0785B00C5CCF0 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = ReceiptParser;
		};
		3A9204D52DD078A600C5CCF0 /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3A9204C12DD0783A00C5CCF0 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3AE56E922DB884ED0012C521 /* Project object */;
}
