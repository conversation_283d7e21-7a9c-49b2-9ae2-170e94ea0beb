---
description:
globs:
alwaysApply: true
---
# BabyPulse Project Overview

BabyPulse is an iOS application for tracking baby activities and health metrics. The app provides various features including:

- Activity tracking (feeding, diapers, sleep)
- Growth tracking
- Health monitoring
- Insights generation
- Chat interface

## Key Files

- [BabyPulseApp.swift](mdc:BabyPulse/BabyPulseApp.swift) - Main app entry point
- [ContentView.swift](mdc:BabyPulse/ContentView.swift) - Root view that handles navigation

## Main Components

- Models - SwiftData models for persistence
- Views - SwiftUI views organized by feature
- Services - Backend services and API integrations 
- ViewModels - Business logic and state management
