---
description:
globs:
alwaysApply: true
---
# BabyPulse Charts and Visualizations

BabyPulse uses various chart components to visualize baby data and trends.

## Growth Charts

- [WeightChartView.swift](mdc:BabyPulse/Views/Charts/WeightChartView.swift) - Weight growth visualization
- [HeightChartView.swift](mdc:BabyPulse/Views/Charts/HeightChartView.swift) - Height growth visualization
- [HeadCircumferenceChartView.swift](mdc:BabyPulse/Views/Charts/HeadCircumferenceChartView.swift) - Head circumference visualization

## Activity Charts

- [FeedingChartView.swift](mdc:BabyPulse/Views/Charts/FeedingChartView.swift) - Feeding patterns and trends
- [DiaperChartView.swift](mdc:BabyPulse/Views/Charts/DiaperChartView.swift) - Diaper change patterns
- [SleepChartView.swift](mdc:BabyPulse/Views/Charts/SleepChartView.swift) - Sleep patterns and duration

## Analysis Visualizations

- [PatternGridView.swift](mdc:BabyPulse/Views/Charts/PatternGridView.swift) - Grid visualization for pattern detection
- [LogChartView.swift](mdc:BabyPulse/Views/Charts/LogChartView.swift) - General-purpose log visualization

## Data Model

- [ChartData.swift](mdc:BabyPulse/Models/ChartData.swift) - Data structures for chart rendering
