---
description:
globs:
alwaysApply: true
---
# BabyPulse Coding Standards

This rule outlines the coding standards and best practices for the BabyPulse project.

## Swift and SwiftUI Standards

- Use Swift's latest language features and idioms
- Follow SwiftUI declarative programming patterns
- Prefer composition over inheritance
- Use SwiftData for persistence operations

## File Organization

- Group related files by feature/module
- Model files define persistence schema
- View files contain only UI logic
- ViewModel files handle business logic

## Naming Conventions

- **Models**: Noun-based names (e.g., `Baby`, `FeedingEntry`)
- **Views**: Suffix with "View" (e.g., `HomeView`, `FeedingLogView`)
- **ViewModels**: Suffix with "ViewModel" (e.g., `HomeViewModel`)
- **Services**: Suffix with "Service" (e.g., `SupabaseService`)

## Code Documentation

- Add documentation comments to public APIs
- Include parameter descriptions
- Document complex logic or algorithms
- Use // MARK: for logical code sections
