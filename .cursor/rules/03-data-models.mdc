---
description:
globs:
alwaysApply: true
---
# BabyPulse Data Models

BabyPulse uses SwiftData for persisting app data locally. Below are the key models:

## Core Models

- [Baby.swift](mdc:BabyPulse/Models/Baby.swift) - Baby profile information
- [UserPreferences.swift](mdc:BabyPulse/Models/UserPreferences.swift) - User settings and preferences
- [UserAccount.swift](mdc:BabyPulse/Models/UserAccount.swift) - User account information

## Activity Tracking Models

- [BaseEntry.swift](mdc:BabyPulse/Models/BaseEntry.swift) - Base class for all entry types
- [FeedingEntry.swift](mdc:BabyPulse/Models/FeedingEntry.swift) - Feeding tracking
- [DiaperEntry.swift](mdc:BabyPulse/Models/DiaperEntry.swift) - Diaper changes
- [SleepEntry.swift](mdc:BabyPulse/Models/SleepEntry.swift) - Sleep tracking
- [GrowthEntry.swift](mdc:BabyPulse/Models/GrowthEntry.swift) - Growth measurements
- [HealthEntry.swift](mdc:BabyPulse/Models/HealthEntry.swift) - Health related entries

## Insights and Chat Models

- [Insight.swift](mdc:BabyPulse/Models/Insight.swift) - AI-generated insights
- [ChatThread.swift](mdc:BabyPulse/Models/ChatThread.swift) - Chat conversation threads
- [ChatMessage.swift](mdc:BabyPulse/Models/ChatMessage.swift) - Individual chat messages
