---
description:
globs:
alwaysApply: true
---
# BabyPulse Services

The app uses various services to handle backend communication, data analysis, and AI features.

## Core Services

- [SupabaseService.swift](mdc:BabyPulse/Services/SupabaseService.swift) - Backend API integration
- [RevenueCatService.swift](mdc:BabyPulse/Services/RevenueCatService.swift) - In-app purchase management
- [LLMService.swift](mdc:BabyPulse/Services/LLMService.swift) - Large Language Model integration

## Data Analysis Services

- [PatternDetectionUtilities.swift](mdc:BabyPulse/Services/PatternDetectionUtilities.swift) - Detects patterns in baby activities
- [CorrelationAnalysisService.swift](mdc:BabyPulse/Services/CorrelationAnalysisService.swift) - Analyzes correlations between activities
- [EventPredictionEngine.swift](mdc:BabyPulse/Services/EventPredictionEngine.swift) - Predicts future events based on patterns
- [PredictionAccuracyTracker.swift](mdc:BabyPulse/Services/PredictionAccuracyTracker.swift) - Tracks accuracy of predictions

## Insight Generation

- [InsightGenerationManager.swift](mdc:BabyPulse/Services/InsightGenerationManager.swift) - Manages insight generation
- [EnhancedInsightService.swift](mdc:BabyPulse/Services/EnhancedInsightService.swift) - Enhanced insight generation
- [PromptTemplateManager.swift](mdc:BabyPulse/Services/PromptTemplateManager.swift) - Manages LLM prompt templates
- [BabyContextSummariser.swift](mdc:BabyPulse/Services/BabyContextSummariser.swift) - Summarizes baby data for context
