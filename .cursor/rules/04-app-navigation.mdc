---
description:
globs:
alwaysApply: true
---
# BabyPulse Navigation Structure

The app uses a tab-based navigation structure with specialized views for each feature.

## Main Navigation

- [ContentView.swift](mdc:BabyPulse/ContentView.swift) - Root view that decides between onboarding and main content
- [MainTabView.swift](mdc:BabyPulse/Views/Main/MainTabView.swift) - Tab-based main navigation

## Main Tab Views

- [HomeView.swift](mdc:BabyPulse/Views/Main/HomeView.swift) - Home/dashboard view
- [LogsView.swift](mdc:BabyPulse/Views/Main/LogsView.swift) - Activity logging view
- [InsightsView.swift](mdc:BabyPulse/Views/Main/InsightsView.swift) - AI-generated insights view

## Feature Sections

- **Onboarding** - First-time user experience
- **Logging** - Data entry for various activities
- **Details** - Detailed views for entries
- **Charts** - Data visualization components
- **Settings** - App configuration
- **Chat** - AI assistant chat interface
