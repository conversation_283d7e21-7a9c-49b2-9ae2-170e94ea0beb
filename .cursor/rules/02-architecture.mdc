---
description:
globs:
alwaysApply: true
---
# BabyPulse Architecture

BabyPulse follows a modern SwiftUI architecture with SwiftData for persistence.

## Key Architectural Patterns

- **MVVM Pattern**: Views are backed by ViewModels that handle business logic
- **SwiftData**: For local data persistence
- **Environmental Objects**: For dependency injection of services
- **Composition**: Features are composed of reusable components

## Service Structure

The app uses various services for different features:

- Syncing data with backend ([SyncManager.swift](mdc:BabyPulse/Models/SyncManager.swift))
- User preferences management
- Insights generation
- Payment processing (RevenueCat)
- Backend communication (Supabase)
