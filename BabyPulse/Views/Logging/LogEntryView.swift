//
//  LogEntryView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

struct LogEntryView: View {
    @StateObject private var viewModel: LogEntryViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @Query private var babies: [Baby]
    @State private var isSaving = false
    @State private var showError = false
    @State private var errorMessage = ""

    // Initialize for creating a new entry
    init(category: LogCategory? = nil) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(category: category))
    }

    // Initialize for editing a feeding entry
    init(feedingEntry: FeedingEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(feedingEntry: feedingEntry))
    }

    // Initialize for editing a diaper entry
    init(diaperEntry: DiaperEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(diaperEntry: diaperEntry))
    }

    // Initialize for editing a sleep entry
    init(sleepEntry: SleepEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(sleepEntry: sleepEntry))
    }

    // Initialize for editing a growth entry
    init(growthEntry: GrowthEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(growthEntry: growthEntry))
    }

    // Initialize for editing a health entry
    init(healthEntry: HealthEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(healthEntry: healthEntry))
    }

    // Initialize for editing a general activity entry
    init(generalActivityEntry: GeneralActivityEntry) {
        _viewModel = StateObject(wrappedValue: LogEntryViewModel(generalActivityEntry: generalActivityEntry))
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Form content
                Group {
                    switch viewModel.selectedCategory {
                    case .feeding:
                        FeedingEntryForm(viewModel: viewModel)
                    case .diaper:
                        DiaperEntryForm(viewModel: viewModel)
                    case .sleep:
                        SleepEntryForm(viewModel: viewModel)
                    case .growth:
                        GrowthEntryForm(viewModel: viewModel)
                    case .health:
                        HealthEntryForm(viewModel: viewModel)
                    case .general:
                        GeneralActivityEntryForm(viewModel: viewModel)
                    case .none:
                        categorySelectionView
                    }
                }

                // Loading overlay
                if isSaving {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()

                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)

                        Text("Saving...")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(.white)
                    }
                    .frame(width: 150, height: 150)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(Color.black.opacity(0.7))
                    )
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if viewModel.selectedCategory != nil {
                        Button(action: {
                            viewModel.selectedCategory = nil
                        }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                Text("Categories")
                            }
                        }
                    } else {
                        Button("Cancel") {
                            dismiss()
                        }
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    if viewModel.selectedCategory != nil {
                        Button(viewModel.isEditMode ? "Update" : "Save") {
                            saveEntry()
                        }
                        .bold()
                        .foregroundColor(categoryColor)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle(navigationBarTitle)
        }
        .onAppear {
            if let firstBaby = babies.first {
                viewModel.setCurrentBaby(firstBaby)
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }

    private var navigationBarTitle: String {
        if viewModel.isEditMode {
            if let category = viewModel.selectedCategory {
                return "Edit \(category.title) Entry"
            } else {
                return "Edit Entry" // Fallback
            }
        } else {
            return "New Entry"
        }
    }

    private var categoryColor: Color {
        guard let category = viewModel.selectedCategory else {
            return BabyPulseColors.primary
        }

        switch category {
        case .feeding:
            return BabyPulseColors.feeding
        case .diaper:
            return BabyPulseColors.diaper
        case .sleep:
            return BabyPulseColors.sleep
        case .growth:
            return BabyPulseColors.growth
        case .health:
            return BabyPulseColors.health
        case .general:
            return BabyPulseColors.generalActivity ?? .gray
        }
    }

    // MARK: - Category Selection View

    private var categorySelectionView: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header
                VStack(spacing: BabyPulseLayout.spacingSM) {
                    // App icon with animation
                    ZStack {
                        Circle()
                            .fill(BabyPulseColors.primary.opacity(0.1))
                            .frame(width: 100, height: 100)
                        
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 50, weight: .medium))
                            .foregroundColor(BabyPulseColors.primary)
                    }
                    .padding(.top, BabyPulseLayout.spacingLG)

                    VStack(spacing: BabyPulseLayout.spacingXS) {
                        Text("New Entry")
                            .font(BabyPulseTypography.title2())
                            .fontWeight(.bold)
                            .foregroundColor(BabyPulseColors.secondary)

                        Text("What would you like to log today?")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, BabyPulseLayout.spacingMD)
                }

                // Adaptive Category Grid - changes columns based on content
                let columns = createAdaptiveColumns(for: LogCategory.allCases.count)
                LazyVGrid(columns: columns, spacing: BabyPulseLayout.spacingMD) {
                    ForEach(LogCategory.allCases) { category in
                        EnhancedCategoryCard(category: category) {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                viewModel.selectedCategory = category
                            }
                        }
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                // Quick actions section
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                    Text("Quick Actions")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)
                        .padding(.horizontal, BabyPulseLayout.paddingMD)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: BabyPulseLayout.spacingSM) {
                            QuickActionButton(
                                title: "Feeding",
                                icon: "cup.and.saucer.fill",
                                color: BabyPulseColors.feeding
                            ) {
                                viewModel.selectedCategory = .feeding
                            }
                            
                            QuickActionButton(
                                title: "Diaper",
                                icon: "heart.fill",
                                color: BabyPulseColors.diaper
                            ) {
                                viewModel.selectedCategory = .diaper
                            }
                            
                            QuickActionButton(
                                title: "Sleep",
                                icon: "moon.fill",
                                color: BabyPulseColors.sleep
                            ) {
                                viewModel.selectedCategory = .sleep
                            }
                        }
                        .padding(.horizontal, BabyPulseLayout.paddingMD)
                    }
                }
                .padding(.top, BabyPulseLayout.spacingMD)

                Spacer(minLength: 100)
            }
            .padding(.horizontal, BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
    }
    
    // Create adaptive columns based on category count
    private func createAdaptiveColumns(for count: Int) -> [GridItem] {
        // Use 2 columns for 6 or fewer items, 3 columns for more
        let columnCount = count <= 6 ? 2 : 3
        return Array(repeating: GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD), count: columnCount)
    }

    // MARK: - Save Entry

    private func saveEntry() {
        // Check if we have a baby profile
        if babies.isEmpty {
            errorMessage = "No baby profile found. Please create a baby profile first."
            showError = true
            return
        }

        // Ensure currentBaby is set
        if viewModel.currentBaby == nil, let firstBaby = babies.first {
            viewModel.setCurrentBaby(firstBaby)
        }

        // Still no baby? Show error
        if viewModel.currentBaby == nil {
            errorMessage = "Could not set baby profile. Please try again."
            showError = true
            return
        }

        isSaving = true

        // Use a slight delay to ensure UI is responsive
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            do {
                let success = viewModel.saveEntry(modelContext: modelContext)
                isSaving = false

                if success {
                    // Directly dismiss the view without showing confirmation
                    dismiss()
                } else {
                    // Show error if save failed
                    errorMessage = "Failed to save entry. Please check your inputs and try again."
                    showError = true
                }
            } catch {
                isSaving = false
                errorMessage = "An error occurred: \(error.localizedDescription)"
                showError = true
            }
        }
    }
}

// MARK: - Category Card

struct CategoryCard: View {
    let category: LogCategory
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: action) {
            VStack(spacing: BabyPulseLayout.spacingMD) {
                ZStack {
                    Circle()
                        .fill(category.color.opacity(0.15))
                        .frame(width: 80, height: 80)

                    Image(systemName: category.icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(category.color)
                }
                .shadow(color: category.color.opacity(0.2), radius: 8, x: 0, y: 4)

                Text(category.title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                    .shadow(color: Color.black.opacity(0.08), radius: 6, x: 0, y: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Enhanced Category Card

struct EnhancedCategoryCard: View {
    let category: LogCategory
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: BabyPulseLayout.spacingMD) {
                // Icon container with improved design
                ZStack {
                    // Background circle with gradient
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    category.color.opacity(0.15),
                                    category.color.opacity(0.25)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                    
                    // Outer ring
                    Circle()
                        .stroke(category.color.opacity(0.3), lineWidth: 1)
                        .frame(width: 70, height: 70)

                    // Icon
                    Image(systemName: category.icon)
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(category.color)
                        .scaleEffect(isPressed ? 0.9 : 1.0)
                }
                .shadow(color: category.color.opacity(0.3), radius: 6, x: 0, y: 3)

                // Title with improved typography
                Text(category.title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .minimumScaleFactor(0.8)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, BabyPulseLayout.paddingMD)
            .padding(.horizontal, BabyPulseLayout.paddingSM)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                    .shadow(
                        color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.08), 
                        radius: isPressed ? 2 : 8, 
                        x: 0, 
                        y: isPressed ? 1 : 4
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .accessibilityLabel("Log \(category.title)")
        .accessibilityHint("Opens the \(category.title.lowercased()) entry form")
    }
}

// MARK: - Quick Action Button

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: BabyPulseLayout.spacingSM) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                // Title
                Text(title)
                    .font(BabyPulseTypography.footnote())
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .padding(.vertical, BabyPulseLayout.paddingSM)
            .padding(.horizontal, BabyPulseLayout.paddingMD)
            .background(
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [color, color.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: color.opacity(0.4), radius: 4, x: 0, y: 2)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .accessibilityLabel("Quick log \(title)")
    }
}

#Preview {
    LogEntryView()
        .modelContainer(for: [Baby.self, FeedingEntry.self, DiaperEntry.self, SleepEntry.self, GrowthEntry.self, HealthEntry.self], inMemory: true)
}
