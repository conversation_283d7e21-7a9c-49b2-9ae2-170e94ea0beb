//
//  HealthEntryForm.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct HealthEntryForm: View {
    @ObservedObject var viewModel: LogEntryViewModel
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.unitSystem) private var unitSystem
    @EnvironmentObject private var userPreferencesManager: UserPreferencesManager

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header with icon
                FormHeaderView(
                    icon: "cross.case.fill",
                    title: "Health Log",
                    color: BabyPulseColors.health
                )

                // Date & Time with wheel picker for better selection


                FormDatePickerView(
                    title: "Date & Time",
                    date: $viewModel.timestamp,
                    useWheelPicker: true,
                    showTimeOnly: true
                )

                // Health Type Selection
                FormSectionView(title: "Health Entry Type") {
                    // Create a list of options for the segmented control
                    // let options = HealthEntry.HealthEntryType.allCases.map { type in
                    // SegmentOption(title: type.description, value: type)
                    // }

                    // Use CustomSegmentedControl for a modern UI
                    // CustomSegmentedControl(
                    // selection: Binding<HealthEntry.HealthEntryType>(
                    // get: { viewModel.healthEntryType },
                    // set: { viewModel.healthEntryType = $0 }
                    // ),
                    // options: options,
                    // color: BabyPulseColors.health
                    // )
                    // .padding(.vertical, 4)

                    // NEW: Vertical list of RadioSelectionButtons
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                        ForEach(HealthEntry.HealthEntryType.allCases, id: \.self) { type in
                            RadioSelectionButton(
                                title: type.description, // Assumes .description is user-friendly
                                isSelected: viewModel.healthEntryType == type,
                                color: BabyPulseColors.health, // Theme color for selection
                                action: {
                                    viewModel.healthEntryType = type
                                }
                            )
                        }
                    }
                    .padding(.vertical, 4) // Maintain some vertical padding if needed
                }

                // Dynamic content based on health entry type
                Group {
                    switch viewModel.healthEntryType {
                    case .temperature:
                        temperatureSection
                    case .medication:
                        medicationSection
                    case .symptom:
                        symptomSection
                    case .vaccination:
                        vaccinationSection
                    case .appointment:
                        appointmentSection
                    }
                }

                // Notes Section
                FormSectionView(title: "Notes") {
                    FormNotesView(notes: $viewModel.notes, showTitle: false)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle("Health")
        .navigationBarTitleDisplayMode(.inline)
    }

    private var temperatureSection: some View {
        FormSectionView(title: "Temperature Details") {
            // Temperature input
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                Text("Temperature")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.text)

                HStack {
                    TextField("Enter temperature", text: $viewModel.temperature)
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .stroke(viewModel.temperatureError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                        )

                    Picker("", selection: $viewModel.temperatureUnit) {
                        Text("°C").tag(HealthEntry.TemperatureUnit.celsius)
                        Text("°F").tag(HealthEntry.TemperatureUnit.fahrenheit)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .frame(width: 100)
                }

                if let error = viewModel.temperatureError {
                    Text(error)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.error)
                        .padding(.top, 4)
                }
            }
        }
    }

    private var medicationSection: some View {
        FormSectionView(title: "Medication Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Medication name input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Medication Name")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter medication name", text: $viewModel.medicationName)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .stroke(viewModel.medicationNameError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                        )

                    if let error = viewModel.medicationNameError {
                        Text(error)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.error)
                            .padding(.top, 4)
                    }
                }

                // Dosage input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Dosage")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter dosage (e.g., 5ml, 1 tablet)", text: $viewModel.medicationDosage)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                }
            }
        }
    }

    private var symptomSection: some View {
        FormSectionView(title: "Symptoms") {
            // Symptoms selection
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                Text("Select all that apply")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.text)

                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: BabyPulseLayout.spacingSM) {
                    ForEach(HealthEntry.Symptom.allCases, id: \.self) { symptom in
                        SelectionButton(
                            isSelected: viewModel.selectedSymptoms.contains(symptom),
                            color: BabyPulseColors.health,
                            action: {
                                if viewModel.selectedSymptoms.contains(symptom) {
                                    viewModel.selectedSymptoms.removeAll { $0 == symptom }
                                } else {
                                    viewModel.selectedSymptoms.append(symptom)
                                }
                            }
                        ) {
                            Text(symptom.description)
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(viewModel.selectedSymptoms.contains(symptom) ? BabyPulseColors.health : BabyPulseColors.textSecondary)
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .frame(maxWidth: .infinity)
                        }
                    }
                }
            }
        }
    }

    private var vaccinationSection: some View {
        FormSectionView(title: "Vaccination Details") {
            // Vaccine name input
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                Text("Vaccine Name")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.text)

                TextField("Enter vaccine name", text: $viewModel.vaccineName)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .stroke(viewModel.vaccineNameError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                    )

                if let error = viewModel.vaccineNameError {
                    Text(error)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.error)
                        .padding(.top, 4)
                }
            }
        }
    }

    private var appointmentSection: some View {
        FormSectionView(title: "Appointment Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Appointment reason input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Reason for Visit")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter reason for visit", text: $viewModel.appointmentReason)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .stroke(viewModel.appointmentReasonError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                        )

                    if let error = viewModel.appointmentReasonError {
                        Text(error)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.error)
                            .padding(.top, 4)
                    }
                }

                // Provider input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Provider/Doctor")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter provider name", text: $viewModel.appointmentProvider)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                }
            }
        }
    }
}

// Note: The supporting components (HealthTypeButton, SymptomToggleButton)
// have been replaced with reusable components from SelectionButton.swift:
// - CustomSegmentedControl
// - SelectionButton

#Preview {
    NavigationView {
        HealthEntryForm(viewModel: LogEntryViewModel(category: .health))
            .environmentObject(UserPreferencesManager.shared)
            .environment(\.unitSystem, .metric)
    }
}
