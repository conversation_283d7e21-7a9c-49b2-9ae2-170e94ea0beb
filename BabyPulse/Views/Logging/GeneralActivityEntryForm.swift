//
//  GeneralActivityEntryForm.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct GeneralActivityEntryForm: View {
    @ObservedObject var viewModel: LogEntryViewModel
    @Environment(\.colorScheme) private var colorScheme

    // Assuming BabyPulseColors.generalActivity is defined
    private var themeColor: Color { BabyPulseColors.generalActivity ?? .gray }

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header with icon
                FormHeaderView(
                    icon: LogCategory.general.icon, // Use icon from LogCategory
                    title: "Log Activity",
                    color: themeColor
                )

                // Activity Type Selector
                FormSectionView(title: "Activity Type") {
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                        ForEach(GeneralActivityType.allCases, id: \.self) { type in
                            RadioSelectionButton(
                                title: type.description,
                                isSelected: viewModel.generalActivityType == type,
                                color: themeColor,
                                action: {
                                    viewModel.generalActivityType = type
                                    // Clear custom name if not 'Other'
                                    if type != .other {
                                        viewModel.customGeneralActivityName = ""
                                    }
                                }
                            )
                        }
                    }
                    .padding(.vertical, 4)
                }

                // Custom Activity Name (if type is .other)
                if viewModel.generalActivityType == .other {
                    FormSectionView(title: "Custom Activity Name") {
                        TextField("Enter activity name", text: $viewModel.customGeneralActivityName)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                            )
                            // Add error handling if needed for this field
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
                
                // Date & Time
                FormDatePickerView(
                    title: "Date & Time",
                    date: $viewModel.timestamp,
                    useWheelPicker: true,
                    showTimeOnly: false // Defaulting to false, can be changed by user preference
                )

                // Duration (Optional)
                FormSectionView(title: "Duration (Optional)") {
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                        Text("Minutes: \(viewModel.generalActivityDurationMinutes)")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.text)
                        
                        Slider(value: Binding(
                            get: { Double(viewModel.generalActivityDurationMinutes) },
                            set: { viewModel.generalActivityDurationMinutes = Int($0) }
                        ), in: 0...120, step: 5) // 0 to 2 hours, in 5-min steps
                        .accentColor(themeColor)
                        .padding(.vertical, BabyPulseLayout.spacingXS)
                        
                        Button("Clear Duration") {
                            viewModel.generalActivityDurationMinutes = 0
                        }
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(themeColor)
                    }
                }

                // Notes Section
                FormSectionView(title: "Notes") {
                    FormNotesView(notes: $viewModel.notes, showTitle: false)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle(LogCategory.general.title)
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // Ensure a default activity type is set if not already
            if viewModel.generalActivityType == .other && viewModel.customGeneralActivityName.isEmpty {
                 // Potentially set a default if 'other' has no custom name yet from a previous edit
                 // Or, initialize generalActivityType to the first case if it's nil in the VM
            } 
        }
    }
}

#if DEBUG
struct GeneralActivityEntryForm_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            GeneralActivityEntryForm(viewModel: LogEntryViewModel(category: .general))
        }
    }
}
#endif 