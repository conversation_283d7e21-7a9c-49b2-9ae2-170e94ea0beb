//
//  DiaperEntryForm.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct DiaperEntryForm: View {
    @ObservedObject var viewModel: LogEntryViewModel
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Enhanced header with better visual hierarchy
                EnhancedFormHeaderView(
                    icon: "heart.fill",
                    title: "Diaper Change",
                    subtitle: "Record diaper change details",
                    color: BabyPulseColors.diaper
                )

                // Diaper Type Selector
                FormSectionView(title: "Type") {
                    // Modern segmented control with icons
                    CustomSegmentedControl(
                        selection: Binding<DiaperEntry.DiaperType>(
                            get: { viewModel.diaperType },
                            set: { viewModel.diaperType = $0 }
                        ),
                        options: [
                            SegmentOption(title: "Wet", icon: "drop.fill", value: DiaperEntry.DiaperType.wet),
                            SegmentOption(title: "Dirty", icon: "allergens", value: DiaperEntry.DiaperType.dirty),
                            SegmentOption(title: "Mixed", icon: "drop.fill.allergens", value: DiaperEntry.DiaperType.mixed)
                        ],
                        color: BabyPulseColors.diaper
                    )
                    .padding(.vertical, 4)
                }

                // Date & Time with wheel picker for better selection
                FormDatePickerView(
                    title: "When",
                    date: $viewModel.timestamp,
                    useWheelPicker: true,
                    showTimeOnly: true
                )

                // Poop Details (only for dirty or mixed diapers)
                if viewModel.diaperType == .dirty || viewModel.diaperType == .mixed {
                    poopDetailsSection
                }

                // Notes Section
                FormSectionView(title: "Notes") {
                    FormNotesView(notes: $viewModel.notes, showTitle: false)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle("Diaper")
        .navigationBarTitleDisplayMode(.inline)
    }

    private var poopDetailsSection: some View {
        FormSectionView(title: "Poop Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingLG) {
                // Color selection with improved grid layout
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                    HStack {
                        Text("Color")
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.text)

                        Spacer()

                        if viewModel.poopColor != .brown {
                            Text(viewModel.poopColor.description)
                                .font(BabyPulseTypography.caption())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .padding(.horizontal, BabyPulseLayout.paddingSM)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(BabyPulseColors.lightGray)
                                )
                        }
                    }

                    // Responsive color grid - adapts to screen size
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: BabyPulseLayout.spacingMD) {
                        ForEach(DiaperEntry.PoopColor.allCases, id: \.self) { color in
                            EnhancedPoopColorButton(
                                color: color,
                                isSelected: viewModel.poopColor == color,
                                action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        viewModel.poopColor = color
                                    }
                                }
                            )
                        }
                    }

                    // Informational cue for critical colors
                    if shouldShowCriticalColorInfo(for: viewModel.poopColor) {
                        criticalColorInfoView(for: viewModel.poopColor)
                            .padding(.top, BabyPulseLayout.spacingSM)
                    }
                }

                // Consistency selection with improved spacing
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                    Text("Consistency")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)

                    VStack(spacing: BabyPulseLayout.spacingSM) {
                        ForEach(DiaperEntry.PoopConsistency.allCases, id: \.self) { consistency in
                            EnhancedRadioButton(
                                title: consistency.description,
                                description: consistencyDescription(for: consistency),
                                isSelected: viewModel.poopConsistency == consistency,
                                color: BabyPulseColors.diaper,
                                action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        viewModel.poopConsistency = consistency
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
    }

    // Helper function to provide consistency descriptions
    private func consistencyDescription(for consistency: DiaperEntry.PoopConsistency) -> String {
        switch consistency {
        case .runny:
            return "Very watery"
        case .seedy:
            return "Seedy texture"
        case .pasty:
            return "Soft and pasty"
        case .formed:
            return "Well-formed"
        case .hard:
            return "Hard pellets"
        }
    }

    private func shouldShowCriticalColorInfo(for color: DiaperEntry.PoopColor) -> Bool {
        switch color {
        case .red, .white, .black:
            return true
        default:
            return false
        }
    }

    private func criticalColorInfoView(for color: DiaperEntry.PoopColor) -> some View {
        let message: String
        switch color {
        case .red:
            message = "Red in stool can sometimes indicate blood. If persistent or you are concerned, please consult your pediatrician."
        case .white:
            message = "White or clay-colored stool can indicate liver or gallbladder issues. Please consult your pediatrician if observed."
        case .black:
            message = "Black stool is normal for newborns (meconium). If it appears later or is tarry and not related to iron supplements, consult your pediatrician."
        default:
            message = ""
        }

        return Group {
            if !message.isEmpty {
                HStack(alignment: .top, spacing: BabyPulseLayout.spacingSM) {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(BabyPulseColors.warning)
                        .font(.title3)
                    Text(message)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
                .padding(BabyPulseLayout.paddingSM)
                .background(BabyPulseColors.warning.opacity(0.1))
                .cornerRadius(BabyPulseLayout.cornerRadiusMD)
                .transition(.opacity.combined(with: .scale(scale: 0.9)))
            } else {
                EmptyView()
            }
        }
    }
}

// MARK: - Supporting Components

// Note: Most components have been replaced with reusable components:
// - DiaperTypeButton -> CustomSegmentedControl
// - ConsistencyButton -> EnhancedRadioButton
// - PoopColorButton -> EnhancedPoopColorButton

// MARK: - Enhanced Components

struct EnhancedPoopColorButton: View {
    let color: DiaperEntry.PoopColor
    let isSelected: Bool
    let action: () -> Void
    @State private var isPressed = false

    var displayColor: Color {
        switch color {
        case .yellow:
            return Color.yellow
        case .green:
            return Color.green
        case .brown:
            return Color.brown
        case .black:
            return Color.black
        case .red:
            return Color.red
        case .white:
            return Color.white
        }
    }

    var accessibleColorName: String {
        switch color {
        case .yellow:
            return "Yellow"
        case .green:
            return "Green"
        case .brown:
            return "Brown"
        case .black:
            return "Black"
        case .red:
            return "Red"
        case .white:
            return "White"
        }
    }

    var body: some View {
        Button(action: action) {
            VStack(spacing: BabyPulseLayout.spacingXS) {
                ZStack {
                    // Background circle
                    Circle()
                        .fill(displayColor)
                        .frame(width: 50, height: 50)
                        .overlay(
                            Circle()
                                .stroke(
                                    color == .white ? Color.gray : Color.clear,
                                    lineWidth: color == .white ? 1 : 0
                                )
                        )

                    // Selection indicator
                    if isSelected {
                        Circle()
                            .stroke(BabyPulseColors.diaper, lineWidth: 3)
                            .frame(width: 58, height: 58)

                        Circle()
                            .fill(BabyPulseColors.diaper)
                            .frame(width: 20, height: 20)
                            .overlay(
                                Image(systemName: "checkmark")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            )
                            .offset(x: 20, y: -20)
                    }
                }
                .scaleEffect(isPressed ? 0.9 : 1.0)

                // Color name
                Text(accessibleColorName)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(isSelected ? BabyPulseColors.diaper : BabyPulseColors.textSecondary)
                    .fontWeight(isSelected ? .semibold : .regular)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, BabyPulseLayout.paddingSM)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(isSelected ? BabyPulseColors.diaper.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .stroke(isSelected ? BabyPulseColors.diaper.opacity(0.3) : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .accessibilityLabel("Select \(accessibleColorName.lowercased()) color")
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }
}

struct EnhancedRadioButton: View {
    let title: String
    let description: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: BabyPulseLayout.spacingMD) {
                // Custom radio button
                ZStack {
                    Circle()
                        .fill(isSelected ? color : Color.clear)
                        .frame(width: 20, height: 20)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? color : BabyPulseColors.mediumGray, lineWidth: 2)
                        )

                    if isSelected {
                        Circle()
                            .fill(.white)
                            .frame(width: 8, height: 8)
                    }
                }

                // Text content
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)
                        .fontWeight(isSelected ? .semibold : .regular)

                    Text(description)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(color)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(isSelected ? color.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .stroke(isSelected ? color.opacity(0.3) : BabyPulseColors.lightGray, lineWidth: 1)
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .accessibilityLabel("\(title): \(description)")
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }
}

// MARK: - Enhanced Form Components

#Preview {
    NavigationView {
        DiaperEntryForm(viewModel: LogEntryViewModel(category: .diaper))
    }
}
