//
//  FeedingEntryForm.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct FeedingEntryForm: View {
    @ObservedObject var viewModel: LogEntryViewModel
    @Environment(\.colorScheme) private var colorScheme
    @ObservedObject private var preferencesManager = UserPreferencesManager.shared

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Enhanced header with better visual hierarchy
                EnhancedFormHeaderView(
                    icon: "cup.and.saucer.fill",
                    title: "Feeding Entry",
                    subtitle: "Track your baby's feeding details",
                    color: BabyPulseColors.feeding
                )

                // Feeding Type Selector with improved design
                FormSectionView(title: "Type") {
                    // Modern segmented control with icons
                    CustomSegmentedControl(
                        selection: Binding<FeedingEntry.FeedingType>(
                            get: { viewModel.feedingType },
                            set: { viewModel.feedingType = $0 }
                        ),
                        options: [
                            SegmentOption(title: "Breast", icon: "figure.dress", value: FeedingEntry.FeedingType.breastfeeding),
                            SegmentOption(title: "Bottle", icon: "cup.and.saucer.fill", value: FeedingEntry.FeedingType.bottleFeeding),
                            SegmentOption(title: "Solid", icon: "fork.knife", value: FeedingEntry.FeedingType.solidFood)
                        ],
                        color: BabyPulseColors.feeding
                    )
                    .padding(.vertical, 4)
                }

                // Date & Time with wheel picker for better selection
                FormDatePickerView(
                    title: "When",
                    date: $viewModel.timestamp,
                    useWheelPicker: true,
                    showTimeOnly: true
                )

                // Dynamic content based on feeding type
                Group {
                    switch viewModel.feedingType {
                    case .breastfeeding:
                        breastfeedingSection
                    case .bottleFeeding:
                        bottleFeedingSection
                    case .solidFood:
                        solidFoodSection
                    }
                }

                // Notes Section
                FormSectionView(title: "Notes") {
                    FormNotesView(notes: $viewModel.notes, showTitle: false)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle("Feeding")
        .navigationBarTitleDisplayMode(.inline)
    }

    private var breastfeedingSection: some View {
        FormSectionView(title: "Breastfeeding Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Duration Slider
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Duration: \(viewModel.feedingDuration) min")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    Slider(value: Binding(
                        get: { Double(viewModel.feedingDuration) },
                        set: { viewModel.feedingDuration = Int($0) }
                    ), in: 1...60, step: 1)
                    .accentColor(BabyPulseColors.feeding)
                    .padding(.vertical, BabyPulseLayout.spacingXS)
                }

                // Breast selection
                HStack(spacing: BabyPulseLayout.spacingMD) {
                    RadioSelectionButton(
                        title: "Left Breast",
                        isSelected: viewModel.leftBreast,
                        color: BabyPulseColors.feeding,
                        action: { viewModel.leftBreast.toggle() }
                    )

                    RadioSelectionButton(
                        title: "Right Breast",
                        isSelected: viewModel.rightBreast,
                        color: BabyPulseColors.feeding,
                        action: { viewModel.rightBreast.toggle() }
                    )
                }
            }
        }
    }

    private var bottleFeedingSection: some View {
        FormSectionView(title: "Bottle Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Volume input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Volume")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    HStack {
                        TextField("Enter volume", text: $viewModel.feedingVolume)
                            .keyboardType(.decimalPad)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .stroke(viewModel.feedingVolumeError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                            )

                        Text(preferencesManager.unitSystem == .metric ? "ml" : "oz")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .padding(.leading, BabyPulseLayout.spacingSM)
                    }

                    if let error = viewModel.feedingVolumeError {
                        Text(error)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.error)
                            .padding(.top, 4)
                    }
                }

                // Content selection
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Content")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    // Modern segmented control with icons
                    CustomSegmentedControl(
                        selection: Binding<FeedingEntry.BottleContent>(
                            get: { viewModel.bottleContent },
                            set: { viewModel.bottleContent = $0 }
                        ),
                        options: [
                            SegmentOption(title: "Formula", icon: "drop.triangle.fill", value: FeedingEntry.BottleContent.formula),
                            SegmentOption(title: "Breast Milk", icon: "figure.dress", value: FeedingEntry.BottleContent.expressedBreastMilk),
                            SegmentOption(title: "Mixed", icon: "arrow.triangle.merge", value: FeedingEntry.BottleContent.mixed)
                        ],
                        color: BabyPulseColors.feeding
                    )
                    .padding(.vertical, 4)
                }
            }
        }
    }

    private var solidFoodSection: some View {
        FormSectionView(title: "Solid Food Details") {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Food item input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Food Item")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter food item", text: $viewModel.foodItem)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .stroke(viewModel.foodItemError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                        )

                    if let error = viewModel.foodItemError {
                        Text(error)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.error)
                            .padding(.top, 4)
                    }
                }

                // Amount input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Amount (optional)")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter amount", text: $viewModel.foodAmount)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                }

                // Reaction input
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("Reaction (optional)")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)

                    TextField("Enter reaction", text: $viewModel.reaction)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                }
            }
        }
    }
}

// Note: The supporting components (FeedingTypeButton, BreastToggleButton, BottleContentButton)
// have been replaced with reusable components from SelectionButton.swift:
// - IconSelectionButton
// - RadioSelectionButton
// - SelectionButton

#Preview {
    NavigationView {
        FeedingEntryForm(viewModel: LogEntryViewModel(category: .feeding))
    }
}


