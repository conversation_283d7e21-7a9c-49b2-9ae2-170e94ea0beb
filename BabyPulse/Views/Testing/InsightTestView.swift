//
//  InsightTestView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

#if DEBUG
struct InsightTestView: View {
        @Environment(\.modelContext) private var modelContext
        @Query private var babies: [Baby]

        @State private var isGenerating = false
        @State private var generatedInsights: [Insight] = []
        @State private var message = ""

        var body: some View {
            NavigationView {
                List {
                    Section(header: Text("Test Controls")) {
                        <PERSON><PERSON>("Generate Test Data") {
                            generateTestData()
                        }
                        .disabled(isGenerating)

                        <PERSON><PERSON>("Generate Growth & Health Insights") {
                            generateGrowthAndHealthInsights()
                        }
                        .disabled(isGenerating || babies.isEmpty)

                        <PERSON><PERSON>("Clear All Data") {
                            clearAllData()
                        }
                        .disabled(isGenerating)
                    }

                    Section(header: Text("Status")) {
                        Text(message)
                            .foregroundColor(message.contains("Error") ? .red : .primary)
                    }

                    Section(header: Text("Generated Insights (\(generatedInsights.count))")) {
                        if generatedInsights.isEmpty {
                            Text("No insights generated yet")
                                .foregroundColor(.secondary)
                        } else {
                            ForEach(generatedInsights) { insight in
                                VStack(alignment: .leading) {
                                    HStack {
                                        Circle()
                                            .fill(categoryColor(for: insight.category))
                                            .frame(width: 12, height: 12)

                                        Text(insight.title)
                                            .font(.headline)
                                    }

                                    Text(insight.metric)
                                        .font(.subheadline)

                                    Text(insight.insightContent)
                                        .font(.caption)
                                        .lineLimit(3)
                                }
                                .padding(.vertical, 4)
                            }
                        }
                    }
                }
                .navigationTitle("Insight Testing")
                .overlay {
                    if isGenerating {
                        ProgressView("Generating insights...")
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(10)
                            .shadow(radius: 10)
                    }
                }
            }
        }

        private func generateTestData() {
            isGenerating = true
            message = "Generating test data..."

            // Reset app data first
            TestingUtilities.resetApp(modelContext: modelContext)

            // Create a test baby
            let baby = Baby(
                name: "Test Baby",
                birthDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
                gender: .male
            )

            // Insert into model context
            modelContext.insert(baby)

            // Generate test log entries
            TestingUtilities.generateTestLogEntries(for: baby, modelContext: modelContext)

            isGenerating = false
            message = "Test data generated successfully"
        }

        private func generateGrowthAndHealthInsights() {
            guard let baby = babies.first else {
                message = "Error: No baby found"
                return
            }

            isGenerating = true
            message = "Generating growth and health insights..."

            TestingUtilities.testGrowthAndHealthInsightGeneration(for: baby, modelContext: modelContext) { insights in
                DispatchQueue.main.async {
                    self.generatedInsights = insights
                    self.isGenerating = false

                    if insights.isEmpty {
                        self.message = "No growth or health insights were generated"
                    } else {
                        self.message = "Generated \(insights.count) growth and health insights"
                    }
                }
            }
        }

        private func clearAllData() {
            isGenerating = true
            message = "Clearing all data..."

            TestingUtilities.resetApp(modelContext: modelContext)

            generatedInsights = []
            isGenerating = false
            message = "All data cleared successfully"
        }

        private func categoryColor(for category: Insight.InsightCategory) -> Color {
            switch category {
            case .feeding:
                return .blue
            case .sleep:
                return .indigo
            case .diaper:
                return .green
            case .growth:
                return .orange
            case .health:
                return .red
            case .development:
                return .purple
            }
        }
}

#Preview {
    InsightTestView()
        .modelContainer(for: [Baby.self, Insight.self], inMemory: true)
}
#endif
