//
//  SimpleInsightTestView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

#if DEBUG
struct SimpleInsightTestView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var message = "Ready to test"
    @State private var isLoading = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Growth & Health Insight Testing")
                .font(.title)
                .padding()
            
            Text(message)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            
            <PERSON><PERSON>("Test Template Loading") {
                testTemplateLoading()
            }
            .buttonStyle(.borderedProminent)
            
            <PERSON><PERSON>("Generate Test Data") {
                generateTestData()
            }
            .buttonStyle(.bordered)
            
            But<PERSON>("Generate Insights") {
                generateInsights()
            }
            .buttonStyle(.bordered)
            
            Spacer()
        }
        .padding()
        .disabled(isLoading)
        .overlay {
            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .background(Color.white.opacity(0.8))
                    .cornerRadius(10)
                    .frame(width: 100, height: 100)
            }
        }
    }
    
    private func testTemplateLoading() {
        isLoading = true
        message = "Testing template loading..."
        
        // Test if the templates are loaded correctly
        let templateManager = PromptTemplateManager.shared
        
        // Try to get templates for growth and health insights
        let weightGainTemplate = templateManager.getTemplate(named: "weight_gain")
        let heightIncreaseTemplate = templateManager.getTemplate(named: "height_increase")
        let headCircumferenceTemplate = templateManager.getTemplate(named: "head_circumference_increase")
        let temperatureTemplate = templateManager.getTemplate(named: "temperature_fluctuation")
        let symptomTemplate = templateManager.getTemplate(named: "symptom_pattern")
        
        // Check if templates were found
        var foundTemplates: [String] = []
        var missingTemplates: [String] = []
        
        if weightGainTemplate != nil { foundTemplates.append("weight_gain") } else { missingTemplates.append("weight_gain") }
        if heightIncreaseTemplate != nil { foundTemplates.append("height_increase") } else { missingTemplates.append("height_increase") }
        if headCircumferenceTemplate != nil { foundTemplates.append("head_circumference_increase") } else { missingTemplates.append("head_circumference_increase") }
        if temperatureTemplate != nil { foundTemplates.append("temperature_fluctuation") } else { missingTemplates.append("temperature_fluctuation") }
        if symptomTemplate != nil { foundTemplates.append("symptom_pattern") } else { missingTemplates.append("symptom_pattern") }
        
        // Update message
        if missingTemplates.isEmpty {
            message = "All templates loaded successfully: \(foundTemplates.joined(separator: ", "))"
        } else {
            message = "Missing templates: \(missingTemplates.joined(separator: ", "))\nFound templates: \(foundTemplates.joined(separator: ", "))"
        }
        
        isLoading = false
    }
    
    private func generateTestData() {
        isLoading = true
        message = "Generating test data..."
        
        // Reset app data first
        TestingUtilities.resetApp(modelContext: modelContext)
        
        // Create a test baby
        let baby = Baby(
            name: "Test Baby",
            birthDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
            gender: .male
        )
        
        // Insert into model context
        modelContext.insert(baby)
        
        // Generate test growth and health entries
        TestingUtilities.generateTestGrowthEntries(for: baby, modelContext: modelContext)
        TestingUtilities.generateTestHealthEntries(for: baby, modelContext: modelContext)
        
        isLoading = false
        message = "Test data generated successfully"
    }
    
    private func generateInsights() {
        isLoading = true
        message = "Generating insights..."
        
        // Get the first baby
        let descriptor = FetchDescriptor<Baby>()
        guard let babies = try? modelContext.fetch(descriptor), let baby = babies.first else {
            isLoading = false
            message = "Error: No baby found. Generate test data first."
            return
        }
        
        // Generate insights
        let insightService = EnhancedInsightService(modelContext: modelContext)
        
        insightService.generateInsights(for: baby) { insights in
            // Filter for growth and health insights
            let growthInsights = insights.filter { $0.category == .growth }
            let healthInsights = insights.filter { $0.category == .health }
            
            DispatchQueue.main.async {
                self.isLoading = false
                
                if insights.isEmpty {
                    self.message = "No insights were generated"
                } else {
                    self.message = "Generated \(insights.count) total insights:\n- \(growthInsights.count) growth insights\n- \(healthInsights.count) health insights"
                    
                    // Log details of growth and health insights
                    if !growthInsights.isEmpty || !healthInsights.isEmpty {
                        var detailMessage = "\n\nInsight details:"
                        
                        for insight in growthInsights {
                            detailMessage += "\n- Growth: \(insight.title)"
                        }
                        
                        for insight in healthInsights {
                            detailMessage += "\n- Health: \(insight.title)"
                        }
                        
                        self.message += detailMessage
                    }
                }
            }
        }
    }
}

#Preview {
    SimpleInsightTestView()
        .modelContainer(for: [Baby.self, Insight.self], inMemory: true)
}
#endif
