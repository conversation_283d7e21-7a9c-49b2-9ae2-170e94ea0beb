//
//  SleepDetailsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

struct SleepDetailsView: View {
    let entry: SleepEntry
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @State private var showDeleteConfirmation = false
    @State private var showEditSheet = false

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header
                HStack {
                    ZStack {
                        Circle()
                            .fill(BabyPulseColors.sleep.opacity(0.2))
                            .frame(width: 60, height: 60)

                        Image(systemName: "moon.fill")
                            .font(.system(size: 30))
                            .foregroundColor(BabyPulseColors.sleep)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(entry.endTime == nil ? "Ongoing Sleep" : "Sleep")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.secondary)

                        Text(formattedDate)
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    Spacer()
                }

                // Details card
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Details")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    // Start time
                    HStack {
                        Text("Start Time")
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Spacer()

                        Text(formattedTime(entry.timestamp))
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.sleep)
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .fill(BabyPulseColors.sleep.opacity(0.1))
                    )

                    // End time (if completed)
                    if let endTime = entry.endTime {
                        HStack {
                            Text("End Time")
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)

                            Spacer()

                            Text(formattedTime(endTime))
                                .font(BabyPulseTypography.bodyBold())
                                .foregroundColor(BabyPulseColors.sleep)
                        }
                        .padding(BabyPulseLayout.paddingMD)
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(BabyPulseColors.sleep.opacity(0.1))
                        )
                    }

                    // Duration
                    HStack {
                        Text("Duration")
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Spacer()

                        Text(formattedDuration)
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.sleep)
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .fill(BabyPulseColors.sleep.opacity(0.1))
                    )

                    // Location (if any)
                    if let location = entry.location {
                        HStack {
                            Text("Location")
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)

                            Spacer()

                            Text(location.description)
                                .font(BabyPulseTypography.bodyBold())
                                .foregroundColor(BabyPulseColors.sleep)
                        }
                        .padding(BabyPulseLayout.paddingMD)
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(BabyPulseColors.sleep.opacity(0.1))
                        )
                    }
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                )

                // Notes card (if any)
                if let notes = entry.notes, !notes.isEmpty {
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                        Text("Notes")
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Text(notes)
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.text)
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }

                Spacer()

                // Edit button
                Button(action: {
                    showEditSheet = true
                }) {
                    HStack {
                        Image(systemName: "pencil")
                            .font(.system(size: 16))

                        Text("Edit Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(BabyPulseColors.sleep)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(BabyPulseColors.sleep, lineWidth: 1)
                    )
                }
                .padding(.bottom, BabyPulseLayout.spacingMD)

                // Delete button
                Button(action: {
                    showDeleteConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "trash")
                            .font(.system(size: 16))

                        Text("Delete Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(.red)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(Color.red, lineWidth: 1)
                    )
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .navigationTitle("Sleep Details")
        .navigationBarTitleDisplayMode(.inline)
        .background(colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
        .alert("Delete Entry", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteEntry()
            }
        } message: {
            Text("Are you sure you want to delete this sleep entry? This action cannot be undone.")
        }
        .sheet(isPresented: $showEditSheet) {
            // Refresh the view when the sheet is dismissed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // This is a workaround to refresh the view after editing
                // In a real app, you might want to use a more robust solution
                let impact = UIImpactFeedbackGenerator(style: .light)
                impact.impactOccurred()
            }
        } content: {
            LogEntryView(sleepEntry: entry)
        }
    }

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        return formatter.string(from: entry.timestamp)
    }

    private func formattedTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        return formatter.string(from: date)
    }

    private var formattedDuration: String {
        if let endTime = entry.endTime {
            let duration = Int(endTime.timeIntervalSince(entry.timestamp) / 60)
            if duration < 60 {
                return "\(duration) min"
            } else {
                let hours = duration / 60
                let minutes = duration % 60
                return "\(hours) hr \(minutes) min"
            }
        } else {
            let now = Date()
            let duration = Int(now.timeIntervalSince(entry.timestamp) / 60)
            if duration < 60 {
                return "\(duration) min (ongoing)"
            } else {
                let hours = duration / 60
                let minutes = duration % 60
                return "\(hours) hr \(minutes) min (ongoing)"
            }
        }
    }

    private func deleteEntry() {
        modelContext.delete(entry)
        try? modelContext.save()
        dismiss()
    }
}

#Preview {
    NavigationView {
        SleepDetailsView(
            entry: SleepEntry.createCompletedSleep(
                startTime: Date().addingTimeInterval(-7200),
                endTime: Date(),
                location: .crib,
                notes: "Slept well after feeding."
            )
        )
    }
    .modelContainer(for: [SleepEntry.self], inMemory: true)
}
