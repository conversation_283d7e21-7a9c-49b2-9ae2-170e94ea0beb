//
//  GrowthDetailsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct GrowthDetailsView: View {
    let entry: GrowthEntry
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.unitSystem) private var unitSystem
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userPreferencesManager: UserPreferencesManager
    @State private var showDeleteConfirmation = false
    @State private var showEditSheet = false

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: entry.timestamp)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header
                HStack {
                    ZStack {
                        Circle()
                            .fill(BabyPulseColors.growth.opacity(0.2))
                            .frame(width: 60, height: 60)

                        Image(systemName: "ruler.fill")
                            .font(.system(size: 30))
                            .foregroundColor(BabyPulseColors.growth)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Growth Measurement")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.secondary)

                        Text(formattedDate)
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    Spacer()
                }

                // Measurements card
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Measurements")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    // Weight
                    if let weight = entry.weight {
                        measurementRow(
                            label: "Weight",
                            value: UnitConverter.formatWeight(weight, unitSystem: unitSystem),
                            percentile: entry.weightPercentile
                        )
                    }

                    // Height
                    if let height = entry.height {
                        measurementRow(
                            label: "Height",
                            value: UnitConverter.formatHeight(height, unitSystem: unitSystem),
                            percentile: entry.heightPercentile
                        )
                    }

                    // Head Circumference
                    if let headCircumference = entry.headCircumference {
                        measurementRow(
                            label: "Head Circumference",
                            value: UnitConverter.formatHeadCircumference(headCircumference, unitSystem: unitSystem),
                            percentile: entry.headCircumferencePercentile
                        )
                    }
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                )

                // Notes
                if let notes = entry.notes, !notes.isEmpty {
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                        Text("Notes")
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Text(notes)
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.text)
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }

                Spacer()

                // Edit button
                Button(action: {
                    showEditSheet = true
                }) {
                    HStack {
                        Image(systemName: "pencil")
                            .font(.system(size: 16))

                        Text("Edit Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(BabyPulseColors.growth)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(BabyPulseColors.growth, lineWidth: 1)
                    )
                }
                .padding(.bottom, BabyPulseLayout.spacingMD)

                // Delete button
                Button(action: {
                    showDeleteConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "trash")
                            .font(.system(size: 16))

                        Text("Delete Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(.red)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(Color.red, lineWidth: 1)
                    )
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle("Growth Details")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // Calculate percentiles when the view appears
            entry.calculatePercentiles()
        }
        .alert("Delete Entry", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteEntry()
            }
        } message: {
            Text("Are you sure you want to delete this growth entry? This action cannot be undone.")
        }
        .sheet(isPresented: $showEditSheet) {
            // Refresh the view when the sheet is dismissed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // This is a workaround to refresh the view after editing
                // In a real app, you might want to use a more robust solution
                let impact = UIImpactFeedbackGenerator(style: .light)
                impact.impactOccurred()
            }
        } content: {
            LogEntryView(growthEntry: entry)
        }
    }

    private func measurementRow(label: String, value: String, percentile: Double?) -> some View {
        HStack {
            Text(label)
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.textSecondary)

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text(value)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.growth)

                if let percentile = percentile {
                    Text(String(format: "%.0f percentile", percentile))
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
            }
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(BabyPulseColors.growth.opacity(0.1))
        )
    }

    private func deleteEntry() {
        modelContext.delete(entry)
        try? modelContext.save()
        dismiss()
    }
}

#Preview {
    NavigationView {
        GrowthDetailsView(entry: GrowthEntry(
            timestamp: Date(),
            weight: 5.2,
            height: 60.5,
            headCircumference: 38.2,
            notes: "Regular checkup with pediatrician. Growth is on track."
        ))
        .environmentObject(UserPreferencesManager.shared)
        .environment(\.unitSystem, .metric)
    }
}
