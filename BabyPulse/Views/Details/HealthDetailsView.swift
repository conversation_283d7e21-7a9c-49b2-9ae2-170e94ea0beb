//
//  HealthDetailsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct HealthDetailsView: View {
    let entry: HealthEntry
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.unitSystem) private var unitSystem
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userPreferencesManager: UserPreferencesManager
    @State private var showDeleteConfirmation = false
    @State private var showEditSheet = false

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: entry.timestamp)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header
                HStack {
                    ZStack {
                        Circle()
                            .fill(BabyPulseColors.health.opacity(0.2))
                            .frame(width: 60, height: 60)

                        Image(systemName: "cross.case.fill")
                            .font(.system(size: 30))
                            .foregroundColor(BabyPulseColors.health)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(entry.type.description)
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.secondary)

                        Text(formattedDate)
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    Spacer()
                }

                // Details card
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Details")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    // Type-specific details
                    switch entry.type {
                    case .temperature:
                        temperatureDetails
                    case .medication:
                        medicationDetails
                    case .symptom:
                        symptomDetails
                    case .vaccination:
                        vaccinationDetails
                    case .appointment:
                        appointmentDetails
                    }
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                )

                // Notes
                if let notes = entry.notes, !notes.isEmpty {
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                        Text("Notes")
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Text(notes)
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.text)
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }

                Spacer()

                // Edit button
                Button(action: {
                    showEditSheet = true
                }) {
                    HStack {
                        Image(systemName: "pencil")
                            .font(.system(size: 16))

                        Text("Edit Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(BabyPulseColors.health)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(BabyPulseColors.health, lineWidth: 1)
                    )
                }
                .padding(.bottom, BabyPulseLayout.spacingMD)

                // Delete button
                Button(action: {
                    showDeleteConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "trash")
                            .font(.system(size: 16))

                        Text("Delete Entry")
                            .font(BabyPulseTypography.body())
                    }
                    .foregroundColor(.red)
                    .padding(BabyPulseLayout.paddingMD)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(Color.red, lineWidth: 1)
                    )
                }
            }
            .padding(BabyPulseLayout.paddingMD)
        }
        .background(colorScheme == .dark ? Color(hex: "000000") : Color(hex: "F5F5F7"))
        .navigationTitle("Health Details")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Delete Entry", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteEntry()
            }
        } message: {
            Text("Are you sure you want to delete this health entry? This action cannot be undone.")
        }
        .sheet(isPresented: $showEditSheet) {
            // Refresh the view when the sheet is dismissed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // This is a workaround to refresh the view after editing
                // In a real app, you might want to use a more robust solution
                let impact = UIImpactFeedbackGenerator(style: .light)
                impact.impactOccurred()
            }
        } content: {
            LogEntryView(healthEntry: entry)
        }
    }

    private var temperatureDetails: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            if let temperature = entry.temperature, let unit = entry.temperatureUnit {
                HStack {
                    Text("Temperature")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(UnitConverter.formatTemperature(temperature, unit: unit, convertTo: unitSystem))
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.health)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(BabyPulseColors.health.opacity(0.1))
                )

                // Add fever indicator if temperature is high
                // Convert to celsius for consistent fever detection
                let tempInCelsius = unit == .celsius ? temperature : UnitConverter.fahrenheitToCelsius(temperature)
                if tempInCelsius >= 38.0 {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(BabyPulseColors.warning)

                        Text("Fever detected")
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.warning)
                    }
                    .padding(BabyPulseLayout.paddingSM)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .fill(BabyPulseColors.warning.opacity(0.1))
                    )
                }
            }
        }
    }

    private var medicationDetails: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            if let medicationName = entry.medicationName {
                HStack {
                    Text("Medication")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(medicationName)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.health)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(BabyPulseColors.health.opacity(0.1))
                )
            }

            if let dosage = entry.medicationDosage {
                HStack {
                    Text("Dosage")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(dosage)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                )
            }
        }
    }

    private var symptomDetails: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            Text("Symptoms")
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.textSecondary)

            LazyVGrid(columns: [GridItem(.flexible())], spacing: BabyPulseLayout.spacingSM) {
                ForEach(entry.getSymptoms(), id: \.self) { symptom in
                    HStack {
                        Image(systemName: "circle.fill")
                            .font(.system(size: 8))
                            .foregroundColor(BabyPulseColors.health)

                        Text(symptom.description)
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.text)

                        Spacer()
                    }
                    .padding(BabyPulseLayout.paddingSM)
                }
            }
            .padding(BabyPulseLayout.paddingSM)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
            )
        }
    }

    private var vaccinationDetails: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            if let vaccineName = entry.vaccineName {
                HStack {
                    Text("Vaccine")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(vaccineName)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.health)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(BabyPulseColors.health.opacity(0.1))
                )
            }
        }
    }

    private var appointmentDetails: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            if let reason = entry.appointmentReason {
                HStack {
                    Text("Reason")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(reason)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.health)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(BabyPulseColors.health.opacity(0.1))
                )
            }

            if let provider = entry.appointmentProvider {
                HStack {
                    Text("Provider")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Text(provider)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                )
            }
        }
    }

    private func deleteEntry() {
        modelContext.delete(entry)
        try? modelContext.save()
        dismiss()
    }
}

#Preview {
    NavigationView {
        HealthDetailsView(entry: HealthEntry(
            timestamp: Date(),
            type: .temperature,
            notes: "Fever started in the evening. Gave Tylenol at 8pm."
        ))
        .environmentObject(UserPreferencesManager.shared)
        .environment(\.unitSystem, .metric)
    }
}
