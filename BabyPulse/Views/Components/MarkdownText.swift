import SwiftUI
import MarkdownView

/// A custom view for rendering markdown text with enhanced formatting capabilities
/// Uses MarkdownView package for robust parsing and rendering
struct MarkdownText: View {
    let content: String
    var lineSpacing: CGFloat = 6
    var fontSize: CGFloat = 16

    @Environment(\.colorScheme) private var colorScheme
    @State private var viewHeight: CGFloat = 0

    var body: some View {
        // Use MarkdownView to render the content directly without preprocessing
        MarkdownViewWrapper(
            markdown: content,
            lineSpacing: lineSpacing,
            fontSize: fontSize,
            viewHeight: $viewHeight
        )
        .frame(height: viewHeight)
        .fixedSize(horizontal: false, vertical: true) // Allow view to expand vertically
    }
}


/// A wrapper for MarkdownView to use in SwiftUI
struct MarkdownViewWrapper: UIViewRepresentable {
    let markdown: String
    let lineSpacing: CGFloat
    let fontSize: CGFloat
    @Binding var viewHeight: CGFloat

    func makeUIView(context: Context) -> MarkdownView {
        let markdownView = MarkdownView()

        // Configure the markdown view
        markdownView.isScrollEnabled = false

        // Set custom CSS to match our styling requirements
        let customCSS = """
        body {
            font-size: \(fontSize)px;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }
        p {
            margin-bottom: \(lineSpacing)px;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: \(lineSpacing * 2)px;
            margin-bottom: \(lineSpacing)px;
        }
        ul, ol {
            margin-bottom: \(lineSpacing)px;
            padding-left: 20px;
        }
        li {
            margin-bottom: \(lineSpacing / 2)px;
        }
        code {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 4px;
        }
        pre {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 8px;
            border-radius: 8px;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid rgba(0, 0, 0, 0.1);
            padding-left: 8px;
            margin-left: 0;
        }
        """

        // Set up callbacks
        markdownView.onRendered = { [weak markdownView] height in
            self.viewHeight = height
        }

        markdownView.onTouchLink = { request in
            guard let url = request.url else { return false }

            if url.scheme == "file" {
                return false
            } else if url.scheme == "https" || url.scheme == "http" {
                UIApplication.shared.open(url)
                return false
            } else {
                return false
            }
        }

        // Load the markdown content with custom CSS
        markdownView.load(markdown: markdown, css: customCSS)

        return markdownView
    }

    func updateUIView(_ uiView: MarkdownView, context: Context) {
        // Update the markdown content if it changes
        uiView.load(markdown: markdown)
    }
}

#Preview {
    VStack {
        MarkdownText(content: """
        # Heading 1
        ## Heading 2
        ### Heading 3

        **Soothing Techniques:**
        - Rock gently
        - Use white noise
        - Swaddle tightly

        **Feeding Tips:**
        1. Ensure proper latch
        2. Burp frequently

        *Note:* Always consult your pediatrician if crying persists.

        ```
        Code block example
        ```

        > Blockquote example

        ---

        [Link example](https://example.com)
        """)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .padding()
    }
}
