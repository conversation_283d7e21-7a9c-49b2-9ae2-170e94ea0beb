import SwiftUI

struct PremiumFeaturePromptView: View {
    let featureTitle: String
    let featureDescription: String
    let featureIcon: String
    
    @State private var showingSubscriptionView = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Header
                VStack(spacing: BabyPulseLayout.spacingMD) {
                    Image(systemName: featureIcon)
                        .font(.system(size: 60))
                        .foregroundColor(BabyPulseColors.primary)
                        .padding(.top, BabyPulseLayout.spacingXL)
                    
                    Text(featureTitle)
                        .font(BabyPulseTypography.title1())
                        .foregroundColor(BabyPulseColors.secondary)
                        .multilineTextAlignment(.center)
                    
                    Text(featureDescription)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, BabyPulseLayout.paddingLG)
                }
                
                // Premium features
                VStack(spacing: BabyPulseLayout.spacingMD) {
                    Text("Unlock Premium Features")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)
                    
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                        PremiumFeatureRow(icon: "arrow.triangle.2.circlepath", title: "Cloud Sync", description: "Sync your data across all your devices")
                        PremiumFeatureRow(icon: "chart.bar.fill", title: "Advanced Analytics", description: "Get detailed insights and trends")
                        PremiumFeatureRow(icon: "person.2.fill", title: "Multiple Babies", description: "Track unlimited baby profiles")
                        PremiumFeatureRow(icon: "square.and.arrow.up.fill", title: "Data Export", description: "Export your data in various formats")
                        PremiumFeatureRow(icon: "xmark.octagon.fill", title: "Ad-Free Experience", description: "Enjoy the app without advertisements")
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                }
                
                Spacer()
                
                // Action buttons
                VStack(spacing: BabyPulseLayout.spacingMD) {
                    Button {
                        self.showingSubscriptionView = true
                    } label: {
                        HStack {
                            Image(systemName: "crown.fill")
                                .foregroundColor(.yellow)
                            
                            Text("Upgrade to Premium")
                                .font(BabyPulseTypography.bodyBold())
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(BabyPulseColors.primary)
                        .foregroundColor(.white)
                        .cornerRadius(BabyPulseLayout.cornerRadiusMD)
                    }
                    
                    Button {
                        dismiss()
                    } label: {
                        Text("Maybe Later")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.secondary)
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.bottom, BabyPulseLayout.paddingLG)
            }
            .navigationTitle("Premium Feature")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark")
                            .foregroundColor(BabyPulseColors.secondary)
                    }
                }
            }
            .navigationDestination(isPresented: $showingSubscriptionView) {
                SubscriptionView()
            }
        }
    }
}

struct PremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(BabyPulseColors.primary)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.secondary)
                
                Text(description)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.secondary.opacity(0.8))
            }
        }
    }
}

#Preview {
    PremiumFeaturePromptView(
        featureTitle: "Cloud Sync",
        featureDescription: "Keep your data synchronized across all your devices",
        featureIcon: "arrow.triangle.2.circlepath"
    )
}
