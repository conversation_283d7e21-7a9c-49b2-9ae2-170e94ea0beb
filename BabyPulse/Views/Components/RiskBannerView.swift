//
//  RiskBannerView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct RiskBannerView: View {
    let status: RiskStatus
    let message: String
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            // Status icon
            ZStack {
                Circle()
                    .fill(status.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: status.icon)
                    .font(.system(size: 20))
                    .foregroundColor(status.color)
            }
            
            // Message
            VStack(alignment: .leading, spacing: 4) {
                Text(statusTitle)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(status.color)
                
                Text(message)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.text)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // Action button
            if status != .normal {
                Button(action: {
                    // Action for risk banner
                }) {
                    Text("Details")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(status.color)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .stroke(status.color, lineWidth: 1.5)
                        )
                }
            }
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .stroke(status.color.opacity(0.3), lineWidth: 1.5)
        )
    }
    
    private var statusTitle: String {
        switch status {
        case .normal:
            return "All Good"
        case .warning:
            return "Attention Needed"
        case .alert:
            return "Alert"
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        RiskBannerView(
            status: .normal,
            message: "Everything looks good! Your baby's patterns are within normal ranges."
        )
        
        RiskBannerView(
            status: .warning,
            message: "Low diaper count today. Newborns typically have 6-8 wet diapers per day."
        )
        
        RiskBannerView(
            status: .alert,
            message: "No feedings logged in the last 6 hours. Newborns should feed every 2-3 hours."
        )
    }
    .padding()
    .background(Color(hex: "F5F5F7"))
}
