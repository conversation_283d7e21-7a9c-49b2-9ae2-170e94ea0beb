//
//  WheelDatePickerSheet.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A sheet that presents a wheel-style date picker
struct WheelDatePickerSheet: View {
    @Binding var date: Date
    @Binding var isPresented: Bool
    let title: String
    let displayedComponents: DatePickerComponents
    @State private var tempDate: Date
    @Environment(\.colorScheme) private var colorScheme
    
    init(
        title: String,
        date: Binding<Date>,
        isPresented: Binding<Bool>,
        displayedComponents: DatePickerComponents = [.date, .hourAndMinute]
    ) {
        self.title = title
        self._date = date
        self._isPresented = isPresented
        self.displayedComponents = displayedComponents
        self._tempDate = State(initialValue: date.wrappedValue)
    }
    
    var body: some View {
        ZStack {
            // Semi-transparent background
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    isPresented = false
                }
            
            // Picker view
            VStack {
                Spacer()
                
                WheelDatePickerView(
                    title: title,
                    date: $tempDate,
                    displayedComponents: displayedComponents,
                    onSave: {
                        date = tempDate
                        isPresented = false
                    },
                    onCancel: {
                        isPresented = false
                    }
                )
                .padding(.horizontal)
            }
            .transition(.move(edge: .bottom))
            .animation(.spring(), value: isPresented)
        }
    }
}

#Preview {
    WheelDatePickerSheet(
        title: "Select Date & Time",
        date: .constant(Date()),
        isPresented: .constant(true)
    )
}
