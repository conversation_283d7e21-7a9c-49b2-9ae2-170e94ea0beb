//
//  FormSectionView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A reusable view component for form sections with consistent styling
struct FormSectionView<Content: View>: View {
    let title: String
    let content: Content
    @Environment(\.colorScheme) private var colorScheme
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
            Text(title)
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.textSecondary)
            
            content
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
}

#Preview {
    VStack {
        FormSectionView(title: "Sample Section") {
            Text("Sample content")
                .padding()
        }
        .padding()
    }
    .background(Color(hex: "F5F5F7"))
}
