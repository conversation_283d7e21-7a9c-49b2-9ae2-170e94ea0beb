//
//  DateNavigationView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct DateNavigationView: View {
    @Binding var selectedDate: Date
    @State private var showDatePicker = false
    let onPreviousDay: () -> Void
    let onNextDay: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    private let calendar = Calendar.current
    private var isToday: Bool {
        calendar.isDateInToday(selectedDate)
    }

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                // Previous day button
                Button(action: self.onPreviousDay) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(BabyPulseColors.primary)
                        .padding(12)
                        .background(
                            Circle()
                                .fill(BabyPulseColors.primary.opacity(0.1))
                        )
                }

                Spacer()

                // Date display
                Button(action: {
                    showDatePicker = true
                }) {
                    HStack(spacing: 8) {
                        Text(formattedDate)
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.secondary)

                        Image(systemName: "calendar")
                            .font(.system(size: 16))
                            .foregroundColor(BabyPulseColors.primary)
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(
                        Capsule()
                            .fill(BabyPulseColors.primary.opacity(0.1))
                    )
                }

                Spacer()

                // Next day button (disabled if it's today)
                Button(action: self.onNextDay) {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(isToday ? BabyPulseColors.textTertiary : BabyPulseColors.primary)
                        .padding(12)
                        .background(
                            Circle()
                                .fill(isToday ? BabyPulseColors.lightGray : BabyPulseColors.primary.opacity(0.1))
                        )
                }
                .disabled(isToday)
            }
            .padding(.horizontal, BabyPulseLayout.paddingMD)
            .padding(.vertical, BabyPulseLayout.paddingSM)
            .background(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)

            // Date divider
            Rectangle()
                .fill(BabyPulseColors.lightGray)
                .frame(height: 1)
        }
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $selectedDate, isPresented: $showDatePicker)
        }
    }

    private var formattedDate: String {
        let formatter = DateFormatter()

        if calendar.isDateInToday(selectedDate) {
            return "Today"
        } else if calendar.isDateInYesterday(selectedDate) {
            return "Yesterday"
        } else {
            formatter.dateFormat = "EEEE, MMM d"
            return formatter.string(from: selectedDate)
        }
    }
}

struct DatePickerSheet: View {
    @Binding var selectedDate: Date
    @Binding var isPresented: Bool
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "Select a date",
                    selection: $selectedDate,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(.graphical)
                .padding()
                .tint(BabyPulseColors.primary)
            }
            .navigationTitle("Select Date")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                    .foregroundColor(BabyPulseColors.primary)
                }
            }
            .background(colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
        }
    }
}

#Preview {
    VStack {
        DateNavigationView(
            selectedDate: .constant(Date()),
            onPreviousDay: {},
            onNextDay: {}
        )

        Spacer()
    }
}
