//
//  BabyProfileHeaderView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct BabyProfileHeaderView: View {
    let babyName: String
    let babyAge: String
    let profileImage: UIImage?
    let onProfileTap: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: onProfileTap) {
            HStack(spacing: BabyPulseLayout.spacingMD) {
                // Profile image
                if let profileImage = self.profileImage {
                    Image(uiImage: profileImage)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 56, height: 56)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(BabyPulseColors.primary, lineWidth: 2)
                        )
                } else {
                    ZStack {
                        Circle()
                            .fill(BabyPulseColors.primary.opacity(0.2))
                            .frame(width: 56, height: 56)
                        
                        Image(systemName: "person.fill")
                            .font(.system(size: 24))
                            .foregroundColor(BabyPulseColors.primary)
                    }
                }
                
                // Baby info
                VStack(alignment: .leading, spacing: 4) {
                    Text(babyName)
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)
                    
                    Text(babyAge)
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(BabyPulseColors.textSecondary)
            }
            .padding(BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    VStack {
        BabyProfileHeaderView(
            babyName: "Emma",
            babyAge: "3 months, 2 weeks",
            profileImage: nil,
            onProfileTap: {}
        )
        .padding()
        
        BabyProfileHeaderView(
            babyName: "Noah",
            babyAge: "1 year, 5 months",
            profileImage: UIImage(systemName: "person.fill"),
            onProfileTap: {}
        )
        .padding()
    }
    .background(Color(hex: "F5F5F7"))
}
