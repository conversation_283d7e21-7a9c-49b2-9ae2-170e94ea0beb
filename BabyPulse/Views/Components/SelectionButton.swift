//
//  SelectionButton.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A reusable button component for selection with consistent styling
struct SelectionButton<Label: View>: View {
    let isSelected: Bool
    let action: () -> Void
    let color: Color
    let label: Label
    
    init(isSelected: Bool, color: Color, action: @escaping () -> Void, @ViewBuilder label: () -> Label) {
        self.isSelected = isSelected
        self.color = color
        self.action = action
        self.label = label()
    }
    
    var body: some View {
        Button(action: action) {
            label
                .padding(BabyPulseLayout.paddingSM)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(isSelected ? color.opacity(0.1) : Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .stroke(isSelected ? color : Color.clear, lineWidth: 1.5)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// A specialized selection button for icon-based selections
struct IconSelectionButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        SelectionButton(isSelected: isSelected, color: color, action: action) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(isSelected ? color.opacity(0.2) : Color.clear)
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: icon)
                        .font(.system(size: 24))
                        .foregroundColor(isSelected ? color : BabyPulseColors.textSecondary)
                }
                
                Text(title)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(isSelected ? color : BabyPulseColors.textSecondary)
            }
            .frame(maxWidth: .infinity)
        }
    }
}

/// A specialized selection button for radio-style selections
struct RadioSelectionButton: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(isSelected ? color : BabyPulseColors.textSecondary)
                
                Spacer()
                
                ZStack {
                    Circle()
                        .stroke(isSelected ? color : BabyPulseColors.mediumGray, lineWidth: 2)
                        .frame(width: 24, height: 24)
                    
                    if isSelected {
                        Circle()
                            .fill(color)
                            .frame(width: 16, height: 16)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(isSelected ? color.opacity(0.1) : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .stroke(isSelected ? color : BabyPulseColors.mediumGray, lineWidth: 1.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    VStack(spacing: 20) {
        // Basic SelectionButton
        SelectionButton(isSelected: true, color: BabyPulseColors.feeding, action: {}) {
            Text("Custom Selection")
                .padding()
        }
        
        // IconSelectionButton
        HStack {
            IconSelectionButton(
                title: "Breast",
                icon: "figure.dress",
                isSelected: true,
                color: BabyPulseColors.feeding,
                action: {}
            )
            
            IconSelectionButton(
                title: "Bottle",
                icon: "cup.and.saucer.fill",
                isSelected: false,
                color: BabyPulseColors.feeding,
                action: {}
            )
        }
        
        // RadioSelectionButton
        RadioSelectionButton(
            title: "Option 1",
            isSelected: true,
            color: BabyPulseColors.sleep,
            action: {}
        )
        
        RadioSelectionButton(
            title: "Option 2",
            isSelected: false,
            color: BabyPulseColors.sleep,
            action: {}
        )
    }
    .padding()
    .background(Color(hex: "F5F5F7"))
}
