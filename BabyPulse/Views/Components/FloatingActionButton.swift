//
//  FloatingActionButton.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct FloatingActionButton: View {
    let action: () -> Void
    let icon: String
    let label: String?

    init(icon: String, label: String? = nil, action: @escaping () -> Void) {
        self.icon = icon
        self.label = label
        self.action = action
    }

    var body: some View {
        Button(action: {
            let impactMed = UIImpactFeedbackGenerator(style: .medium)
            impactMed.impactOccurred()
            self.action()
        }) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(.white)

                if let label = label {
                    Text(label)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(.white)
                }
            }
            .padding(label == nil ? 16 : 12)
            .background(
                Circle()
                    .fill(BabyPulseColors.primary)
                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
            )
        }
    }
}

struct FloatingActionButtonGroup: View {
    @Binding var isExpanded: Bool
    let buttons: [(icon: String, label: String, action: () -> Void)]

    var body: some View {
        VStack(spacing: 16) {
            if isExpanded {
                ForEach(0..<buttons.count, id: \.self) { index in
                    let button = buttons[index]
                    FloatingActionButton(icon: button.icon, label: nil) {
                        isExpanded = false
                        button.action()
                    }
                    .transition(.scale.combined(with: .opacity))
                    .scaleEffect(0.9)
                }
            }

            FloatingActionButton(icon: isExpanded ? "xmark" : "plus") {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isExpanded.toggle()
                }
            }
            .scaleEffect(1.1)
            .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .padding()
    }
}

#Preview {
    VStack {
        Spacer()
        HStack {
            Spacer()
            FloatingActionButton(icon: "plus") {
                print("FAB tapped")
            }
        }
        .padding()
    }
}
