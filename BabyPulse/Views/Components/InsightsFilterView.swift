//
//  InsightsFilterView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

struct InsightsFilterView: View {
    @Binding var selectedCategory: Insight.InsightCategory?
    @Binding var timeRange: InsightsViewModel.TimeRange
    @Binding var sortOption: InsightsViewModel.SortOption
    let onApply: () -> Void
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                // Categories section
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Categories")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)

                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        ForEach([Insight.InsightCategory.feeding, .sleep, .diaper, .growth, .health, .development], id: \.self) { category in
                            categoryRow(category)

                            if category != .development {
                                Divider()
                                    .background(BabyPulseColors.lightGray)
                            }
                        }
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                // Time range section
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Time Range")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)

                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        ForEach(InsightsViewModel.TimeRange.allCases) { range in
                            timeRangeRow(range)

                            if range != InsightsViewModel.TimeRange.allCases.last {
                                Divider()
                                    .background(BabyPulseColors.lightGray)
                            }
                        }
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                // Sort options section
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Sort By")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)

                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        ForEach(InsightsViewModel.SortOption.allCases) { option in
                            sortOptionRow(option)

                            if option != InsightsViewModel.SortOption.allCases.last {
                                Divider()
                                    .background(BabyPulseColors.lightGray)
                            }
                        }
                    }
                    .padding(BabyPulseLayout.paddingMD)
                    .background(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                Spacer()

                // Apply button
                Button(action: {
                    onApply()
                    dismiss()
                }) {
                    Text("Apply Filters")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(BabyPulseLayout.paddingMD)
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                                .fill(BabyPulseColors.primary)
                        )
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)
                .padding(.bottom, BabyPulseLayout.paddingMD)
            }
            .padding(.top, BabyPulseLayout.paddingLG)
            .navigationTitle("Filter Insights")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onApply()
                        dismiss()
                    }
                    .foregroundColor(BabyPulseColors.primary)
                }
            }
            .background(colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
        }
    }

    private func categoryRow(_ category: Insight.InsightCategory) -> some View {
        let isSelected = selectedCategory == category

        return Button(action: {
            if isSelected {
                selectedCategory = nil
            } else {
                selectedCategory = category
            }
        }) {
            HStack {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color(categoryColor(category)).opacity(0.2))
                        .frame(width: 40, height: 40)

                    Image(systemName: categoryIcon(category))
                        .font(.system(size: 18))
                        .foregroundColor(Color(categoryColor(category)))
                }

                // Label
                Text(categoryTitle(category))
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.secondary)

                Spacer()

                // Radio button
                ZStack {
                    Circle()
                        .stroke(isSelected ? Color(categoryColor(category)) : BabyPulseColors.mediumGray, lineWidth: 2)
                        .frame(width: 24, height: 24)

                    if isSelected {
                        Circle()
                            .fill(Color(categoryColor(category)))
                            .frame(width: 16, height: 16)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // Helper functions for category display
    private func categoryTitle(_ category: Insight.InsightCategory) -> String {
        switch category {
        case .feeding:
            return "Feeding"
        case .sleep:
            return "Sleep"
        case .diaper:
            return "Diaper"
        case .growth:
            return "Growth"
        case .health:
            return "Health"
        case .development:
            return "Development"
        }
    }

    private func categoryIcon(_ category: Insight.InsightCategory) -> String {
        switch category {
        case .feeding:
            return "cup.and.saucer.fill" // Replacing bottle.fill with a valid symbol
        case .sleep:
            return "moon.fill"
        case .diaper:
            return "figure.walk.circle.fill"
        case .growth:
            return "ruler"
        case .health:
            return "heart.fill"
        case .development:
            return "brain.head.profile"
        }
    }

    private func categoryColor(_ category: Insight.InsightCategory) -> UIColor {
        switch category {
        case .feeding:
            return UIColor(BabyPulseColors.feeding)
        case .sleep:
            return UIColor(BabyPulseColors.sleep)
        case .diaper:
            return UIColor(BabyPulseColors.diaper)
        case .growth:
            return UIColor(BabyPulseColors.growth)
        case .health:
            return UIColor(BabyPulseColors.health)
        case .development:
            return UIColor(BabyPulseColors.primary)
        }
    }

    private func sortOptionRow(_ option: InsightsViewModel.SortOption) -> some View {
        Button(action: {
            sortOption = option
        }) {
            HStack {
                Text(option.rawValue)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.secondary)

                Spacer()

                // Radio button
                ZStack {
                    Circle()
                        .stroke(sortOption == option ? BabyPulseColors.primary : BabyPulseColors.mediumGray, lineWidth: 2)
                        .frame(width: 24, height: 24)

                    if sortOption == option {
                        Circle()
                            .fill(BabyPulseColors.primary)
                            .frame(width: 16, height: 16)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // Add time range selection
    private func timeRangeRow(_ range: InsightsViewModel.TimeRange) -> some View {
        Button(action: {
            timeRange = range
        }) {
            HStack {
                Text(range.rawValue)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.secondary)

                Spacer()

                // Radio button
                ZStack {
                    Circle()
                        .stroke(timeRange == range ? BabyPulseColors.primary : BabyPulseColors.mediumGray, lineWidth: 2)
                        .frame(width: 24, height: 24)

                    if timeRange == range {
                        Circle()
                            .fill(BabyPulseColors.primary)
                            .frame(width: 16, height: 16)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    InsightsFilterView(
        selectedCategory: .constant(Insight.InsightCategory.feeding),
        timeRange: .constant(InsightsViewModel.TimeRange.daily),
        sortOption: .constant(InsightsViewModel.SortOption.newest),
        onApply: {}
    )
}
