//
//  FormHeaderView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A reusable view component for form headers with consistent styling
struct FormHeaderView: View {
    let icon: String
    let title: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(color)
            
            Text(title)
                .font(BabyPulseTypography.title2())
                .foregroundColor(BabyPulseColors.secondary)
            
            Spacer()
        }
        .padding(.top, BabyPulseLayout.paddingMD)
    }
}

#Preview {
    VStack {
        FormHeaderView(
            icon: "cup.and.saucer.fill",
            title: "Feeding Log",
            color: BabyPulseColors.feeding
        )
        .padding()
        
        FormHeaderView(
            icon: "heart.fill",
            title: "Diaper Log",
            color: BabyPulseColors.diaper
        )
        .padding()
        
        FormHeaderView(
            icon: "moon.fill",
            title: "Sleep Log",
            color: BabyPulseColors.sleep
        )
        .padding()
    }
    .background(Color(hex: "F5F5F7"))
}
