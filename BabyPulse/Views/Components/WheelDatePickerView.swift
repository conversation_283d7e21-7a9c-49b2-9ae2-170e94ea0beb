//
//  WheelDatePickerView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A date picker component that uses the wheel style for selecting date and time
struct WheelDatePickerView: View {
    @Binding var date: Date
    let title: String
    let displayedComponents: DatePickerComponents
    let onSave: () -> Void
    let onCancel: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    
    init(
        title: String,
        date: Binding<Date>,
        displayedComponents: DatePickerComponents = [.date, .hourAndMinute],
        onSave: @escaping () -> Void,
        onCancel: @escaping () -> Void
    ) {
        self.title = title
        self._date = date
        self.displayedComponents = displayedComponents
        self.onSave = onSave
        self.onCancel = onCancel
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with title and buttons
            HStack {
                Button("Cancel") {
                    onCancel()
                }
                .foregroundColor(BabyPulseColors.primary)
                
                Spacer()
                
                Text(title)
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.secondary)
                
                Spacer()
                
                Button("Done") {
                    onSave()
                }
                .foregroundColor(BabyPulseColors.primary)
                .fontWeight(.semibold)
            }
            .padding()
            .background(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
            
            // Divider
            Rectangle()
                .fill(BabyPulseColors.lightGray)
                .frame(height: 1)
            
            // Wheel picker
            DatePicker("", selection: $date, displayedComponents: displayedComponents)
                .datePickerStyle(.wheel)
                .labelsHidden()
                .padding()
                .background(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
        }
        .cornerRadius(BabyPulseLayout.cornerRadiusLG)
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

#Preview {
    ZStack {
        Color.gray.opacity(0.3).ignoresSafeArea()
        
        WheelDatePickerView(
            title: "Select Date & Time",
            date: .constant(Date()),
            onSave: {},
            onCancel: {}
        )
        .padding()
    }
}
