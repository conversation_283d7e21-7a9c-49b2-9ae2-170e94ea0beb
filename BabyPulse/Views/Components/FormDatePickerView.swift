//
//  FormDatePickerView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A reusable date picker component with consistent styling
struct FormDatePickerView: View {
    @Binding var date: Date
    let title: String
    let displayedComponents: DatePickerComponents
    let useWheelPicker: Bool
    let showTimeOnly: Bool
    @State private var showWheelPicker = false
    @Environment(\.colorScheme) private var colorScheme

    init(
        title: String,
        date: Binding<Date>,
        displayedComponents: DatePickerComponents = [.date, .hourAndMinute],
        useWheelPicker: Bool = false,
        showTimeOnly: Bool = false
    ) {
        self.title = title
        self._date = date
        self.displayedComponents = displayedComponents
        self.useWheelPicker = useWheelPicker
        self.showTimeOnly = showTimeOnly
    }

    var body: some View {
        ZStack { // Root ZStack to allow overlay
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.textSecondary)

                if useWheelPicker {
                    Button(action: {
                        withAnimation {
                            showWheelPicker = true
                        }
                    }) {
                        HStack {
                            Spacer()
                            Text(formattedDate)
                                .font(BabyPulseTypography.body())
                                .foregroundColor(BabyPulseColors.text)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .font(.system(size: 12))
                                .foregroundColor(BabyPulseColors.textSecondary)
                        }
                        .padding(BabyPulseLayout.paddingSM)
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                    }
                } else {
                    DatePicker("", selection: $date, displayedComponents: displayedComponents)
                        .datePickerStyle(.compact)
                        .labelsHidden()
                        .padding(BabyPulseLayout.paddingSM)
                        .background(
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray)
                        )
                }
            }
            .disabled(showWheelPicker)

            // Overlay the WheelDatePickerSheet
            if showWheelPicker && useWheelPicker {
                WheelDatePickerSheet(
                    title: "Select \(title)",
                    date: $date,
                    isPresented: $showWheelPicker,
                    displayedComponents: displayedComponents
                )
                .zIndex(1)
            }
        }
    }

    private var formattedDate: String {
        let formatter = DateFormatter()

        if showTimeOnly {
            // Only show time regardless of the actual components
            formatter.dateStyle = .none
            formatter.timeStyle = .short
        } else if displayedComponents == .date {
            formatter.dateStyle = .medium
            formatter.timeStyle = .none
        } else if displayedComponents == .hourAndMinute {
            formatter.dateStyle = .none
            formatter.timeStyle = .short
        } else {
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
        }

        return formatter.string(from: date)
    }
}

#Preview {
    VStack {
        FormDatePickerView(
            title: "Date & Time (Compact)",
            date: .constant(Date())
        )
        .padding()

        FormDatePickerView(
            title: "Date & Time (Wheel)",
            date: .constant(Date()),
            useWheelPicker: true
        )
        .padding()

        FormDatePickerView(
            title: "Date & Time (Wheel, Time Only Display)",
            date: .constant(Date()),
            useWheelPicker: true,
            showTimeOnly: true
        )
        .padding()

        FormDatePickerView(
            title: "Date Only (Compact)",
            date: .constant(Date()),
            displayedComponents: .date
        )
        .padding()

        FormDatePickerView(
            title: "Date Only (Wheel)",
            date: .constant(Date()),
            displayedComponents: .date,
            useWheelPicker: true
        )
        .padding()

        FormDatePickerView(
            title: "Time Only (Compact)",
            date: .constant(Date()),
            displayedComponents: .hourAndMinute
        )
        .padding()

        FormDatePickerView(
            title: "Time Only (Wheel)",
            date: .constant(Date()),
            displayedComponents: .hourAndMinute,
            useWheelPicker: true
        )
        .padding()
    }
    .background(Color(hex: "F5F5F7"))
}
