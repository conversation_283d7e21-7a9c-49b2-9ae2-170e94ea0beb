//
//  TodaySummaryCardView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct TodaySummaryCardView: View {
    let summaryData: DailySummary
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            // Header
            HStack {
                Text("Today's Summary")
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.secondary)

                Spacer()

                Text(formattedDate)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }

            // Summary metrics
            HStack(spacing: BabyPulseLayout.spacingMD) {
                // Feeding amount summary
                SummaryMetricView(
                    icon: "drop.fill",
                    value: String(format: "%.0f", summaryData.totalFeedingVolume),
                    label: "mL",
                    color: BabyPulseColors.feeding
                )

                Divider()
                    .frame(width: 1)
                    .background(BabyPulseColors.lightGray)
                    .padding(.vertical, 4)

                // Diaper summary
                SummaryMetricView(
                    icon: "heart.fill",
                    value: "\(summaryData.totalDiapers)",
                    label: "Diapers",
                    color: BabyPulseColors.diaper
                )

                Divider()
                    .frame(width: 1)
                    .background(BabyPulseColors.lightGray)
                    .padding(.vertical, 4)

                // Sleep summary
                SummaryMetricView(
                    icon: "moon.fill",
                    value: String(format: "%.1f", summaryData.totalSleepHours),
                    label: "Sleep (hrs)",
                    color: BabyPulseColors.sleep
                )
            }
            .padding(BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray.opacity(0.5))
            )


        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        return formatter.string(from: Date())
    }
}

// MARK: - Supporting Views

struct SummaryMetricView: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(color)
            }

            Text(value)
                .font(BabyPulseTypography.title3())
                .foregroundColor(BabyPulseColors.secondary)

            Text(label)
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct DetailMetricView: View {
    let label: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(label)
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textSecondary)

            Text(value)
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    VStack {
        TodaySummaryCardView(
            summaryData: DailySummary(
                totalFeedings: 6,
                breastFeedings: 3,
                bottleFeedings: 2,
                solidFeedings: 1,
                totalFeedingVolume: 240,
                totalDiapers: 5,
                wetDiapers: 3,
                dirtyDiapers: 1,
                mixedDiapers: 1,
                totalSleepHours: 12.5,
                sleepSessions: 3
            )
        )
        .padding()
    }
    .background(Color(hex: "F5F5F7"))
}
