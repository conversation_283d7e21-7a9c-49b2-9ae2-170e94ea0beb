//
//  InsightGenerationView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct InsightGenerationView: View {
    let state: InsightGenerationManager.GenerationState
    
    var body: some View {
        switch state {
        case .idle:
            EmptyView()
            
        case .generating:
            generatingBanner
            
        case .completed(let count):
            completedBanner(count: count)
            
        case .failed(let error):
            errorBanner(message: error)
        }
    }
    
    private var generatingBanner: some View {
        HStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(0.8)
            
            Text("Generating insights...")
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.text)
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal)
        .transition(.move(edge: .top).combined(with: .opacity))
    }
    
    private func completedBanner(count: Int) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.system(size: 20))
            
            Text("\(count) new insight\(count == 1 ? "" : "s") generated")
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.text)
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal)
        .transition(.move(edge: .top).combined(with: .opacity))
    }
    
    private func errorBanner(message: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.circle.fill")
                .foregroundColor(.red)
                .font(.system(size: 20))
            
            Text(message)
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.text)
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal)
        .transition(.move(edge: .top).combined(with: .opacity))
    }
}

#Preview {
    VStack(spacing: 20) {
        InsightGenerationView(state: .idle)
        InsightGenerationView(state: .generating(babyId: UUID()))
        InsightGenerationView(state: .completed(insightCount: 3))
        InsightGenerationView(state: .failed(error: "Failed to connect to the server"))
    }
    .padding()
}
