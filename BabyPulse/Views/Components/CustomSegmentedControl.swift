//
//  CustomSegmentedControl.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A modern, customizable segmented control with icons and animation
struct CustomSegmentedControl<T: Hashable>: View {
    @Binding var selection: T
    let options: [SegmentOption<T>]
    let color: Color
    
    @Namespace private var animation
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(options.indices, id: \.self) { index in
                let option = options[index]
                let isSelected = option.value == selection
                
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selection = option.value
                    }
                }) {
                    VStack(spacing: 4) {
                        if let icon = option.icon {
                            Image(systemName: icon)
                                .font(.system(size: 16, weight: isSelected ? .semibold : .regular))
                        }
                        
                        Text(option.title)
                            .font(BabyPulseTypography.footnote())
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        ZStack {
                            if isSelected {
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .fill(color.opacity(0.2))
                                    .matchedGeometryEffect(id: "SegmentBackground", in: animation)
                            }
                        }
                    )
                    .foregroundColor(isSelected ? color : BabyPulseColors.textSecondary)
                }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : BabyPulseColors.lightGray.opacity(0.5))
        )
        .overlay(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
}

/// Represents an option in the segmented control
struct SegmentOption<T: Hashable> {
    let title: String
    let icon: String?
    let value: T
    
    init(title: String, icon: String? = nil, value: T) {
        self.title = title
        self.icon = icon
        self.value = value
    }
}

#Preview {
    VStack(spacing: 30) {
        // Example with icons
        CustomSegmentedControl(
            selection: .constant("option1"),
            options: [
                SegmentOption(title: "Formula", icon: "drop.triangle.fill", value: "option1"),
                SegmentOption(title: "Breast Milk", icon: "figure.dress", value: "option2"),
                SegmentOption(title: "Mixed", icon: "arrow.triangle.merge", value: "option3")
            ],
            color: BabyPulseColors.feeding
        )
        .padding()
        
        // Example without icons
        CustomSegmentedControl(
            selection: .constant(2),
            options: [
                SegmentOption(title: "Option 1", value: 1),
                SegmentOption(title: "Option 2", value: 2),
                SegmentOption(title: "Option 3", value: 3)
            ],
            color: BabyPulseColors.sleep
        )
        .padding()
    }
    .padding()
    .background(Color(hex: "F5F5F7"))
}
