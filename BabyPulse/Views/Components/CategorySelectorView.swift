//
//  CategorySelectorView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct CategorySelectorView: View {
    @Binding var selectedCategory: LogCategory?
    var onCategoryChanged: ((LogCategory?) -> Void)?
    var excludedCategories: [LogCategory] = []
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: BabyPulseLayout.spacingSM) {
                // All category option
                categoryButton(nil, "All")

                // Individual categories - filter out excluded categories
                ForEach(LogCategory.allCases.filter { !excludedCategories.contains($0) }) { category in
                    categoryButton(category, category.title)
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingMD)
            .padding(.vertical, BabyPulseLayout.paddingSM)
        }
        .background(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
    }

    private func categoryButton(_ category: LogCategory?, _ title: String) -> some View {
        Button(action: {
            selectedCategory = category
            onCategoryChanged?(category)
        }) {
            HStack(spacing: 4) {
                if let category = category {
                    Image(systemName: category.icon)
                        .font(.system(size: 14))
                }

                Text(title)
                    .font(BabyPulseTypography.footnote())
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                Capsule()
                    .fill(selectedCategory == category ?
                          (category?.color ?? BabyPulseColors.primary) :
                          (colorScheme == .dark ? Color(hex: "2C2C2E") : Color.gray.opacity(0.15)))
            )
            .foregroundColor(selectedCategory == category ? .white : BabyPulseColors.textSecondary)
        }
    }
}

#Preview {
    VStack {
        CategorySelectorView(
            selectedCategory: .constant(.feeding),
            onCategoryChanged: { _ in }
        )

        Spacer()

        // Preview with excluded categories
        CategorySelectorView(
            selectedCategory: .constant(.feeding),
            onCategoryChanged: { _ in },
            excludedCategories: [.health]
        )
    }
}
