//
//  EnhancedFormHeaderView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// A reusable view component for enhanced form headers with consistent styling
struct EnhancedFormHeaderView: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    @State private var isAnimating = false

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Icon with enhanced design
            ZStack {
                // Background circles for depth
                Circle()
                    .fill(color.opacity(0.1))
                    .frame(width: 100, height: 100)
                
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 35, weight: .medium))
                    .foregroundColor(color)
                    .scaleEffect(isAnimating ? 1.05 : 1.0)
            }
            .shadow(color: color.opacity(0.3), radius: 8, x: 0, y: 4)
            
            // Title and subtitle
            VStack(spacing: BabyPulseLayout.spacingXS) {
                Text(title)
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.secondary)
                
                Text(subtitle)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .opacity(0.8)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, BabyPulseLayout.paddingMD)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                isAnimating = true
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(subtitle)")
    }
}

#Preview {
    VStack {
        EnhancedFormHeaderView(
            icon: "cup.and.saucer.fill",
            title: "Feeding Entry",
            subtitle: "Track your baby's feeding details",
            color: BabyPulseColors.feeding
        )
        
        EnhancedFormHeaderView(
            icon: "heart.fill",
            title: "Diaper Change",
            subtitle: "Record diaper change details",
            color: BabyPulseColors.diaper
        )
        
        EnhancedFormHeaderView(
            icon: "moon.fill",
            title: "Sleep Entry",
            subtitle: "Track your baby's sleep",
            color: BabyPulseColors.sleep
        )
    }
    .padding()
    .background(Color(hex: "F5F5F7"))
}
