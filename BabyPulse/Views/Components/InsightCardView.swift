//
//  InsightCardView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct InsightCardView: View {
    let insight: Insight
    @Environment(\.colorScheme) private var colorScheme
    @State private var isExpanded: Bool = false
    var onTap: ((Insight) -> Void)?

    var body: some View {
        Button(action: {
            onTap?(insight)
        }) {
            cardContent
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Card Content

    private var cardContent: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            // Header
            HStack {
                // Severity indicator
                Circle()
                    .fill(insight.severity.color)
                    .frame(width: 8, height: 8)
                    .padding(.trailing, 4)

                ZStack {
                    Circle()
                        .fill(insight.category.color.opacity(0.2))
                        .frame(width: 32, height: 32)

                    Image(systemName: insight.category.icon)
                        .font(.system(size: 16))
                        .foregroundColor(insight.category.color)
                }

                Text(insight.title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(insight.category.color)

                Spacer()

                // Severity indicator as icon
                if insight.severity == .urgent {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.red)
                        .padding(.trailing, 4)
                } else if insight.severity == .warning {
                    Image(systemName: "exclamationmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.orange)
                        .padding(.trailing, 4)
                }

                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(BabyPulseColors.textSecondary)
            }

            // Metric
            Text(insight.metric)
                .font(BabyPulseTypography.title3())
                .foregroundColor(BabyPulseColors.secondary)

            // Description
            MarkdownText(content: insight.insightContent)
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.textSecondary)
                .lineLimit(isExpanded ? nil : 3)

            // Action Items (if available)
            if !insight.actionItems.isEmpty {
                actionItemsView
            }

            // Footer
            footerView
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .stroke(insight.severity != .info ? insight.severity.color.opacity(0.3) : Color.clear, lineWidth: 1)
        )
    }

    // MARK: - Action Items View

    private var actionItemsView: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
            Text("Suggested Actions:")
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.textSecondary)
                .fontWeight(.medium)

            if !isExpanded {
                // Show only first action item when collapsed
                actionItemRow(text: insight.actionItems[0])
            } else {
                // Show all action items when expanded
                ForEach(insight.actionItems.indices, id: \.self) { index in
                    actionItemRow(text: insight.actionItems[index])
                }
            }
        }
        .padding(.vertical, BabyPulseLayout.spacingSM)
        .padding(.horizontal, BabyPulseLayout.spacingSM)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusSM)
                .fill(insight.category.color.opacity(0.1))
        )
    }

    private func actionItemRow(text: String) -> some View {
        HStack(alignment: .top, spacing: 4) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 12))
                .foregroundColor(insight.category.color)

            MarkdownText(content: text, fontSize: 12)
                .foregroundColor(BabyPulseColors.text)
        }
    }

    // MARK: - Footer View

    private var footerView: some View {
        HStack {
            // Timestamp
            Text(formattedTimestamp)
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textTertiary)

            Spacer()

            // Expand/Collapse button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }) {
                Text(isExpanded ? "Show less" : "Show more")
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(insight.category.color)
            }

            // Confidence indicator
            if insight.confidence < 100 {
                confidenceIndicator
            }
        }
    }

    private var confidenceIndicator: some View {
        HStack(spacing: 2) {
            Text("Confidence:")
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textTertiary)

            Text("\(insight.confidence)%")
                .font(BabyPulseTypography.captionBold())
                .foregroundColor(confidenceColor)
        }
    }

    private var formattedTimestamp: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: insight.timestamp, relativeTo: Date())
    }

    private var confidenceColor: Color {
        if insight.confidence >= 90 {
            return .green
        } else if insight.confidence >= 70 {
            return .orange
        } else {
            return .red
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        InsightCardView(
            insight: Insight(
                id: UUID(),
                category: .feeding,
                title: "Feeding Pattern",
                metric: "6 feedings today",
                insightContent: "Your baby is feeding well with a good mix of breast and bottle feedings. The average time between feedings is about 3 hours.",
                timestamp: Date().addingTimeInterval(-3600),
                isRead: false,
                needsAttention: false,
                confidence: 95,
                severity: .info,
                actionItems: ["Continue with the current feeding schedule", "Monitor for any signs of hunger between feedings"],
                hashKey: "abc123"
            )
        )

        InsightCardView(
            insight: Insight(
                id: UUID(),
                category: .sleep,
                title: "Sleep Pattern Changes",
                metric: "12.5 hours",
                insightContent: "Your baby's sleep pattern has changed. The longest stretch was 4 hours during the night, which is shorter than the previous average of 6 hours. This could be a sign of a sleep regression or developmental leap.",
                timestamp: Date().addingTimeInterval(-7200),
                isRead: false,
                needsAttention: true,
                confidence: 85,
                severity: .warning,
                actionItems: ["Maintain a consistent bedtime routine", "Consider adjusting daytime naps", "Watch for signs of overtiredness"],
                hashKey: "def456"
            )
        )

        InsightCardView(
            insight: Insight(
                id: UUID(),
                category: .health,
                title: "Temperature Alert",
                metric: "38.5°C",
                insightContent: "Your baby has a fever that has persisted for more than 24 hours. This is concerning and requires immediate attention, especially for a baby under 3 months old.",
                timestamp: Date().addingTimeInterval(-1800),
                isRead: false,
                needsAttention: true,
                confidence: 95,
                severity: .urgent,
                actionItems: ["Contact your pediatrician immediately", "Monitor temperature every hour", "Ensure adequate fluid intake"],
                hashKey: "ghi789"
            )
        )
    }
    .padding()
    .background(Color(hex: "F5F5F7"))
}
