//
//  MainTabView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

// Import models
@preconcurrency import class SwiftData.ModelContext

// Import views - needed for DirectChatView
import OSLog

struct MainTabView: View {
    @State private var selectedTab = 0
    @State private var showingLogEntry = false
    @State private var fabExpanded = false
    @State private var selectedLogCategory: LogCategory? = nil
    @Environment(\.colorScheme) private var colorScheme

    // For tab animation
    @Namespace private var tabAnimation

    var body: some View {
        ZStack {
            // Background with proper safe area handling
            BabyPulseColors.background
                .ignoresSafeArea(.all)

            // Main content structure
            VStack(spacing: 0) {
                // Tab content with proper safe area respect
                TabView(selection: $selectedTab) {
                    // Home tab
                    NavigationStack {
                        HomeView()
                            .safeAreaInset(edge: .bottom) {
                                // Reserve space for custom tab bar
                                Color.clear.frame(height: 88)
                            }
                    }
                    .tag(0)

                    // Logs tab
                    NavigationStack {
                        LogsView()
                            .safeAreaInset(edge: .bottom) {
                                // Reserve space for custom tab bar
                                Color.clear.frame(height: 88)
                            }
                    }
                    .tag(1)

                    // Insights tab
                    NavigationStack {
                        InsightsView()
                            .safeAreaInset(edge: .bottom) {
                                // Reserve space for custom tab bar
                                Color.clear.frame(height: 88)
                            }
                    }
                    .tag(2)

                    // Chat tab
                    NavigationStack {
                        DirectChatView()
                            .safeAreaInset(edge: .bottom) {
                                // Reserve space for custom tab bar
                                Color.clear.frame(height: 88)
                            }
                    }
                    .tag(3)

                    // Settings tab
                    NavigationStack {
                        SettingsView()
                            .safeAreaInset(edge: .bottom) {
                                // Reserve space for custom tab bar
                                Color.clear.frame(height: 88)
                            }
                    }
                    .tag(4)
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
            }

            // Custom floating tab bar overlay
            VStack {
                Spacer()
                modernCustomTabBar
                    .safeAreaInset(edge: .bottom) {
                        Color.clear.frame(height: 8)
                    }
            }

            // Enhanced Floating Action Button
            EnhancedFloatingActionButton(
                isExpanded: $fabExpanded,
                buttons: [
                    (icon: "drop.fill", label: "Feeding", color: BabyPulseColors.feeding, action: { showDirectEntryForm(category: .feeding) }),
                    (icon: "moon.fill", label: "Sleep", color: BabyPulseColors.sleep, action: { showDirectEntryForm(category: .sleep) }),
                    (icon: "heart.fill", label: "Diaper", color: BabyPulseColors.diaper, action: { showDirectEntryForm(category: .diaper) }),
                    (icon: "list.clipboard", label: "Logs", color: BabyPulseColors.primary, action: { showLogEntryWithCategorySelection() })
                ]
            )
        }
        .sheet(isPresented: $showingLogEntry) {
            LogEntryView(category: selectedLogCategory)
                .onDisappear {
                    // Notify LogsView to refresh when entry is added
                    NotificationCenter.default.post(name: .logEntryAdded, object: nil)
                }
        }
        .ignoresSafeArea(.keyboard) // Handle keyboard properly
    }

    // MARK: - Modern Custom Tab Bar
    private var modernCustomTabBar: some View {
        HStack(spacing: 0) {
            ForEach(0..<5) { index in
                Button {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selectedTab = index
                    }

                    // Add haptic feedback
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()
                } label: {
                    VStack(spacing: 6) {
                        // Icon with improved visual states
                        Image(systemName: tabIcon(for: index))
                            .font(.system(
                                size: selectedTab == index ? 24 : 20,
                                weight: selectedTab == index ? .semibold : .medium
                            ))
                            .foregroundStyle(
                                selectedTab == index ?
                                    BabyPulseColors.primary :
                                    BabyPulseColors.textSecondary
                            )
                            .scaleEffect(selectedTab == index ? 1.1 : 1.0)
                            .animation(.spring(response: 0.3), value: selectedTab)

                        // Active label with animation
                        if selectedTab == index {
                            Text(tabTitle(for: index))
                                .font(BabyPulseTypography.caption())
                                .fontWeight(.semibold)
                                .foregroundColor(BabyPulseColors.primary)
                                .matchedGeometryEffect(id: "tab_text", in: tabAnimation)
                                .transition(.opacity.combined(with: .scale))
                        }

                        // Active indicator dot
                        Circle()
                            .fill(selectedTab == index ? BabyPulseColors.primary : Color.clear)
                            .frame(width: 6, height: 6)
                            .scaleEffect(selectedTab == index ? 1 : 0)
                            .animation(.spring(response: 0.3), value: selectedTab)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .contentShape(Rectangle())
                }
                .buttonStyle(ModernTabButtonStyle())
            }
        }
        .padding(.horizontal, BabyPulseLayout.paddingMD)
        .padding(.vertical, BabyPulseLayout.paddingSM)
        .background(
            // Modern glassmorphism tab bar
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusXL)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusXL)
                        .stroke(
                            Color.primary.opacity(0.1),
                            lineWidth: 0.5
                        )
                )
                .shadow(
                    color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.1),
                    radius: 20,
                    x: 0,
                    y: 8
                )
        )
        .padding(.horizontal, BabyPulseLayout.paddingLG)
    }

    // MARK: - Helper Functions
    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "house.fill"
        case 1: return "list.bullet.clipboard.fill"
        case 2: return "chart.bar.fill"
        case 3: return "message.fill"
        case 4: return "gear"
        default: return "house.fill"
        }
    }

    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Home"
        case 1: return "Logs"
        case 2: return "Insights"
        case 3: return "Chat"
        case 4: return "Settings"
        default: return "Home"
        }
    }

    // Show the general log entry view with category selection
    private func showLogEntryWithCategorySelection() {
        selectedLogCategory = nil
        fabExpanded = false

        // Add haptic feedback
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // Add a slight delay to allow the FAB animation to complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            showingLogEntry = true
        }
    }

    // Show a specific entry form directly
    private func showDirectEntryForm(category: LogCategory) {
        selectedLogCategory = category
        fabExpanded = false

        // Add haptic feedback
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // Add a slight delay to allow the FAB animation to complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            showingLogEntry = true
        }
    }
}

// MARK: - Modern Tab Button Style
struct ModernTabButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Enhanced FAB with improved design
struct EnhancedFloatingActionButton: View {
    @Binding var isExpanded: Bool
    let buttons: [(icon: String, label: String, color: Color, action: () -> Void)]
    @Environment(\.colorScheme) private var colorScheme

    @State private var dragAmount: CGSize = .zero
    @State private var finalOffset: CGSize = .zero
    @State private var hasInitializedPosition = false

    // FAB properties
    private let fabSize: CGFloat = 64
    private var fabRadius: CGFloat { fabSize / 2 }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Backdrop when expanded
                if isExpanded {
                    Color.black.opacity(0.2)
                        .ignoresSafeArea()
                        .blur(radius: 2)
                        .onTapGesture {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                isExpanded = false
                            }
                        }
                }

                // FAB Content positioned at bottom-right
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        
                        VStack(spacing: 20) {
                            // Expanded menu items
                            if isExpanded {
                                ForEach(buttons.indices, id: \.self) { index in
                                    Button(action: {
                                        buttons[index].action()
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            isExpanded = false
                                        }
                                    }) {
                                        HStack(spacing: 16) {
                                            Text(buttons[index].label)
                                                .font(BabyPulseTypography.callout())
                                                .fontWeight(.semibold)
                                                .foregroundColor(.white)
                                                .padding(.horizontal, 20)
                                                .padding(.vertical, 12)
                                                .background(
                                                    Capsule()
                                                        .fill(buttons[index].color)
                                                        .shadow(color: buttons[index].color.opacity(0.3), radius: 8, x: 0, y: 4)
                                                )
                                            
                                            Image(systemName: buttons[index].icon)
                                                .font(.system(size: 22, weight: .semibold))
                                                .foregroundColor(.white)
                                                .frame(width: 56, height: 56)
                                                .background(
                                                    Circle()
                                                        .fill(buttons[index].color)
                                                        .shadow(color: buttons[index].color.opacity(0.3), radius: 12, x: 0, y: 6)
                                                )
                                        }
                                    }
                                    .transition(.asymmetric(
                                        insertion: .move(edge: .trailing).combined(with: .opacity).combined(with: .scale),
                                        removal: .move(edge: .trailing).combined(with: .opacity)
                                    ))
                                }
                            }

                            // Main FAB button
                            Button(action: {
                                let generator = UIImpactFeedbackGenerator(style: .medium)
                                generator.impactOccurred()
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                    isExpanded.toggle()
                                }
                            }) {
                                Image(systemName: isExpanded ? "xmark" : "plus")
                                    .font(.system(size: 26, weight: .semibold))
                                    .foregroundColor(.white)
                                    .frame(width: fabSize, height: fabSize)
                                    .background(
                                        Circle()
                                            .fill(BabyPulseColors.primary)
                                            .shadow(color: BabyPulseColors.primary.opacity(0.4), radius: 10, x: 0, y: 5)
                                    )
                            }
                        }
                        .offset(x: dragAmount.width, y: dragAmount.height)
                        .gesture(
                            DragGesture(minimumDistance: 1)
                                .onChanged { gesture in
                                    if !isExpanded {
                                        self.dragAmount = gesture.translation
                                    }
                                }
                                .onEnded { gesture in
                                    if !isExpanded {
                                        // Update the position based on drag
                                        let newX = finalOffset.width + gesture.translation.width
                                        let newY = finalOffset.height + gesture.translation.height
                                        
                                        // Calculate bounds relative to the geometry
                                        let safeAreaInsets = geometry.safeAreaInsets
                                        let maxX = geometry.size.width - fabSize - safeAreaInsets.trailing - BabyPulseLayout.paddingMD
                                        let minX = safeAreaInsets.leading + BabyPulseLayout.paddingMD
                                        let maxY = geometry.size.height - fabSize - safeAreaInsets.bottom - 96 - BabyPulseLayout.paddingMD // Account for tab bar
                                        let minY = safeAreaInsets.top + BabyPulseLayout.paddingMD
                                        
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            finalOffset.width = min(maxX, max(minX, newX))
                                            finalOffset.height = min(maxY, max(minY, newY))
                                            dragAmount = .zero
                                        }
                                    }
                                }
                        )
                        .offset(x: finalOffset.width, y: finalOffset.height)
                        .padding(.trailing, BabyPulseLayout.paddingMD)
                        .padding(.bottom, 96 + BabyPulseLayout.paddingMD) // Account for tab bar height
                    }
                }
            }
            .onAppear {
                if !hasInitializedPosition {
                    // Start at bottom-right position
                    let safeAreaInsets = geometry.safeAreaInsets
                    finalOffset = CGSize(
                        width: 0, // Already positioned at trailing edge
                        height: 0  // Already positioned at bottom edge
                    )
                    hasInitializedPosition = true
                }
            }
        }
        .allowsHitTesting(true)
    }
}

#Preview {
    MainTabView()
        .modelContainer(for: [Baby.self, UserPreferences.self, FeedingEntry.self, DiaperEntry.self, SleepEntry.self, GrowthEntry.self, HealthEntry.self, ChatMessage.self, ChatThread.self], inMemory: true)
}
