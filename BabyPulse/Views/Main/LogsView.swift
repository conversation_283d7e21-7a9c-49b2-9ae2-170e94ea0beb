//
//  LogsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData
import Combine

// ViewState enum for better state management
enum LogsViewState: Equatable {
    case loading
    case empty
    case content
    case error(String)

    static func == (lhs: LogsViewState, rhs: LogsViewState) -> Bo<PERSON> {
        switch (lhs, rhs) {
        case (.loading, .loading), (.empty, .empty), (.content, .content):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

struct LogsView: View {
    @StateObject private var viewModel = LogsViewModel()
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @State private var showLogEntrySheet = false
    @State private var showFilterSheet = false
    @State private var showDatePicker = false
    @State private var selectedEntry: TimelineEntry?

    // Used for animation between states
    @Namespace private var animation

    var body: some View {
        ZStack {
            // Background
            (colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern header section
                modernHeaderSection
                    .background(.ultraThinMaterial)
                    .zIndex(1)

                // Content based on selected time span
                if viewModel.selectedTimeSpan.isChartView {
                    // Chart view
                    LogChartView(viewModel: viewModel)
                        .background(colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
                } else {
                    // Daily view with enhanced design
                    VStack(spacing: 0) {
                        // Date navigation with modern design and date picker
                        modernDateNavigator
                            .background(.ultraThinMaterial)
                            .zIndex(1)

                        // Content with state handling
                        ZStack {
                            switch getViewState() {
                            case .loading:
                                modernSkeletonView
                                    .transition(.opacity)
                            case .empty:
                                modernEmptyStateView
                                    .transition(.scale.combined(with: .opacity))
                            case .content:
                                modernTimelineContent
                                    .transition(.opacity)
                            case .error(let message):
                                modernErrorView(message: message)
                                    .transition(.scale.combined(with: .opacity))
                            }
                        }
                        .animation(.spring(response: 0.3), value: getViewState())
                        .background(colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showLogEntrySheet) {
            LogEntryView()
                .onDisappear {
                    viewModel.loadEntriesForSelectedDate()
                }
        }
        .sheet(isPresented: $showFilterSheet) {
            OptimizedFilterView(filters: $viewModel.activeFilters)
                .onDisappear {
                    viewModel.loadEntriesForSelectedDate()
                }
        }
        .sheet(isPresented: $showDatePicker) {
            DatePickerView(selectedDate: $viewModel.selectedDate)
                .onDisappear {
                    viewModel.loadEntriesForSelectedDate()
                }
        }
        .navigationDestination(item: $selectedEntry) { entry in
            detailView(for: entry)
        }
        .onAppear {
            viewModel.setModelContext(modelContext)
            // Always set default filters to include all categories on appear for now
            // This ensures .general is included. If you implement filter persistence later,
            // you might need a more sophisticated check.
            viewModel.activeFilters = Set(LogCategory.allCases)
            print("LOGSVIEW: Forcing activeFilters to all cases: \(viewModel.activeFilters.map { $0.rawValue })") // DEBUG
        }
        .onReceive(NotificationCenter.default.publisher(for: .logEntryAdded)) { _ in
            // Real-time update when activities are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .feedingEntryAdded)) { _ in
            // Real-time update when feeding entries are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .diaperEntryAdded)) { _ in
            // Real-time update when diaper entries are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .sleepEntryAdded)) { _ in
            // Real-time update when sleep entries are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .growthEntryAdded)) { _ in
            // Real-time update when growth entries are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .healthEntryAdded)) { _ in
            // Real-time update when health entries are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .generalActivityAdded)) { _ in
            // Real-time update when general activities are added
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .generalActivityEntryUpdated)) { _ in
            // Real-time update when general activities are updated
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.loadEntriesForSelectedDate()
            }
        }
        .refreshable {
            await refreshData()
        }
    }

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Title and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Activity Logs")
                        .font(BabyPulseTypography.title1())
                        .fontWeight(.bold)
                        .foregroundColor(BabyPulseColors.text)

                    Text("Track your baby's activities")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Spacer()

                HStack(spacing: BabyPulseLayout.spacingMD) {
                    // Filter button with badge
                    Button(action: {
                        showFilterSheet = true
                    }) {
                        ZStack {
                            Image(systemName: "line.3.horizontal.decrease.circle.fill")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(BabyPulseColors.primary)

                            // Active filter indicator
                            if viewModel.activeFilters.count < LogCategory.allCases.count {
                                Circle()
                                    .fill(BabyPulseColors.error)
                                    .frame(width: 8, height: 8)
                                    .offset(x: 8, y: -8)
                            }
                        }
                    }

                    // Add entry button
                    Button(action: {
                        showLogEntrySheet = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(BabyPulseColors.primary)
                    }
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)

            // Improved time span selector
            improvedTimeSpanSelector
                .padding(.horizontal, BabyPulseLayout.paddingLG)
        }
        .padding(.vertical, BabyPulseLayout.paddingMD)
    }

    // MARK: - Improved Time Span Selector (cleaner design)
    private var improvedTimeSpanSelector: some View {
        HStack(spacing: 0) {
            ForEach(TimeSpan.allCases, id: \.self) { timeSpan in
                Button(action: {
                    withAnimation(.spring(response: 0.3)) {
                        viewModel.setTimeSpan(timeSpan)
                    }

                    // Add haptic feedback
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()
                }) {
                    Text(timeSpan.title)
                        .font(BabyPulseTypography.footnote())
                        .fontWeight(.semibold)
                        .foregroundColor(viewModel.selectedTimeSpan == timeSpan ? .white : BabyPulseColors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, BabyPulseLayout.spacingSM)
                        .background(
                            Rectangle()
                                .fill(viewModel.selectedTimeSpan == timeSpan ? BabyPulseColors.primary : Color.clear)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .stroke(BabyPulseColors.mediumGray.opacity(0.3), lineWidth: 1)
                )
        )
        .clipShape(RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD))
    }

    // MARK: - Enhanced Date Navigator with Date Picker
    private var modernDateNavigator: some View {
        HStack {
            Button(action: {
                withAnimation(.spring(response: 0.3)) {
                    viewModel.loadPreviousDay()
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(BabyPulseColors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(BabyPulseColors.primary.opacity(0.1))
                    )
            }

            Spacer()

            // Tappable date section to open date picker
            Button(action: {
                showDatePicker = true
            }) {
                VStack(spacing: 4) {
                    Text(formatSelectedDate())
                        .font(BabyPulseTypography.title3())
                        .fontWeight(.bold)
                        .foregroundColor(BabyPulseColors.text)

                    HStack(spacing: 4) {
                        Text(formatSelectedDateSubtitle())
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.textSecondary)
                        
                        Image(systemName: "calendar")
                            .font(.system(size: 12))
                            .foregroundColor(BabyPulseColors.primary)
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            Button(action: {
                withAnimation(.spring(response: 0.3)) {
                    viewModel.loadNextDay()
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(viewModel.isToday ? BabyPulseColors.textTertiary : BabyPulseColors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill((viewModel.isToday ? BabyPulseColors.textTertiary : BabyPulseColors.primary).opacity(0.1))
                    )
            }
            .disabled(viewModel.isToday)
        }
        .padding(.horizontal, BabyPulseLayout.paddingLG)
        .padding(.vertical, BabyPulseLayout.paddingMD)
    }

    // MARK: - Content Views

    private var modernTimelineContent: some View {
        ScrollView {
            LazyVStack(spacing: BabyPulseLayout.spacingMD) {
                ForEach(Array(viewModel.logEntries.enumerated()), id: \.element.id) { index, entry in
                    ModernTimelineEntryCard(
                        entry: entry,
                        isFirst: index == 0,
                        isLast: index == viewModel.logEntries.count - 1
                    ) {
                        selectedEntry = entry
                    }
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)
            .padding(.vertical, BabyPulseLayout.paddingMD)
        }
    }

    private var modernSkeletonView: some View {
        ScrollView {
            LazyVStack(spacing: BabyPulseLayout.spacingMD) {
                ForEach(0..<5, id: \.self) { _ in
                    ModernSkeletonCard()
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)
            .padding(.vertical, BabyPulseLayout.paddingMD)
        }
    }

    private var modernEmptyStateView: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            Image(systemName: "calendar.badge.plus")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(BabyPulseColors.primary.opacity(0.6))

            VStack(spacing: BabyPulseLayout.spacingSM) {
                Text("No activities logged")
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)

                Text("Start tracking your baby's activities to see them here.")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            Button {
                showLogEntrySheet = true
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .semibold))

                    Text("Log First Activity")
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                .background(
                    Capsule()
                        .fill(BabyPulseColors.primary)
                        .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(BabyPulseLayout.paddingXL)
    }

    private func modernErrorView(message: String) -> some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(BabyPulseColors.error.opacity(0.6))

            VStack(spacing: BabyPulseLayout.spacingSM) {
                Text("Something went wrong")
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)

                Text(message)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            Button {
                viewModel.loadEntriesForSelectedDate()
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .semibold))

                    Text("Try Again")
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                .background(
                    Capsule()
                        .fill(BabyPulseColors.primary)
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(BabyPulseLayout.paddingXL)
    }

    // MARK: - Helper Functions

    private func getViewState() -> LogsViewState {
        if viewModel.isLoading {
            return .loading
        } else if let error = viewModel.errorMessage {
            return .error(error)
        } else if viewModel.logEntries.isEmpty {
            return .empty
        } else {
            return .content
        }
    }

    private func formatSelectedDate() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        return formatter.string(from: viewModel.selectedDate)
    }

    private func formatSelectedDateSubtitle() -> String {
        let calendar = Calendar.current
        if calendar.isDateInToday(viewModel.selectedDate) {
            return "Today"
        } else if calendar.isDateInYesterday(viewModel.selectedDate) {
            return "Yesterday"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy"
            return formatter.string(from: viewModel.selectedDate)
        }
    }

    private func detailView(for entry: TimelineEntry) -> some View {
        // Return appropriate detail view based on entry type
        // This would need to be implemented based on your specific entry types
        Text("Detail view for \(entry.id)")
    }

    private func refreshData() async {
        viewModel.loadEntriesForSelectedDate()
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 second delay
    }
}

// MARK: - New Components

// Date Picker Sheet
struct DatePickerView: View {
    @Binding var selectedDate: Date
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                DatePicker(
                    "Select Date",
                    selection: $selectedDate,
                    in: ...Date(),
                    displayedComponents: [.date]
                )
                .datePickerStyle(.graphical)
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                Spacer()
            }
            .navigationTitle("Select Date")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .presentationDetents([.medium])
    }
}

// Optimized Filter View with default activities selected
struct OptimizedFilterView: View {
    @Binding var filters: Set<LogCategory>
    @Environment(\.dismiss) private var dismiss
    @State private var localFilters: Set<LogCategory>

    init(filters: Binding<Set<LogCategory>>) {
        self._filters = filters
        self._localFilters = State(initialValue: filters.wrappedValue.isEmpty ? Set(LogCategory.allCases) : filters.wrappedValue)
    }

    var body: some View {
        NavigationView {
            VStack(spacing: BabyPulseLayout.spacingLG) {
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                    Text("Activity Types")
                        .font(BabyPulseTypography.title3())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.text)

                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: BabyPulseLayout.spacingMD) {
                        ForEach(LogCategory.allCases, id: \.self) { category in
                            FilterToggleCard(
                                category: category,
                                isSelected: localFilters.contains(category)
                            ) {
                                if localFilters.contains(category) {
                                    localFilters.remove(category)
                                } else {
                                    localFilters.insert(category)
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)

                Spacer()
            }
            .navigationTitle("Filter Activities")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Apply") {
                        filters = localFilters
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .presentationDetents([.medium, .large])
    }
}

struct FilterToggleCard: View {
    let category: LogCategory
    let isSelected: Bool
    let onToggle: () -> Void

    var body: some View {
        Button(action: onToggle) {
            VStack(spacing: BabyPulseLayout.spacingSM) {
                Image(systemName: iconForCategory(category))
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(isSelected ? .white : colorForCategory(category))

                Text(category.title)
                    .font(BabyPulseTypography.footnote())
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : BabyPulseColors.text)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(isSelected ? colorForCategory(category) : colorForCategory(category).opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(colorForCategory(category).opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }

    private func colorForCategory(_ category: LogCategory) -> Color {
        switch category {
        case .feeding: return BabyPulseColors.feeding
        case .diaper: return BabyPulseColors.diaper
        case .sleep: return BabyPulseColors.sleep
        case .growth: return BabyPulseColors.growth
        case .health: return BabyPulseColors.health
        default: return BabyPulseColors.generalActivity
        }
    }

    private func iconForCategory(_ category: LogCategory) -> String {
        switch category {
        case .feeding: return "drop.fill"
        case .diaper: return "heart.fill"
        case .sleep: return "moon.fill"
        case .growth: return "arrow.up.right"
        case .health: return "cross.fill"
        default: return "circle.fill"
        }
    }
}

// MARK: - Existing Components (unchanged)

struct ModernTimelineEntryCard: View {
    let entry: TimelineEntry
    let isFirst: Bool
    let isLast: Bool
    let onTap: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: BabyPulseLayout.spacingMD) {
                // Timeline indicator
                VStack(spacing: 0) {
                    // Top line
                    if !isFirst {
                        Rectangle()
                            .fill(BabyPulseColors.mediumGray)
                            .frame(width: 2, height: 20)
                    } else {
                        Color.clear.frame(width: 2, height: 20)
                    }

                    // Center dot
                    Circle()
                        .fill(colorForCategory(entry.category))
                        .frame(width: 12, height: 12)
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )

                    // Bottom line
                    if !isLast {
                        Rectangle()
                            .fill(BabyPulseColors.mediumGray)
                            .frame(width: 2, height: 20)
                    } else {
                        Color.clear.frame(width: 2, height: 20)
                    }
                }

                // Content
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                    HStack {
                        HStack(spacing: 8) {
                            Image(systemName: iconForCategory(entry.category))
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(colorForCategory(entry.category))

                            Text(entry.category.title)
                                .font(BabyPulseTypography.callout())
                                .fontWeight(.semibold)
                                .foregroundColor(BabyPulseColors.text)
                        }

                        Spacer()

                        Text(formatTime(entry.timestamp))
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    if let notes = entry.notes, !notes.isEmpty {
                        Text(notes)
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }

                    // Additional details based on category
                    if let details = formatEntryDetails(entry) {
                        Text(details)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(colorForCategory(entry.category))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(colorForCategory(entry.category).opacity(0.1))
                            )
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(BabyPulseLayout.paddingLG)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func colorForCategory(_ category: LogCategory) -> Color {
        switch category {
        case .feeding: return BabyPulseColors.feeding
        case .diaper: return BabyPulseColors.diaper
        case .sleep: return BabyPulseColors.sleep
        case .growth: return BabyPulseColors.growth
        case .health: return BabyPulseColors.health
        default: return BabyPulseColors.generalActivity
        }
    }

    private func iconForCategory(_ category: LogCategory) -> String {
        switch category {
        case .feeding: return "drop.fill"
        case .diaper: return "heart.fill"
        case .sleep: return "moon.fill"
        case .growth: return "arrow.up.right"
        case .health: return "cross.fill"
        default: return "circle.fill"
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func formatEntryDetails(_ entry: TimelineEntry) -> String? {
        // This would format specific details based on entry type
        // Implementation would depend on your TimelineEntry structure
        return nil
    }
}

struct ModernSkeletonCard: View {
    @State private var isAnimating = false
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            // Timeline indicator placeholder
            VStack(spacing: 0) {
                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 2, height: 20)

                Circle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 12, height: 12)

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 2, height: 20)
            }

            // Content placeholder
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                HStack {
                    Rectangle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 120, height: 16)
                        .cornerRadius(8)

                    Spacer()

                    Rectangle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 60, height: 12)
                        .cornerRadius(6)
                }

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(height: 14)
                    .cornerRadius(7)

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 100, height: 12)
                    .cornerRadius(6)
            }
        }
        .padding(BabyPulseLayout.paddingLG)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
        .onAppear {
            isAnimating = true
        }
    }
}

#Preview {
    LogsView()
        .modelContainer(for: [Baby.self, UserPreferences.self, FeedingEntry.self, DiaperEntry.self, SleepEntry.self, GrowthEntry.self, HealthEntry.self], inMemory: true)
}
