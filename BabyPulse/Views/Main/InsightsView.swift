//
//  InsightsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData
import Combine

struct InsightsView: View {
    @StateObject private var viewModel = InsightsViewModel()
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme

    // Generation manager reference
    @StateObject private var generationManager = InsightGenerationManager.shared

    // State for LLM testing
    @State private var isTestingLLM = false
    @State private var llmTestResult: String? = nil
    @State private var showingLLMTestAlert = false

    // Animation state
    @State private var showGenerationBanner = false

    var body: some View {
        ZStack {
            // Background
            (colorScheme == .dark ? Color.black : Color(hex: "F5F5F7"))
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern header section
                modernHeaderSection
                    .background(.ultraThinMaterial)
                    .zIndex(2)

                // Generation banner (animated)
                if showGenerationBanner {
                    ModernGenerationBanner(state: generationManager.generationState)
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .zIndex(1)
                }

                // Content with better organization
                ZStack {
                    if viewModel.isLoading && viewModel.filteredInsights.isEmpty {
                        modernLoadingView
                    } else if viewModel.filteredInsights.isEmpty {
                        modernEmptyStateView
                    } else {
                        modernInsightsContent
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // Initialize view model and generation manager with proper error handling
            viewModel.modelContext = modelContext

            // Load data first
            Task {
                await viewModel.loadData()

                // Then initialize the generation manager
                generationManager.initialize(with: modelContext)

                // Setup observation of generation state after initialization
                DispatchQueue.main.async {
                    self.setupGenerationStateObserver()
                }
            }
        }
        .sheet(isPresented: $viewModel.showingInsightDetail) {
            if let insight = viewModel.selectedInsight {
                NavigationView {
                    InsightDetailView(insight: insight)
                }
            }
        }
        .sheet(isPresented: $viewModel.showingFilterSheet) {
            ModernInsightsFilterView(
                selectedCategory: $viewModel.selectedCategory,
                timeRange: $viewModel.timeRange,
                sortOption: $viewModel.sortOption,
                onApply: {
                    viewModel.applyFiltersAndSort()
                    viewModel.showingFilterSheet = false
                }
            )
        }
        .alert("LLM Test Result", isPresented: $showingLLMTestAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            if let result = llmTestResult {
                Text(result)
            } else {
                Text("Failed to get response from LLM. Check console for details.")
            }
        }
        .refreshable {
            await viewModel.loadData()
        }
        .alert(item: Binding<AlertItem?>(
            get: { viewModel.errorMessage != nil ? AlertItem(message: viewModel.errorMessage!) : nil },
            set: { _ in viewModel.errorMessage = nil }
        )) { alertItem in
            Alert(
                title: Text("Error"),
                message: Text(alertItem.message),
                dismissButton: .default(Text("OK"))
            )
        }
    }

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Title and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Insights")
                        .font(BabyPulseTypography.title1())
                        .fontWeight(.bold)
                        .foregroundColor(BabyPulseColors.text)

                    Text("Personalized baby analytics")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Spacer()

                HStack(spacing: BabyPulseLayout.spacingMD) {
                    // Generate insights button
                    Button(action: {
                        Task {
                            if let baby = viewModel.currentBaby {
                                await generationManager.manuallyGenerateInsights(for: baby) { _ in }
                            }
                        }
                    }) {
                        Image(systemName: generationManager.generationState.isGenerating ? "wand.and.stars" : "lightbulb.fill")
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(BabyPulseColors.primary)
                            .symbolEffect(.pulse, isActive: generationManager.generationState.isGenerating)
                    }
                    .disabled(generationManager.generationState.isGenerating)

                    // Filter button with indicator
                    Button(action: {
                        viewModel.showingFilterSheet = true
                    }) {
                        ZStack {
                            Image(systemName: "line.3.horizontal.decrease.circle.fill")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(BabyPulseColors.primary)

                            // Active filter indicator
                            if viewModel.selectedCategory != nil {
                                Circle()
                                    .fill(BabyPulseColors.error)
                                    .frame(width: 8, height: 8)
                                    .offset(x: 8, y: -8)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)

            // Quick stats summary
            if !viewModel.filteredInsights.isEmpty {
                modernQuickStats
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
            }
        }
        .padding(.vertical, BabyPulseLayout.paddingMD)
    }

    // MARK: - Modern Quick Stats
    private var modernQuickStats: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            // Total insights
            StatCard(
                icon: "lightbulb.fill",
                title: "Total",
                value: "\(viewModel.filteredInsights.count)",
                color: BabyPulseColors.primary
            )

            // Recent insights
            StatCard(
                icon: "clock.fill",
                title: "Recent",
                value: "\(recentInsightsCount)",
                color: BabyPulseColors.success
            )

            // Categories
            StatCard(
                icon: "square.grid.2x2.fill",
                title: "Categories",
                value: "\(uniqueCategoriesCount)",
                color: BabyPulseColors.info
            )
        }
    }

    // MARK: - Content Views

    private var modernInsightsContent: some View {
        ScrollView {
            LazyVStack(spacing: BabyPulseLayout.spacingLG) {
                // Filter summary if active
                if viewModel.selectedCategory != nil || viewModel.timeRange != .daily {
                    modernFilterSummary
                }

                // Insights grid
                LazyVGrid(
                    columns: [
                        GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD),
                        GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD)
                    ],
                    spacing: BabyPulseLayout.spacingLG
                ) {
                    ForEach(viewModel.filteredInsights, id: \.id) { insight in
                        ModernInsightCardView(insight: insight) {
                            viewModel.selectedInsight = insight
                            viewModel.showingInsightDetail = true
                        }
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity),
                            removal: .scale.combined(with: .opacity)
                        ))
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingLG)

                // Bottom spacing
                Color.clear.frame(height: BabyPulseLayout.spacingLG)
            }
            .padding(.vertical, BabyPulseLayout.paddingMD)
        }
    }

    private var modernFilterSummary: some View {
        VStack(spacing: BabyPulseLayout.spacingSM) {
            HStack {
                Image(systemName: "line.3.horizontal.decrease")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(BabyPulseColors.primary)

                Text("Active Filters")
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)

                Spacer()

                Button("Clear All") {
                    viewModel.selectedCategory = nil
                    viewModel.timeRange = .daily
                    viewModel.applyFiltersAndSort()
                }
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.primary)
            }

            // Filter chips
            HStack(spacing: BabyPulseLayout.spacingSM) {
                if let category = viewModel.selectedCategory {
                    FilterChip(
                        title: category.description,
                        color: BabyPulseColors.primary
                    ) {
                        viewModel.selectedCategory = nil
                        viewModel.applyFiltersAndSort()
                    }
                }

                if viewModel.timeRange != .daily {
                    FilterChip(
                        title: viewModel.timeRange.rawValue,
                        color: BabyPulseColors.info
                    ) {
                        viewModel.timeRange = .daily
                        viewModel.applyFiltersAndSort()
                    }
                }

                Spacer()
            }
        }
        .padding(BabyPulseLayout.paddingLG)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
        .padding(.horizontal, BabyPulseLayout.paddingLG)
    }

    private var modernLoadingView: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            // Skeleton cards
            LazyVGrid(
                columns: [
                    GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD),
                    GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD)
                ],
                spacing: BabyPulseLayout.spacingLG
            ) {
                ForEach(0..<6, id: \.self) { _ in
                    ModernInsightSkeletonCard()
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)
        }
        .padding(.vertical, BabyPulseLayout.paddingLG)
    }

    private var modernEmptyStateView: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            Image(systemName: "lightbulb")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(BabyPulseColors.primary.opacity(0.6))

            VStack(spacing: BabyPulseLayout.spacingSM) {
                Text("No insights yet")
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)

                Text("Log more activities to generate personalized insights for your baby.")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            Button {
                Task {
                    if let baby = viewModel.currentBaby {
                        await generationManager.manuallyGenerateInsights(for: baby) { _ in }
                    }
                }
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "wand.and.stars")
                        .font(.system(size: 16, weight: .semibold))

                    Text("Generate Insights")
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                .background(
                    Capsule()
                        .fill(BabyPulseColors.primary)
                        .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
            .disabled(generationManager.generationState.isGenerating)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(BabyPulseLayout.paddingXL)
    }

    // MARK: - Helper Functions

    private var recentInsightsCount: Int {
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return viewModel.filteredInsights.filter { $0.timestamp >= weekAgo }.count
    }

    private var uniqueCategoriesCount: Int {
        Set(viewModel.filteredInsights.map { $0.category }).count
    }

    private func setupGenerationStateObserver() {
        // Observe generation state changes
        showGenerationBanner = generationManager.generationState.isGenerating
    }

    private func testLLMInteraction() {
        isTestingLLM = true
        llmTestResult = nil

        Task {
            // Your LLM testing logic here
            try? await Task.sleep(nanoseconds: 1_000_000_000) // Simulate async operation

            DispatchQueue.main.async {
                self.isTestingLLM = false
                self.llmTestResult = "LLM test completed successfully"
                self.showingLLMTestAlert = true
            }
        }
    }
}

// MARK: - Modern Components

struct StatCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingSM) {
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(BabyPulseTypography.title3())
                .fontWeight(.bold)
                .foregroundColor(BabyPulseColors.text)

            Text(title)
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(color.opacity(0.1))
        )
    }
}

struct ModernGenerationBanner: View {
    let state: InsightGenerationManager.GenerationState
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: "wand.and.stars")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(BabyPulseColors.primary)
                .symbolEffect(.pulse, isActive: state.isGenerating)

            VStack(alignment: .leading, spacing: 4) {
                Text("Generating Insights")
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)

                Text("Analyzing your baby's data...")
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }

            Spacer()

            if state.isGenerating {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: BabyPulseColors.primary))
            }
        }
        .padding(BabyPulseLayout.paddingLG)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(BabyPulseColors.primary.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .stroke(BabyPulseColors.primary.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, BabyPulseLayout.paddingLG)
    }
}

struct ModernInsightCardView: View {
    let insight: Insight
    let onTap: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    private var categoryColor: Color {
        switch insight.category {
        case .feeding: return BabyPulseColors.feeding
        case .sleep: return BabyPulseColors.sleep
        case .growth: return BabyPulseColors.growth
        case .health: return BabyPulseColors.health
        default: return BabyPulseColors.primary
        }
    }

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(insight.category.description)
                            .font(BabyPulseTypography.caption())
                            .fontWeight(.semibold)
                            .foregroundColor(categoryColor)

                        Text(insight.timestamp, style: .date)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    Spacer()

                    Image(systemName: "lightbulb.fill")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(categoryColor)
                }

                // Content
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                    Text(insight.title)
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.text)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)

                    Text(insight.insightContent)
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .lineLimit(4)
                        .multilineTextAlignment(.leading)
                }

                // Footer
                HStack {
                    Text("Read more")
                        .font(BabyPulseTypography.caption())
                        .fontWeight(.semibold)
                        .foregroundColor(categoryColor)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(categoryColor)
                }
            }
            .padding(BabyPulseLayout.paddingLG)
            .frame(minHeight: 160, alignment: .topLeading)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(categoryColor.opacity(0.2), lineWidth: 1)
                    )
                    .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
            )
        }
        .buttonStyle(ModernInsightButtonStyle())
    }
}

struct ModernInsightSkeletonCard: View {
    @State private var isAnimating = false
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
            // Header skeleton
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Rectangle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 80, height: 12)
                        .cornerRadius(6)

                    Rectangle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 60, height: 10)
                        .cornerRadius(5)
                }

                Spacer()

                Circle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 18, height: 18)
            }

            // Content skeleton
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(height: 16)
                    .cornerRadius(8)

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(height: 14)
                    .cornerRadius(7)

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 120, height: 14)
                    .cornerRadius(7)
            }

            // Footer skeleton
            HStack {
                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 80, height: 12)
                    .cornerRadius(6)

                Spacer()

                Rectangle()
                    .fill(BabyPulseColors.lightGray)
                    .frame(width: 12, height: 12)
                    .cornerRadius(6)
            }
        }
        .padding(BabyPulseLayout.paddingLG)
        .frame(minHeight: 160, alignment: .topLeading)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
        .onAppear {
            isAnimating = true
        }
    }
}

struct FilterChip: View {
    let title: String
    let color: Color
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 6) {
            Text(title)
                .font(BabyPulseTypography.caption())
                .fontWeight(.semibold)

            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.system(size: 10, weight: .bold))
            }
        }
        .foregroundColor(color)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(color.opacity(0.1))
                .overlay(
                    Capsule()
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct ModernInsightButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// Mock filter view - replace with your actual implementation
struct ModernInsightsFilterView: View {
    @Binding var selectedCategory: Insight.InsightCategory?
    @Binding var timeRange: InsightsViewModel.TimeRange
    @Binding var sortOption: InsightsViewModel.SortOption
    let onApply: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Filter & Sort Options")
                    .font(BabyPulseTypography.title2())
                    .padding()

                // Add your filter UI here

                Spacer()

                Button("Apply") {
                    onApply()
                }
                .font(BabyPulseTypography.callout())
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                .background(
                    Capsule()
                        .fill(BabyPulseColors.primary)
                )
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}



#Preview {
    InsightsView()
        .modelContainer(for: [Baby.self, UserPreferences.self, Insight.self], inMemory: true)
}
