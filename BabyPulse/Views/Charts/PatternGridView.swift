//
//  PatternGridView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct PatternGridView: View {
    let data: PatternData
    @Environment(\.colorScheme) private var colorScheme
    @State private var selectedDay: Date? = nil
    @State private var showingPatternDetails = false
    @State private var zoomLevel: ZoomLevel = .normal

    // Constants for grid layout
    private let hourHeight: CGFloat = 16  // Reduced from 24
    private let hourCount = 24

    // Zoom levels for different views
    enum ZoomLevel {
        case compact
        case normal
        case expanded

        var hourHeightMultiplier: CGFloat {
            switch self {
            case .compact: return 0.75
            case .normal: return 1.0
            case .expanded: return 1.5
            }
        }
    }

    // Calculate day width based on time span
    private var dayWidth: CGFloat {
        let baseWidth: CGFloat
        switch data.timeSpan {
        case .daily:
            baseWidth = 36
        case .weekly:
            baseWidth = 36
        case .biweekly:
            baseWidth = 18
        case .monthly:
            baseWidth = 9
        }

        // Apply zoom level
        switch zoomLevel {
        case .compact: return baseWidth * 0.75
        case .normal: return baseWidth
        case .expanded: return baseWidth * 1.5
        }
    }

    // Calculate actual hour height with zoom
    private var actualHourHeight: CGFloat {
        hourHeight * zoomLevel.hourHeightMultiplier
    }

    // Helper method to determine which days to show labels for based on time span
    private func shouldShowDayLabel(for day: Date, at index: Int) -> Bool {
        switch data.timeSpan {
        case .daily:
            return true
        case .weekly:
            return true
        case .biweekly:
            return index % 3 == 0
        case .monthly:
            return index % 5 == 0
        }
    }

    // Detect if a consistent pattern exists for a specific hour range
    private func hasConsistentPattern(forHourRange hour: Int) -> Bool {
        // Get activities that start in this hour range
        let activitiesAtThisHour = data.activities.filter { activity in
            let hourOfDay = Calendar.current.component(.hour, from: activity.startTime)
            return hourOfDay == hour && activity.category == data.category
        }

        // Check if we have activities for at least 70% of the days
        let totalDays = uniqueDays.count
        let daysWithActivityAtThisHour = Set(activitiesAtThisHour.map { Calendar.current.startOfDay(for: $0.date) }).count

        return Double(daysWithActivityAtThisHour) / Double(totalDays) >= 0.7
    }

    // Get pattern summary text
    private var patternSummaryText: String {
        let consistentHours = (0..<24).filter { hasConsistentPattern(forHourRange: $0) }

        if consistentHours.isEmpty {
            return "No consistent pattern detected yet"
        }

        if consistentHours.count > 3 {
            return "Multiple consistent patterns throughout the day"
        }

        return consistentHours.map { hour in
            let date = Calendar.current.date(from: DateComponents(hour: hour))!
            let formatter = DateFormatter()
            formatter.dateFormat = "h a"
            return "Regular \(data.category.title.lowercased()) around \(formatter.string(from: date))"
        }.joined(separator: ", ")
    }

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Header with pattern insights and zoom controls
            VStack(spacing: BabyPulseLayout.spacingSM) {
                HStack {
                    Image(systemName: data.category.icon)
                        .foregroundColor(data.category.color)
                        .font(.system(size: 18))

                    Text("\(data.category.title) Pattern")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)

                    Spacer()

                    // Zoom controls
                    HStack(spacing: 12) {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                zoomLevel = .compact
                            }
                        }) {
                            Image(systemName: "minus.magnifyingglass")
                                .foregroundColor(zoomLevel == .compact ? data.category.color : BabyPulseColors.textSecondary)
                        }

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                zoomLevel = .normal
                            }
                        }) {
                            Image(systemName: "1.magnifyingglass")
                                .foregroundColor(zoomLevel == .normal ? data.category.color : BabyPulseColors.textSecondary)
                        }

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                zoomLevel = .expanded
                            }
                        }) {
                            Image(systemName: "plus.magnifyingglass")
                                .foregroundColor(zoomLevel == .expanded ? data.category.color : BabyPulseColors.textSecondary)
                        }
                    }
                }

                // Pattern insights banner
                HStack {
                    Image(systemName: "sparkles")
                        .foregroundColor(data.category.color)
                        .font(.system(size: 12))

                    Text(patternSummaryText)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .lineLimit(1)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding(.vertical, 6)
                .padding(.horizontal, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(data.category.color.opacity(0.1))
                )
            }

            // Pattern grid
            ScrollView(.vertical) {
                ScrollView(.horizontal, showsIndicators: false) {
                    ZStack(alignment: .topLeading) {
                        // Day/night background
                        VStack(spacing: 0) {
                            ForEach(0..<hourCount, id: \.self) { hour in
                                Rectangle()
                                    .fill(getHourBackgroundColor(hour: hour))
                                    .frame(height: actualHourHeight)
                            }
                        }
                        .padding(.leading, 50)

                        // Background grid
                        VStack(spacing: 0) {
                            ForEach(0..<hourCount, id: \.self) { hour in
                                HStack {
                                    // Hour label
                                    Text(formatHour(hour))
                                        .font(.system(size: 9))
                                        .foregroundColor(BabyPulseColors.textSecondary)
                                        .frame(width: 50, alignment: .trailing)
                                        .lineLimit(1)
                                        .minimumScaleFactor(0.8)

                                    // Horizontal line
                                    Rectangle()
                                        .fill(BabyPulseColors.lightGray.opacity(0.5))
                                        .frame(height: 1)
                                }
                                .frame(height: actualHourHeight)

                                // Pattern indicator for consistent hours
                                .background(
                                    hasConsistentPattern(forHourRange: hour) ?
                                        data.category.color.opacity(0.15) : Color.clear
                                )
                            }
                        }

                        // Day columns
                        HStack(spacing: 0) {
                            // Spacer for hour labels
                            Rectangle()
                                .fill(Color.clear)
                                .frame(width: 50)

                            // Day columns
                            ForEach(Array(uniqueDays.enumerated()), id: \.element) { index, day in
                                VStack {
                                    // Day label - only show for selected days based on time span
                                    if shouldShowDayLabel(for: day, at: index) {
                                        Text(formatDay(day))
                                            .font(.system(size: getFontSize(for: data.timeSpan)))
                                            .foregroundColor(BabyPulseColors.textSecondary)
                                            .frame(height: 16)
                                            .lineLimit(1)
                                            .minimumScaleFactor(0.7)
                                    } else {
                                        // Empty space to maintain layout
                                        Color.clear
                                            .frame(height: 16)
                                    }

                                    // Vertical line
                                    Rectangle()
                                        .fill(BabyPulseColors.lightGray.opacity(0.5))
                                        .frame(width: 1, height: CGFloat(hourCount) * actualHourHeight)
                                }
                                .frame(width: dayWidth)
                                .contentShape(Rectangle())
                                .onTapGesture {
                                    selectedDay = day
                                    showingPatternDetails = true
                                }
                                .background(
                                    selectedDay == day ?
                                        data.category.color.opacity(0.1) : Color.clear
                                )
                            }
                        }

                        // Activity blocks
                        ForEach(data.activities.indices, id: \.self) { index in
                            let activity = data.activities[index]
                            if let dayIndex = dayIndex(for: activity.date),
                               let startHour = hourFraction(from: activity.startTime),
                               let duration = activity.duration {

                                let durationHours = duration / 3600 // Convert seconds to hours

                                // Calculate block width based on time span
                                let blockWidth = max(dayWidth - 2, 3) // Ensure minimum width of 3 with smaller margin

                                // Calculate minimum height based on time span
                                let minHeight: CGFloat = data.timeSpan == .monthly ? 6 : 8

                                // Identify consecutive activities to highlight patterns
                                let isPartOfPattern = isActivityPartOfPattern(index)

                                ZStack(alignment: .center) {
                                    Rectangle()
                                        .fill(activity.category.color.opacity(isPartOfPattern ? 0.9 : 0.7))
                                        .frame(
                                            width: blockWidth,
                                            height: max(actualHourHeight * CGFloat(durationHours), minHeight)
                                        )

                                    // Add subtle glow if part of pattern
                                    if isPartOfPattern {
                                        Rectangle()
                                            .fill(activity.category.color)
                                            .frame(
                                                width: blockWidth,
                                                height: max(actualHourHeight * CGFloat(durationHours), minHeight)
                                            )
                                            .blur(radius: 3)
                                            .opacity(0.3)
                                    }
                                }
                                .position(
                                    x: 50 + dayWidth * CGFloat(dayIndex) + dayWidth / 2,
                                    y: actualHourHeight * CGFloat(startHour) + (actualHourHeight * CGFloat(durationHours) / 2)
                                )
                                .onTapGesture {
                                    // Show activity details
                                }
                            }
                        }
                    }
                    .frame(
                        width: 50 + (dayWidth * CGFloat(uniqueDays.count)),
                        height: CGFloat(hourCount) * actualHourHeight + 16
                    )
                    .padding(.top, BabyPulseLayout.paddingSM)
                }
            }
            .frame(maxHeight: 384)
            .overlay(
                Group {
                    if uniqueDays.isEmpty {
                        VStack {
                            Image(systemName: "calendar.badge.clock")
                                .font(.system(size: 30))
                                .foregroundColor(data.category.color.opacity(0.5))
                                .padding(.bottom, 8)

                            Text("Not enough data to show patterns")
                                .font(BabyPulseTypography.body())
                                .foregroundColor(BabyPulseColors.textSecondary)

                            Text("Add more \(data.category.title.lowercased()) entries to see patterns emerge")
                                .font(BabyPulseTypography.caption())
                                .foregroundColor(BabyPulseColors.textTertiary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 20)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(colorScheme == .dark ? Color.black.opacity(0.3) : Color.white.opacity(0.7))
                    }
                }
            )
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .sheet(isPresented: $showingPatternDetails) {
            patternDetailView
        }
    }

    // Color background by day/night cycle
    private func getHourBackgroundColor(hour: Int) -> Color {
        if (6...18).contains(hour) {
            // Daytime (6am-6pm) - very subtle light
            return colorScheme == .dark ?
                Color(hex: "2D2D2F") : Color(hex: "F8F8FA")
        } else {
            // Nighttime - slightly darker
            return colorScheme == .dark ?
                Color(hex: "252527") : Color(hex: "F0F0F2")
        }
    }

    // Check if an activity forms part of a consistent pattern
    private func isActivityPartOfPattern(_ index: Int) -> Bool {
        let activity = data.activities[index]
        let hourOfDay = Calendar.current.component(.hour, from: activity.startTime)

        return hasConsistentPattern(forHourRange: hourOfDay)
    }

    // Detail view for a specific day's pattern
    @ViewBuilder
    private var patternDetailView: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Header
            HStack {
                Button(action: {
                    showingPatternDetails = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .font(.system(size: 24))
                }

                Spacer()

                Group {
                    if let selectedDay = selectedDay {
                        Text(selectedDay, style: .date)
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.secondary)
                    } else {
                        Text("Select a day")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }
                }

                Spacer()

                Color.clear
                    .frame(width: 24, height: 24)
            }
            .padding()

            Group {
                if let selectedDay = selectedDay {
                    let dayActivities = activitiesForDay(selectedDay)

                    if dayActivities.isEmpty {
                        VStack(spacing: BabyPulseLayout.spacingMD) {
                            Image(systemName: data.category.icon)
                                .font(.system(size: 40))
                                .foregroundColor(data.category.color.opacity(0.3))
                                .padding(.bottom)

                            Text("No \(data.category.title.lowercased()) activities recorded")
                                .font(BabyPulseTypography.body())
                                .foregroundColor(BabyPulseColors.textSecondary)

                            Text("Try another day or add more entries")
                                .font(BabyPulseTypography.caption())
                                .foregroundColor(BabyPulseColors.textTertiary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        List {
                            Section(
                                header: Text("\(data.category.title) Activities")
                                    .font(BabyPulseTypography.footnote())
                                    .foregroundColor(data.category.color)
                            ) {
                                ForEach(dayActivities) { activity in
                                    HStack {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(formatActivityTime(activity.startTime))
                                                .font(BabyPulseTypography.bodyBold())
                                                .foregroundColor(BabyPulseColors.secondary)

                                            if let duration = activity.duration {
                                                Text(formatDuration(duration))
                                                    .font(BabyPulseTypography.caption())
                                                    .foregroundColor(BabyPulseColors.textTertiary)
                                            }
                                        }

                                        Spacer()

                                        if isActivityPartOfPattern(dayActivities.firstIndex(of: activity) ?? 0) {
                                            Image(systemName: "repeat")
                                                .foregroundColor(data.category.color)
                                                .font(.system(size: 14))
                                        }
                                    }
                                    .padding(.vertical, 4)
                                }
                            }

                            // Pattern insights
                            Section(
                                header: Text("Pattern Insights")
                                    .font(BabyPulseTypography.footnote())
                                    .foregroundColor(BabyPulseColors.primary)
                            ) {
                                if dayPatternInsights(for: selectedDay).isEmpty {
                                    HStack {
                                        Image(systemName: "info.circle")
                                            .foregroundColor(BabyPulseColors.textTertiary)

                                        Text("Keep logging to discover patterns")
                                            .font(BabyPulseTypography.body())
                                            .foregroundColor(BabyPulseColors.textSecondary)
                                    }
                                } else {
                                    ForEach(dayPatternInsights(for: selectedDay), id: \.self) { insight in
                                        HStack {
                                            Image(systemName: "sparkles")
                                                .foregroundColor(data.category.color)

                                            Text(insight)
                                                .font(BabyPulseTypography.body())
                                                .foregroundColor(BabyPulseColors.textSecondary)
                                        }
                                    }
                                }
                            }
                        }
                        .listStyle(InsetGroupedListStyle())
                    }
                } else {
                    // Add a default view when selectedDay is nil
                    VStack {
                        Text("Select a day to view details")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }

            Spacer()
        }
    }

    // Filter activities for a specific day
    private func activitiesForDay(_ day: Date) -> [PatternData.PatternActivity] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: day)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!

        return data.activities.filter { activity in
            activity.startTime >= startOfDay && activity.startTime < endOfDay
        }
    }

    // Generate pattern insights for a specific day
    private func dayPatternInsights(for day: Date) -> [String] {
        var insights: [String] = []
        let dayActivities = activitiesForDay(day)

        if dayActivities.isEmpty {
            return insights
        }

        // Check if activities on this day match common patterns
        let hourOfDayActivities = dayActivities.map { Calendar.current.component(.hour, from: $0.startTime) }

        // Look for patterns across all data
        for hour in 0..<24 {
            if hasConsistentPattern(forHourRange: hour) && hourOfDayActivities.contains(hour) {
                let date = Calendar.current.date(from: DateComponents(hour: hour))!
                let formatter = DateFormatter()
                formatter.dateFormat = "h a"
                insights.append("Consistent \(data.category.title.lowercased()) around \(formatter.string(from: date))")
            }
        }

        // Add more insights based on specific patterns for each category
        if data.category == .sleep {
            if let longestSleep = dayActivities.max(by: { ($0.duration ?? 0) < ($1.duration ?? 0) }) {
                if let duration = longestSleep.duration, duration > 7200 { // More than 2 hours
                    insights.append("Longer sleep period: \(formatDuration(duration))")
                }
            }
        }

        return insights
    }

    // Format duration for display
    private func formatDuration(_ seconds: TimeInterval) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    // Format activity time
    private func formatActivityTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        return formatter.string(from: date)
    }

    // Get unique days from activities
    private var uniqueDays: [Date] {
        let calendar = Calendar.current
        let days = data.activities.map { calendar.startOfDay(for: $0.date) }
        return Array(Set(days)).sorted()
    }

    // Get index of day in uniqueDays array
    private func dayIndex(for date: Date) -> Int? {
        let calendar = Calendar.current
        let day = calendar.startOfDay(for: date)
        return uniqueDays.firstIndex(of: day)
    }

    // Convert time to hour fraction (e.g., 1:30 PM = 13.5)
    private func hourFraction(from date: Date) -> Double? {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        return Double(hour) + Double(minute) / 60.0
    }

    // Format hour for display (e.g., "1 PM")
    private func formatHour(_ hour: Int) -> String {
        let formatter = DateFormatter()

        // Use more concise format for monthly view
        if data.timeSpan == .monthly {
            formatter.dateFormat = "ha"
        } else {
            formatter.dateFormat = "h a"
        }

        let calendar = Calendar.current
        var components = DateComponents()
        components.hour = hour

        if let date = calendar.date(from: components) {
            return formatter.string(from: date)
        }

        return "\(hour)"
    }

    // Format day for display (e.g., "1", "15", "30")
    private func formatDay(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"  // Just day number
        return formatter.string(from: date)
    }

    // Get appropriate font size based on time span
    private func getFontSize(for timeSpan: TimeSpan) -> CGFloat {
        switch timeSpan {
        case .daily, .weekly:
            return 9
        case .biweekly:
            return 8
        case .monthly:
            return 7
        }
    }
}
