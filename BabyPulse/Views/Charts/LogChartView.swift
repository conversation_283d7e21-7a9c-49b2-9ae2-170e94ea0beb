//
//  LogChartView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import Charts

struct LogChartView: View {
    @ObservedObject var viewModel: LogsViewModel
    @Environment(\.colorScheme) private var colorScheme

    // Initialize with check to ensure health category is not selected
    init(viewModel: LogsViewModel) {
        self.viewModel = viewModel

        // If health category is selected, reset to nil (All)
        if viewModel.selectedChartCategory == .health {
            viewModel.selectedChartCategory = nil
        }
        // If general category is selected, reset to nil (All)
        if viewModel.selectedChartCategory == .general { // Assuming .general is a case in LogCategory
            viewModel.selectedChartCategory = nil
        }
    }

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Date range display with navigation controls
            HStack {
                // Back button
                Button(action: {
                    withAnimation {
                        viewModel.navigateChartPrevious()
                    }
                    
                    // Add haptic feedback
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color(UIColor.tertiarySystemBackground))
                                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                        )
                }
                
                Spacer()
                
                // Date range
                Button {
                    // Reset to current period
                    withAnimation {
                        viewModel.resetChartPeriodToCurrent()
                        viewModel.loadDataForCharts()
                    }
                } label: {
                    Text(dateRangeText)
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.secondary)
                }

                Spacer()
                
                // Forward button - disabled when at current date
                Button(action: {
                    withAnimation {
                        viewModel.navigateChartNext()
                    }
                    
                    // Add haptic feedback
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()
                }) {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(viewModel.canNavigateChartForward ? BabyPulseColors.primary : BabyPulseColors.textTertiary)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color(UIColor.tertiarySystemBackground))
                                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                        )
                }
                .disabled(!viewModel.canNavigateChartForward)
            }
            .padding(.horizontal, BabyPulseLayout.paddingMD)

            // Category selector - exclude health category
            CategorySelectorView(
                selectedCategory: $viewModel.selectedChartCategory,
                onCategoryChanged: { _ in },
                excludedCategories: [.health, .general]
            )

            // Chart content
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .padding()
                    .frame(maxHeight: .infinity)
            } else if viewModel.isAllChartDataEmpty {
                VStack {
                    Spacer()
                    Image(systemName: "chart.bar.xaxis.ascending.badge.clock")
                        .font(.system(size: 50))
                        .foregroundColor(BabyPulseColors.mediumGray)
                        .padding(.bottom, BabyPulseLayout.spacingSM)
                    Text("No Data Available")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.textSecondary)
                    Text("There is no data to display for the selected period and filters. Try adjusting the date range or filters.")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textTertiary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, BabyPulseLayout.paddingLG)
                    Spacer()
                }
                .frame(maxHeight: .infinity)
            } else {
                ScrollView {
                    VStack(spacing: BabyPulseLayout.spacingLG) {
                        // Show appropriate chart based on selected category
                        if let category = viewModel.selectedChartCategory {
                            // Show category-specific charts
                            if category == .feeding, let feedingData = viewModel.feedingChartData {
                                FeedingChartView(data: feedingData)
                            }

                            if category == .sleep, let sleepData = viewModel.sleepChartData {
                                SleepChartView(data: sleepData)
                            }

                            if category == .diaper, let diaperData = viewModel.diaperChartData {
                                DiaperChartView(data: diaperData)
                            }

                            if category == .growth {
                                // Show all growth charts stacked vertically
                                if let weightData = viewModel.weightChartData {
                                    WeightChartView(data: weightData)
                                }

                                if let heightData = viewModel.heightChartData {
                                    HeightChartView(data: heightData)
                                }

                                if let headCircumferenceData = viewModel.headCircumferenceChartData {
                                    HeadCircumferenceChartView(data: headCircumferenceData)
                                }
                            }

                            // Show pattern grid only for specific categories (not for growth)
                            if category != .growth, let patternData = viewModel.patternData(for: category) {
                                PatternGridView(data: patternData)
                            }
                        } else {
                            // Show all category charts but no pattern when "All" is selected
                            if let feedingData = viewModel.feedingChartData {
                                FeedingChartView(data: feedingData)
                            }

                            if let sleepData = viewModel.sleepChartData {
                                SleepChartView(data: sleepData)
                            }

                            if let diaperData = viewModel.diaperChartData {
                                DiaperChartView(data: diaperData)
                            }

                            // Show growth charts when "All" is selected
                            if let weightData = viewModel.weightChartData {
                                WeightChartView(data: weightData)
                            }

                            if let heightData = viewModel.heightChartData {
                                HeightChartView(data: heightData)
                            }

                            if let headCircumferenceData = viewModel.headCircumferenceChartData {
                                HeadCircumferenceChartView(data: headCircumferenceData)
                            }
                        }
                    }
                    .padding(BabyPulseLayout.paddingMD)
                }
            }
        }
    }

    private var dateRangeText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd"

        return "\(formatter.string(from: viewModel.chartStartDate)) - \(formatter.string(from: viewModel.chartEndDate))"
    }
}
