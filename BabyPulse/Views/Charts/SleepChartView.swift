//
//  SleepChartView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import Charts

// Helper functions for chart configuration
private func getAxisLabelCount(for timeSpan: TimeSpan) -> Int {
    switch timeSpan {
    case .daily:
        return 6  // Show 6 labels for daily view
    case .weekly:
        return 7  // Show all 7 days for weekly view
    case .biweekly:
        return 7  // Show 7 labels for biweekly view (every other day)
    case .monthly:
        return 8  // Show 8 labels for monthly view (roughly every 4 days)
    }
}

private func shouldShowLabel(at index: Int, in dataPoints: [ChartDataPoint], for timeSpan: TimeSpan) -> Bool {
    switch timeSpan {
    case .daily:
        return true  // Show all labels for daily view
    case .weekly:
        return true  // Show all labels for weekly view
    case .biweekly:
        return index % 2 == 0  // Show every other label for biweekly view
    case .monthly:
        return index % 4 == 0  // Show every fourth label for monthly view
    }
}

private func getBarWidth(for timeSpan: TimeSpan) -> CGFloat {
    switch timeSpan {
    case .daily:
        return 20  // Wider bars for daily view (few bars)
    case .weekly:
        return 12  // Medium width for weekly view (7 bars)
    case .biweekly:
        return 8   // Narrower for biweekly view (14 bars)
    case .monthly:
        return 6   // Narrowest for monthly view (30 bars)
    }
}

struct SleepChartView: View {
    let data: SleepChartData
    @Environment(\.colorScheme) private var colorScheme

    // Header view
    private var headerView: some View {
        HStack {
            Image(systemName: "moon.fill")
                .foregroundColor(BabyPulseColors.sleep)
                .font(.system(size: 18))

            Text("Sleep")
                .font(BabyPulseTypography.title3())
                .foregroundColor(BabyPulseColors.secondary)

            Spacer()
        }
    }

    // Stats summary view
    private var statsSummaryView: some View {
        HStack(spacing: BabyPulseLayout.spacingLG) {
            // Average
            VStack(alignment: .leading, spacing: 4) {
                Text("Average")
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.textSecondary)

                HStack(alignment: .firstTextBaseline, spacing: 4) {
                    Text(data.formattedAverage)
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.sleep)

                    Text("hrs")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                if let percentChange = data.percentChange {
                    let arrowImageName = percentChange >= 0 ? "arrow.up" : "arrow.down"

                    HStack(spacing: 2) {
                        Image(systemName: arrowImageName)
                            .font(.system(size: 10))

                        Text(data.formattedPercentChange)
                            .font(BabyPulseTypography.caption())
                    }
                    .foregroundColor(data.percentChangeColor)
                }
            }

            Spacer()

            // Min
            VStack(alignment: .center, spacing: 4) {
                Text("Min")
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.textSecondary)

                Text(data.formattedMinimum)
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.sleep)
            }

            Spacer()

            // Max
            VStack(alignment: .center, spacing: 4) {
                Text("Max")
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.textSecondary)

                Text(data.formattedMaximum)
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.sleep)
            }
        }
        .padding(.horizontal, BabyPulseLayout.paddingSM)
    }

    // Chart content view
    private var chartContent: some View {
        Chart {
            ForEach(data.dataPoints) { point in
                BarMark(
                    x: .value("Day", point.formattedDate),
                    y: .value("Hours", point.value),
                    width: .fixed(getBarWidth(for: data.timeSpan))
                )
                .foregroundStyle(BabyPulseColors.sleep.gradient)
                .cornerRadius(4)
            }

            if data.average > 0 {
                RuleMark(y: .value("Average", data.average))
                    .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                    .foregroundStyle(BabyPulseColors.sleep.opacity(0.5))
                    .annotation(position: .top, alignment: .trailing) {
                        averageAnnotation
                    }
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic(desiredCount: getAxisLabelCount(for: data.timeSpan))) { value in
                if let stringValue = value.as(String.self),
                   let index = data.dataPoints.firstIndex(where: { $0.formattedDate == stringValue }) {

                    if shouldShowLabel(at: index, in: data.dataPoints, for: data.timeSpan) {
                        AxisValueLabel {
                            Text(stringValue)
                                .font(.system(size: 10))
                        }
                    } else {
                        AxisValueLabel {
                            Text("")
                        }
                    }

                    AxisGridLine()
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading)
        }
        .chartYAxisLabel("Hours")
        .frame(height: 220)
        .padding(.top, BabyPulseLayout.paddingSM)
    }

    // Empty data view
    private var emptyDataView: some View {
        VStack {
            Spacer()
            Image(systemName: "moon.fill") // Icon for sleep
                .font(.system(size: 40))
                .foregroundColor(BabyPulseColors.sleep.opacity(0.6))
                .padding(.bottom, BabyPulseLayout.spacingXS)
            Text("No Sleep Data")
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.textSecondary)
            Text("No sleep entries logged for this period.")
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textTertiary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            Spacer()
        }
        .frame(height: 220) // Match chart content height for consistency
        .padding(.top, BabyPulseLayout.paddingSM)
    }

    // Average annotation view
    private var averageAnnotation: some View {
        Text("Avg: \(data.formattedAverage) hrs")
            .font(BabyPulseTypography.caption())
            .foregroundColor(BabyPulseColors.textSecondary)
            .padding(.horizontal, 4)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(colorScheme == .dark ? Color(hex: "2C2C2E") : Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
            )
    }

    // Background view
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
            .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Header
            headerView

            if data.dataPoints.isEmpty {
                emptyDataView
            } else {
                // Stats summary
                statsSummaryView

                // Chart
                chartContent
            }
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(backgroundView)
    }
}
