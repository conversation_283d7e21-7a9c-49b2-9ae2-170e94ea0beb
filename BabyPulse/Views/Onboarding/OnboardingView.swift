//
//  OnboardingView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct OnboardingView: View {
    @StateObject private var viewModel = OnboardingViewModel()
    @Binding var onboardingCompleted: Bool
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme

    // For animations
    @State private var isLoaded = false

    var body: some View {
        ZStack {
            // Background with subtle animated gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    BabyPulseColors.primary.opacity(0.05),
                    Color.white,
                    BabyPulseColors.primary.opacity(0.08)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .hueRotation(Angle(degrees: isLoaded ? 10 : 0))
            .animation(.easeInOut(duration: 5.0).repeatForever(autoreverses: true), value: isLoaded)

            ScrollView {
                VStack(spacing: 0) {
                    // Header with logo
                    HStack {
                        if viewModel.currentStep != .welcome {
                            Button(action: {
                                withAnimation {
                                    viewModel.moveToPreviousStep()
                                }
                            }) {
                                Image(systemName: "chevron.left")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(BabyPulseColors.primary)
                                    .padding(12)
                                    .background(Circle().fill(Color.white.opacity(0.8)))
                                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                            }
                            .padding(.leading, BabyPulseLayout.paddingMD)
                            .accessibilityLabel("Go back")
                        } else {
                            Spacer()
                                .frame(width: 60)
                        }

                        Spacer()

                        // Logo or branding
                        Image(systemName: "heart.fill")
                            .foregroundColor(BabyPulseColors.primary)
                            .font(.system(size: 22))
                            .padding(8)
                            .background(
                                Circle()
                                    .fill(Color.white)
                                    .shadow(color: BabyPulseColors.primary.opacity(0.2), radius: 8, x: 0, y: 4)
                            )

                        Spacer()

                        if viewModel.currentStep != .welcome {
                            // Skip button on non-welcome pages
                            Button("Skip") {
                                // Show confirmation dialog
                                // For now just complete onboarding
                                viewModel.completeOnboarding(modelContext: modelContext)
                                onboardingCompleted = true
                            }
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .padding(.trailing, BabyPulseLayout.paddingMD)
                            .accessibilityLabel("Skip onboarding")
                        } else {
                            Spacer()
                                .frame(width: 60)
                        }
                    }
                    .padding(.top, BabyPulseLayout.paddingMD)
                    .padding(.bottom, BabyPulseLayout.paddingSM)

                    // Progress indicator (only for steps after welcome)
                    if viewModel.currentStep != .welcome {
                        // Enhanced progress indicator with labels
                        VStack(spacing: 4) {
                            HStack(spacing: 8) {
                                ForEach(OnboardingStep.allCases, id: \.rawValue) { step in
                                    if step != .welcome {
                                        let isActive = viewModel.currentStep.rawValue >= step.rawValue
                                        let isCurrent = viewModel.currentStep == step

                                        Circle()
                                            .fill(isActive ? BabyPulseColors.primary : BabyPulseColors.mediumGray)
                                            .frame(width: 8, height: 8)
                                            .overlay(
                                                Circle()
                                                    .stroke(BabyPulseColors.primary.opacity(0.3), lineWidth: isCurrent ? 4 : 0)
                                                    .frame(width: 16, height: 16)
                                                    .opacity(isCurrent ? 1 : 0)
                                            )
                                            .scaleEffect(isCurrent ? 1.2 : 1.0)
                                            .accessibilityHidden(true)
                                    }
                                }
                            }

                            // Step label
                            Text(stepTitle)
                                .font(BabyPulseTypography.caption())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .accessibilityLabel("Current step: \(stepTitle)")
                        }
                        .padding(.top, BabyPulseLayout.paddingSM)
                        .padding(.bottom, BabyPulseLayout.paddingSM)
                        .frame(maxWidth: .infinity, alignment: .center)
                    }

                    // Current step view
                    ZStack {
                        switch viewModel.currentStep {
                        case .welcome:
                            WelcomeStepView(viewModel: viewModel)
                                .transition(.asymmetric(
                                    insertion: .opacity,
                                    removal: .move(edge: .leading).combined(with: .opacity)
                                ))
                        case .babyProfile:
                            BabyProfileStepView(viewModel: viewModel)
                                .transition(.asymmetric(
                                    insertion: .move(edge: .trailing).combined(with: .opacity),
                                    removal: .opacity)
                                )
                        }
                    }
                    .padding(.bottom, BabyPulseLayout.paddingLG)
                }
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: viewModel.currentStep)
            }
            .scrollIndicators(.hidden)
        }
        .onAppear {
            isLoaded = true

            // Set up finish handler
            viewModel.finishOnboarding = {
                viewModel.completeOnboarding(modelContext: modelContext)
                onboardingCompleted = true
            }
        }
    }

    private var stepTitle: String {
        switch viewModel.currentStep {
        case .welcome:
            return "Welcome"
        case .babyProfile:
            return "Profile"
        }
    }
}

#Preview {
    OnboardingView(onboardingCompleted: .constant(false))
        .modelContainer(for: [Baby.self, UserPreferences.self], inMemory: true)
}
