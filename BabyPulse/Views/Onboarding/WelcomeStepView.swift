//
//  WelcomeStepView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct WelcomeStepView: View {
    @ObservedObject var viewModel: OnboardingViewModel
    @State private var isAnimating = false
    @State private var showingVideo = false
    
    // Animation properties
    @State private var heartScale: CGFloat = 1.0
    @State private var heartRotation: Double = 0
    @State private var bubbleOpacity: Double = 0.0
    
    private let taglines = [
        "AI-powered insights for your baby's journey",
        "Transform data into personalized guidance", 
        "The smartest baby tracking companion"
    ]
    @State private var currentTagline = 0
    @State private var taglineOpacity = 1.0
    
    var body: some View {
        ZStack {
            // Background gradient with animated elements
            ZStack {
                // Main background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white,
                        BabyPulseColors.primary.opacity(0.1),
                        BabyPulseColors.primary.opacity(0.05)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                // Animated decorative elements
                VStack {
                    HStack {
                        Circle()
                            .fill(BabyPulseColors.feeding.opacity(0.2))
                            .frame(width: 80, height: 80)
                            .offset(x: isAnimating ? 5 : -5, y: isAnimating ? -10 : 10)
                        
                        Spacer()
                    }
                    
                    Spacer()
                    
                    HStack {
                        Spacer()
                        
                        Circle()
                            .fill(BabyPulseColors.sleep.opacity(0.15))
                            .frame(width: 100, height: 100)
                            .offset(x: isAnimating ? -15 : 15, y: isAnimating ? 8 : -8)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 80)
                .animation(Animation.easeInOut(duration: 8).repeatForever(autoreverses: true), value: isAnimating)
            }
            
            // Main content
            VStack(spacing: BabyPulseLayout.spacingLG) {
                Spacer()
                
                // Logo and animation
                VStack(spacing: 24) {
                    ZStack {
                        // Outer circles
                        ForEach(0..<3) { i in
                            Circle()
                                .stroke(BabyPulseColors.primary.opacity(Double(3-i) * 0.1), lineWidth: 1)
                                .frame(width: CGFloat(140 + (i * 30)), height: CGFloat(140 + (i * 30)))
                                .scaleEffect(bubbleOpacity)
                        }
                        
                        // Main circle
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        BabyPulseColors.primary.opacity(0.7),
                                        BabyPulseColors.primary
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 80
                                )
                            )
                            .frame(width: 120, height: 120)
                            .shadow(color: BabyPulseColors.primary.opacity(0.4), radius: 15, x: 0, y: 10)
                        
                        // Icon
                        Image(systemName: "figure.and.child.holdinghands")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 65, height: 65)
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(heartRotation))
                            .scaleEffect(heartScale)
                    }
                    .accessibilityHidden(true)
                    
                    // App name with animation
                    Text("BabyPulse")
                        .font(.system(size: 38, weight: .bold, design: .rounded))
                        .foregroundColor(BabyPulseColors.secondary)
                        .tracking(1.5)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .padding(.bottom, BabyPulseLayout.spacingMD)
                
                // Animated tagline
                Text(taglines[currentTagline])
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .frame(height: 50)
                    .opacity(taglineOpacity)
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                    .accessibilityLabel("Tagline: \(taglines[currentTagline])")
                
                // Feature highlights (more visually appealing)
                VStack(spacing: BabyPulseLayout.spacingSM) {
                    FeatureHighlight(
                        icon: "brain.head.profile",
                        title: "AI-Powered Insights",
                        description: "Get personalized advice based on your baby's unique patterns",
                        color: BabyPulseColors.primary
                    )
                    
                    FeatureHighlight(
                        icon: "waveform.path.ecg",
                        title: "Pattern Recognition",
                        description: "Discover hidden connections in your baby's activities",
                        color: BabyPulseColors.feeding
                    )
                    
                    FeatureHighlight(
                        icon: "message.badge.waveform",
                        title: "Smart Chat Assistant",
                        description: "Ask questions and get answers tailored to your data",
                        color: BabyPulseColors.sleep
                    )
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                
                Spacer()
                
                // Video demo button
                Button(action: {
                    showingVideo = true
                }) {
                    HStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 16))
                        Text("Watch how it works")
                    }
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.primary)
                    .padding(.vertical, 8)
                }
                .padding(.bottom, BabyPulseLayout.spacingMD)
                .accessibilityLabel("Watch demonstration video")
                
                // Get started button
                BabyPulseButton(
                    title: "Get Started",
                    icon: "arrow.right",
                    style: .primary,
                    size: .large,
                    isFullWidth: true,
                    action: {
                        withAnimation {
                            viewModel.moveToNextStep()
                        }
                    }
                )
                .accessibilityHint("Begin setting up your baby's profile")
                .padding(.horizontal, BabyPulseLayout.paddingMD)
                .padding(.bottom, BabyPulseLayout.paddingLG)
            }
            .ignoresSafeArea(edges: .bottom)
        }
        .onAppear {
            // Start animations
            isAnimating = true
            startAnimations()
            
            // Start tagline rotation
            Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
                withAnimation(.easeInOut(duration: 0.7)) {
                    taglineOpacity = 0
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
                    currentTagline = (currentTagline + 1) % taglines.count
                    withAnimation(.easeInOut(duration: 0.7)) {
                        taglineOpacity = 1.0
                    }
                }
            }
        }
        .sheet(isPresented: $showingVideo) {
            // Video preview sheet
            AppDemoView()
                .presentationDetents([.medium, .large])
        }
    }
    
    private func startAnimations() {
        // Heart animation
        withAnimation(Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            heartScale = 1.08
        }
        
        // Subtle rotation
        withAnimation(Animation.easeInOut(duration: 8).repeatForever(autoreverses: true)) {
            heartRotation = 5
        }
        
        // Bubble animation
        withAnimation(Animation.easeOut(duration: 1.2)) {
            bubbleOpacity = 1.0
        }
    }
}

// Feature highlight component with improved visuals
struct FeatureHighlight: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [color, color.opacity(0.7)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 46, height: 46)
                    .shadow(color: color.opacity(0.3), radius: 4, x: 0, y: 2)
                
                Image(systemName: icon)
                    .font(.system(size: 22, weight: .medium))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.text)
                
                Text(description)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(BabyPulseColors.mediumGray)
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(description)")
    }
}

// Video preview component
struct AppDemoView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Header
            HStack {
                Text("App Preview")
                    .font(BabyPulseTypography.title2())
                    .foregroundColor(BabyPulseColors.secondary)
                
                Spacer()
                
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)
            .padding(.top, BabyPulseLayout.paddingLG)
            
            // Placeholder for video/screenshot carousel
            TabView {
                ForEach(1...3, id: \.self) { index in
                    VStack {
                        ZStack {
                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                                .fill(BabyPulseColors.primary.opacity(0.1))
                                .aspectRatio(9/16, contentMode: .fit)
                                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                            
                            // Here you would show actual app screenshots
                            VStack(spacing: 12) {
                                Image(systemName: ["chart.xyaxis.line", "square.grid.3x3.square", "figure.and.child.holdinghands"][index-1])
                                    .font(.system(size: 40))
                                    .foregroundColor(BabyPulseColors.primary)
                                
                                Text(["Analytics Dashboard", "Activity Tracking", "Smart Assistant"][index-1])
                                    .font(BabyPulseTypography.bodyBold())
                                    .foregroundColor(BabyPulseColors.text)
                            }
                        }
                        
                        Text(["View trends and insights at a glance", "Log activities with just a few taps", "Get personalized advice for your baby"][index-1])
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 40)
                            .padding(.top, 8)
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                }
            }
            .tabViewStyle(PageTabViewStyle())
            .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
            
            // Call to action
            VStack {
                Text("Create your account to experience these features and more")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, BabyPulseLayout.paddingMD)
                    .padding(.top, BabyPulseLayout.paddingMD)
                
                Button(action: { dismiss() }) {
                    Text("Back to sign up")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.primary)
                        .padding(.vertical, 8)
                }
                .padding(.bottom, BabyPulseLayout.paddingLG)
            }
        }
    }
}

#Preview {
    WelcomeStepView(viewModel: OnboardingViewModel())
}
