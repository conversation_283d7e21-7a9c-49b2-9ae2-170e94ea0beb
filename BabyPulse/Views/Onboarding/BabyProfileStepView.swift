//
//  BabyProfileStepView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import PhotosUI

struct BabyProfileStepView: View {
    @ObservedObject var viewModel: OnboardingViewModel
    @State private var isAnimating = false
    @State private var isShowingAvatarPicker = false
    @FocusState private var isNameFieldFocused: Bool
    
    var body: some View {
        ZStack {
            // Background
            Color(hex: "F9FAFB")
                .ignoresSafeArea()

            // Content
            ScrollView {
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingLG) {
                    // Header
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                        Text("Baby Profile")
                            .font(BabyPulseTypography.title2())
                            .foregroundColor(BabyPulseColors.secondary)

                        Text("Tell us about your little one")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }
                    .padding(.bottom, BabyPulseLayout.spacingMD)
                    .accessibilityElement(children: .combine)

                    // Photo picker with avatar options
                    VStack {
                        ZStack {
                            if let photoData = viewModel.photoData, let uiImage = UIImage(data: photoData) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 120, height: 120)
                                    .clipShape(Circle())
                                    .overlay(
                                        Circle()
                                            .stroke(BabyPulseColors.primary, lineWidth: 3)
                                    )
                                    .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                            } else if let avatarIndex = viewModel.selectedAvatarIndex {
                                Image(viewModel.avatarOptions[avatarIndex])
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 120, height: 120)
                                    .clipShape(Circle())
                                    .overlay(
                                        Circle()
                                            .stroke(BabyPulseColors.primary, lineWidth: 3)
                                    )
                                    .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                            } else {
                                ZStack {
                                    Circle()
                                        .fill(BabyPulseColors.lightGray)
                                        .frame(width: 120, height: 120)

                                    Image(systemName: "person.fill")
                                        .font(.system(size: 40))
                                        .foregroundColor(BabyPulseColors.primary)

                                    Circle()
                                        .stroke(BabyPulseColors.primary, lineWidth: 3)
                                        .frame(width: 120, height: 120)
                                }
                                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                            }
                            
                            // Edit button overlay
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Button(action: {
                                        isShowingAvatarPicker.toggle()
                                    }) {
                                        Image(systemName: "pencil.circle.fill")
                                            .font(.system(size: 30))
                                            .foregroundColor(BabyPulseColors.primary)
                                            .background(Circle().fill(Color.white))
                                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                                    }
                                    .offset(x: 10, y: 10)
                                }
                            }
                            .frame(width: 120, height: 120)
                        }
                        
                        HStack(spacing: BabyPulseLayout.spacingMD) {
                            PhotosPicker(selection: $viewModel.selectedPhoto, matching: .images) {
                                Text("Add Photo")
                                    .font(BabyPulseTypography.footnote())
                                    .foregroundColor(BabyPulseColors.primary)
                            }
                            .onChange(of: viewModel.selectedPhoto) { _, _ in
                                Task {
                                    await viewModel.loadPhotoData()
                                }
                            }
                            
                            if viewModel.photoData != nil || viewModel.selectedAvatarIndex != nil {
                                Button("Clear") {
                                    viewModel.photoData = nil
                                    viewModel.selectedPhoto = nil
                                    viewModel.selectedAvatarIndex = nil
                                }
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                            }
                        }
                        .padding(.top, BabyPulseLayout.spacingSM)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.bottom, BabyPulseLayout.spacingLG)
                    
                    // Avatar picker sheet
                    .sheet(isPresented: $isShowingAvatarPicker) {
                        VStack(spacing: BabyPulseLayout.spacingLG) {
                            Text("Choose an Avatar")
                                .font(BabyPulseTypography.title3())
                                .padding(.top, BabyPulseLayout.paddingLG)
                            
                            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: BabyPulseLayout.spacingMD) {
                                ForEach(0..<viewModel.avatarOptions.count, id: \.self) { index in
                                    Button {
                                        viewModel.selectAvatar(index: index)
                                        isShowingAvatarPicker = false
                                    } label: {
                                        Image(viewModel.avatarOptions[index])
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 100, height: 100)
                                            .clipShape(Circle())
                                            .overlay(
                                                Circle()
                                                    .stroke(
                                                        viewModel.selectedAvatarIndex == index ? BabyPulseColors.primary : Color.clear,
                                                        lineWidth: 3
                                                    )
                                            )
                                            .padding(BabyPulseLayout.paddingSM)
                                            .background(
                                                Circle()
                                                    .fill(viewModel.selectedAvatarIndex == index ? BabyPulseColors.primary.opacity(0.1) : Color.white)
                                            )
                                    }
                                }
                            }
                            .padding()
                            
                            Button("Cancel") {
                                isShowingAvatarPicker = false
                            }
                            .padding(.bottom, BabyPulseLayout.paddingLG)
                        }
                        .presentationDetents([.medium])
                    }

                    // Form fields
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        // Name field
                        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                            Text("Baby's Name *")
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .accessibilityHidden(true)

                            HStack {
                                Image(systemName: "person.fill")
                                    .foregroundColor(BabyPulseColors.primary)
                                    .accessibilityHidden(true)

                                TextField("Enter baby's name", text: $viewModel.babyName)
                                    .font(BabyPulseTypography.body())
                                    .focused($isNameFieldFocused)
                                    .submitLabel(.next)
                                    .onChange(of: viewModel.babyName) { _, newValue in
                                        if !newValue.isEmpty && viewModel.nameError != nil {
                                            viewModel.nameError = nil
                                        }
                                    }
                                    .accessibilityLabel("Baby's name")
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .fill(BabyPulseColors.lightGray)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .stroke(viewModel.nameError != nil ? BabyPulseColors.error : Color.clear, lineWidth: 1.5)
                            )

                            if let error = viewModel.nameError {
                                Text(error)
                                    .font(BabyPulseTypography.caption())
                                    .foregroundColor(BabyPulseColors.error)
                                    .accessibilityLabel("Error: \(error)")
                            }
                        }

                        // Birth date picker with age display
                        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                            Text("Birth Date *")
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .accessibilityHidden(true)

                            HStack {
                                Image(systemName: "calendar")
                                    .foregroundColor(BabyPulseColors.primary)
                                    .accessibilityHidden(true)

                                DatePicker("", selection: $viewModel.birthDate, in: ...Date(), displayedComponents: .date)
                                    .datePickerStyle(.compact)
                                    .labelsHidden()
                                    .onChange(of: viewModel.birthDate) { _, _ in
                                        viewModel.updateBabyAge()
                                    }
                                    .accessibilityLabel("Birth date")

                                Spacer()
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                                    .fill(BabyPulseColors.lightGray)
                            )
                            
                            if !viewModel.babyAge.isEmpty {
                                HStack {
                                    Text("Age: \(viewModel.babyAge)")
                                        .font(BabyPulseTypography.caption())
                                        .foregroundColor(BabyPulseColors.primary)
                                        .padding(.top, 4)
                                        .accessibilityLabel("Baby's age is \(viewModel.babyAge)")
                                    
                                    Spacer()
                                }
                            }
                        }

                        // Gender selection with non-binary option
                        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                            Text("Gender *")
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .accessibilityHidden(true)

                            HStack(spacing: BabyPulseLayout.spacingSM) {
                                GenderOption(
                                    title: "Boy",
                                    icon: "figure.stand",
                                    isSelected: viewModel.gender == .male,
                                    color: Color.blue
                                ) {
                                    withAnimation {
                                        viewModel.gender = .male
                                    }
                                }
                                .accessibilityElement(children: .ignore)
                                .accessibilityLabel("Boy")
                                .accessibilityAddTraits(viewModel.gender == .male ? .isSelected : [])

                                GenderOption(
                                    title: "Girl",
                                    icon: "figure.stand.dress",
                                    isSelected: viewModel.gender == .female,
                                    color: Color.pink
                                ) {
                                    withAnimation {
                                        viewModel.gender = .female
                                    }
                                }
                                .accessibilityElement(children: .ignore)
                                .accessibilityLabel("Girl")
                                .accessibilityAddTraits(viewModel.gender == .female ? .isSelected : [])
                            }
                        }
                    }
                    .padding(.bottom, BabyPulseLayout.spacingLG)

                    // Next button - single clear call to action
                    BabyPulseButton(
                        title: "Continue",
                        icon: "",
                        style: .primary,
                        size: .large,
                        isFullWidth: true,
                        action: {
                            if viewModel.validateBabyProfile() {
                                withAnimation {
                                    viewModel.moveToNextStep()
                                }
                            } else if viewModel.nameError != nil {
                                // Focus the name field if that's the issue
                                isNameFieldFocused = true
                            }
                        }
                    )
                    .padding(.bottom, BabyPulseLayout.paddingMD)
                }
                .padding(BabyPulseLayout.paddingLG)
            }
        }
        .onAppear {
            isAnimating = true
            isNameFieldFocused = true
            viewModel.updateBabyAge()
        }
    }

    // Gender option component
    private struct GenderOption: View {
        let title: String
        let icon: String
        let isSelected: Bool
        let color: Color
        let action: () -> Void

        var body: some View {
            Button(action: action) {
                VStack(spacing: BabyPulseLayout.spacingSM) {
                    ZStack {
                        Circle()
                            .fill(isSelected ? color.opacity(0.2) : Color.white)
                            .frame(width: 60, height: 60)

                        Image(systemName: icon)
                            .font(.system(size: 30))
                            .foregroundColor(isSelected ? color : BabyPulseColors.darkGray)
                    }

                    Text(title)
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(isSelected ? color : BabyPulseColors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(BabyPulseLayout.paddingSM)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(isSelected ? color.opacity(0.1) : BabyPulseColors.lightGray)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .stroke(isSelected ? color : Color.clear, lineWidth: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

#Preview {
    BabyProfileStepView(viewModel: OnboardingViewModel())
}
