import SwiftUI
import OSLog

struct MessageBubbleView: View {
    let message: ChatMessage
    @State private var showCopyConfirmation: Bool = false
    @State private var feedbackGiven: Bool = false
    @State private var showActionMenu: Bool = false
    
    private let logger = Logger(subsystem: "com.babypulse", category: "MessageBubbleView")
    
    // Updated constants for better design
    private let bubblePadding: CGFloat = BabyPulseLayout.paddingMD
    private let maxBubbleWidth: CGFloat = UIScreen.main.bounds.width * 0.8

    var body: some View {
        HStack(alignment: .bottom, spacing: BabyPulseLayout.spacingSM) {
            // For assistant messages, show avatar
            if message.chatRole == .assistant {
                assistantAvatar
            }
            
            // Add spacer for user messages (right-aligned)
            if message.chatRole == .user {
                Spacer()
            }

            VStack(alignment: message.chatRole == .user ? .trailing : .leading, spacing: BabyPulseLayout.spacingXS) {
                // Message bubble with streaming animation if needed
                VStack(alignment: message.chatRole == .assistant ? .leading : .trailing, spacing: BabyPulseLayout.spacingXS) {
                    messageBubble
                    
                    // Timestamp and feedback indicator
                    if message.chatRole != .system {
                        HStack(spacing: BabyPulseLayout.spacingXS) {
                            Text(formatTimestamp(message.timestamp))
                                .font(BabyPulseTypography.caption())
                                .foregroundColor(BabyPulseColors.textTertiary)

                            // Show feedback indicator if provided
                            if message.chatRole == .assistant && message.feedback == true {
                                Image(systemName: "hand.thumbsup.fill")
                                    .font(.system(size: 10))
                                    .foregroundColor(BabyPulseColors.success)
                            }
                        }
                        .padding(.horizontal, BabyPulseLayout.spacingXS)
                    }
                }
                
                // Action button menu for assistant messages
                if message.chatRole == .assistant && message.messageState != .streaming && !message.content.isEmpty {
                    HStack(spacing: BabyPulseLayout.spacingMD) {
                        Button(action: {
                            UIPasteboard.general.string = message.content
                            showCopyConfirmation = true
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                showCopyConfirmation = false
                            }
                        }) {
                            Image(systemName: "doc.on.doc")
                                .font(.system(size: 12))
                                .foregroundColor(BabyPulseColors.textSecondary)
                        }
                        .buttonStyle(ScaleButtonStyle())

                        Button(action: {
                            message.feedback = !(message.feedback ?? false)
                            feedbackGiven = true
                            logger.debug("User marked message as \(message.feedback == true ? "helpful" : "not helpful")")
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                feedbackGiven = false
                            }
                        }) {
                            Image(systemName: message.feedback == true ? "hand.thumbsup.fill" : "hand.thumbsup")
                                .font(.system(size: 12))
                                .foregroundColor(message.feedback == true ? BabyPulseColors.success : BabyPulseColors.textSecondary)
                        }
                        .buttonStyle(ScaleButtonStyle())
                    }
                    .padding(.horizontal, BabyPulseLayout.spacingXS)
                    .opacity(showActionMenu ? 1.0 : 0.0)
                }
                
                // Feedback confirmation overlay
                if showCopyConfirmation || feedbackGiven {
                    Text(showCopyConfirmation ? "Copied to clipboard" : 
                         (message.feedback == true ? "Marked as helpful" : "Marked as unhelpful"))
                        .font(BabyPulseTypography.caption())
                        .padding(.horizontal, BabyPulseLayout.spacingSM)
                        .padding(.vertical, BabyPulseLayout.spacingXS)
                        .background(
                            Capsule()
                                .fill(BabyPulseColors.success.opacity(0.1))
                                .overlay(
                                    Capsule()
                                        .stroke(BabyPulseColors.success.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(BabyPulseColors.success)
                        .transition(.opacity.combined(with: .scale))
                }
            }
            .opacity(message.chatRole == .system ? 0.7 : 1.0)
            .frame(maxWidth: message.chatRole == .system ? .infinity : nil)
            .onTapGesture {
                if message.chatRole == .assistant && message.messageState != .streaming {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showActionMenu.toggle()
                    }
                }
            }

            // Add spacer for assistant and system messages (left-aligned)
            if message.chatRole == .assistant || message.chatRole == .system {
                Spacer()
            }
            
            // For user messages, show avatar
            if message.chatRole == .user {
                userAvatar
            }
        }
        .padding(.horizontal, BabyPulseLayout.paddingMD)
        .padding(.vertical, BabyPulseLayout.spacingXS)
    }
    
    // MARK: - UI Components
    
    private var messageBubble: some View {
        Group {
            if message.messageState == .streaming {
                if message.content.isEmpty {
                    // "AI is typing..." bubble
                    HStack(spacing: BabyPulseLayout.spacingSM) {
                        Text("AI is typing")
                            .font(BabyPulseTypography.callout())
                        AnimatedDotsView()
                        Spacer()
                    }
                    .padding(bubblePadding)
                    .background(messageBackground)
                    .foregroundColor(messageForeground)
                    .clipShape(RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG))
                } else {
                    // Streaming content with cursor
                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                        // Subtle disclaimer for assistant messages
                        if message.chatRole == .assistant {
                            disclaimerText
                        }
                        
                        MarkdownText(content: message.content)
                    }
                    .padding(bubblePadding)
                    .background(messageBackground)
                    .foregroundColor(messageForeground)
                    .clipShape(RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG))
                    .overlay(alignment: .trailing) {
                        TypewriterCursorView()
                            .padding(.trailing, BabyPulseLayout.paddingMD)
                    }
                }
            } else {
                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    // Subtle disclaimer for assistant messages
                    if message.chatRole == .assistant && !message.content.isEmpty {
                        disclaimerText
                    }
                    
                    MarkdownText(content: message.content)
                }
                .padding(bubblePadding)
                .background(messageBackground)
                .foregroundColor(messageForeground)
                .clipShape(RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG))
                .shadow(
                    color: message.chatRole == .user ? BabyPulseColors.primary.opacity(0.15) : 
                           Color.black.opacity(0.04),
                    radius: message.chatRole == .user ? 3 : 2,
                    x: 0, 
                    y: 1
                )
            }
        }
        .frame(maxWidth: maxBubbleWidth, alignment: message.chatRole == .user ? .trailing : .leading)
    }
    
    // More subtle disclaimer text
    private var disclaimerText: some View {
        Text("AI-generated content - not medical advice")
            .font(.system(size: 10, weight: .medium))
            .foregroundColor(BabyPulseColors.textTertiary)
            .opacity(0.8)
    }
    
    private var assistantAvatar: some View {
        ZStack {
            Circle()
                .fill(BabyPulseColors.primary.opacity(0.1))
                .frame(width: 24, height: 24)
            
            Image(systemName: "brain.head.profile")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(BabyPulseColors.primary)
        }
    }
    
    private var userAvatar: some View {
        ZStack {
            Circle()
                .fill(BabyPulseColors.lightGray)
                .frame(width: 24, height: 24)
            
            Text("You")
                .font(.system(size: 8, weight: .medium))
                .foregroundColor(BabyPulseColors.textSecondary)
        }
    }
    
    // MARK: - Helper Properties & Methods
    
    private var messageBackground: Color {
        switch message.chatRole {
        case .user:
            return BabyPulseColors.primary
        case .assistant:
            return message.messageState == .streaming ?
                BabyPulseColors.lightGray.opacity(0.8) : BabyPulseColors.lightGray
        case .system:
            return BabyPulseColors.lightGray.opacity(0.5)
        }
    }

    private var messageForeground: Color {
        switch message.chatRole {
        case .user:
            return .white
        case .assistant, .system:
            return BabyPulseColors.text
        }
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// Typewriter cursor view for streaming messages
struct TypewriterCursorView: View {
    @State private var opacity: Double = 0.0

    var body: some View {
        Rectangle()
            .fill(Color.primary)
            .frame(width: 2, height: 14)
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeInOut(duration: 0.6).repeatForever()) {
                    opacity = 1.0
                }
            }
    }
}

#Preview {
    VStack {
        MessageBubbleView(message: ChatMessage.createSystemMessage(content: "This is a system message with important information."))
        MessageBubbleView(message: ChatMessage.createAssistantMessage(content: "Hello! How can I help you with your baby today?"))
        MessageBubbleView(message: ChatMessage.createStreamingAssistantMessage(initialContent: "I'm typing a response about your baby's sleep patterns..."))
        MessageBubbleView(message: ChatMessage.createUserMessage(content: "My baby isn't sleeping well. What should I do?"))
    }
}
