import SwiftUI

struct ChatInputView: View {
    @Binding var text: String
    let onSend: () -> Void
    let isLoading: Bool
    var isStreaming: Bool = false

    @State private var inputHeight: CGFloat = 36
    @FocusState private var isFocused: Bool
    private let minHeight: CGFloat = 36
    private let maxHeight: CGFloat = 120

    // Computed property to determine if the input is disabled
    private var isInputDisabled: Bool {
        isLoading || isStreaming
    }

    // Computed property for the placeholder text
    private var placeholderText: String {
        if isStreaming {
            return "Wait for response to complete..."
        } else {
            return "Ask a question..."
        }
    }

    var body: some View {
        HStack(alignment: .bottom, spacing: BabyPulseLayout.spacingMD) {
            // Modern text input with dynamic height
            ZStack(alignment: .topLeading) {
                // Hidden text view to calculate height
                Text(text.isEmpty ? " " : text)
                    .font(BabyPulseTypography.body())
                    .padding(.horizontal, 4)
                    .padding(.vertical, 8)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)
                    .opacity(0)
                    .background(GeometryReader { geometry in
                        Color.clear.preference(
                            key: ViewHeightKey.self,
                            value: geometry.size.height
                        )
                    })

                // Actual input field - using TextEditor for all iOS versions for consistency
                TextEditor(text: $text)
                    .font(BabyPulseTypography.body())
                    .frame(height: inputHeight)
                    .padding(.horizontal, -4)
                    .padding(.vertical, 4)
                    .disabled(isInputDisabled)
                    .focused($isFocused)
                    .scrollContentBackground(.hidden)
                    .background(Color.clear)

                // Placeholder text
                if text.isEmpty && !isFocused {
                    Text(placeholderText)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.textTertiary)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 8)
                        .allowsHitTesting(false)
                }
            }
            .padding(EdgeInsets(top: 4, leading: BabyPulseLayout.paddingSM, bottom: 4, trailing: BabyPulseLayout.paddingSM))
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(isInputDisabled ? BabyPulseColors.lightGray.opacity(0.8) : BabyPulseColors.lightGray)
            )
            .clipShape(RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG))
            .overlay(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .stroke(isFocused ? BabyPulseColors.primary.opacity(0.3) : Color.clear, lineWidth: 1)
            )
            .onPreferenceChange(ViewHeightKey.self) { height in
                let newHeight = min(max(height, minHeight), maxHeight)
                if inputHeight != newHeight {
                    withAnimation(.easeOut(duration: 0.2)) {
                        inputHeight = newHeight
                    }
                }
            }

            // Modern send button with state-based styling
            Button(action: onSend) {
                ZStack {
                    Circle()
                        .fill(isButtonEnabled() ? BabyPulseColors.primary : BabyPulseColors.mediumGray)
                        .frame(width: 36, height: 36)
                        .shadow(
                            color: isButtonEnabled() ? BabyPulseColors.primary.opacity(0.25) : .clear,
                            radius: isButtonEnabled() ? 4 : 0,
                            x: 0,
                            y: 2
                        )

                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.7)
                    } else if isStreaming {
                        Image(systemName: "ellipsis")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "arrow.up")
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
            }
            .disabled(!isButtonEnabled())
            .scaleEffect(isButtonEnabled() ? 1.0 : 0.95)
            .animation(.easeInOut(duration: 0.2), value: isButtonEnabled())
        }
        .padding(.horizontal, BabyPulseLayout.paddingMD)
        .padding(.vertical, BabyPulseLayout.spacingMD)
        .background(Color(.systemBackground))
    }

    // Helper to determine if send button should be enabled
    private func isButtonEnabled() -> Bool {
        return !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isInputDisabled
    }
}

// Helper key to get text height
struct ViewHeightKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}

#Preview {
    VStack {
        Spacer()
        ChatInputView(text: .constant("Hello"), onSend: {}, isLoading: false)
        ChatInputView(text: .constant(""), onSend: {}, isLoading: true)
        ChatInputView(text: .constant(""), onSend: {}, isLoading: false, isStreaming: true)
    }
}
