import SwiftUI
import SwiftData
import OSLog

@preconcurrency import class SwiftData.ModelContext

struct ChatView: View {
    @StateObject private var viewModel: ChatViewModel
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isInputFocused: Bool
    
    let showBackButton: Bool
    let showHeader: Bool

    private let logger = Logger(subsystem: "com.babypulse", category: "ChatView")

    init(thread: ChatThread? = nil, showBackButton: Bool = true, showHeader: Bool = true) {
        _viewModel = StateObject(wrappedValue: ChatViewModel(thread: thread))
        self.showBackButton = showBackButton
        self.showHeader = showHeader
    }

    var body: some View {
        ZStack {
            // Background
            Color(UIColor.systemBackground)
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Streamlined header (only if showHeader is true)
                if showHeader {
                    modernChatHeader
                }
                
                // Messages list with optimized spacing
                messagesContent
                
                // Bottom section with suggested questions and input
                bottomSection
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.setModelContext(modelContext)
            viewModel.loadMessages()
        }
    }

    // MARK: - UI Components

    private var modernChatHeader: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            if showBackButton {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(BabyPulseColors.primary.opacity(0.1))
                        )
                }
            }

            Spacer()

            // Simplified menu button (only if thread exists)
            if viewModel.thread != nil {
                Menu {
                    Button(role: .destructive, action: {
                        viewModel.deleteThread()
                        dismiss()
                    }) {
                        Label("Delete Conversation", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(BabyPulseColors.lightGray.opacity(0.5))
                        )
                }
            }
        }
        .padding(.horizontal, BabyPulseLayout.paddingMD)
        .padding(.vertical, BabyPulseLayout.spacingSM)
        .background(
            Rectangle()
                .fill(Color(.systemBackground))
                .overlay(
                    Rectangle()
                        .frame(height: 0.5)
                        .foregroundColor(BabyPulseColors.mediumGray.opacity(0.3)),
                    alignment: .bottom
                )
        )
    }

    private var messagesContent: some View {
        ScrollViewReader { scrollView in
            ScrollView {
                LazyVStack(spacing: BabyPulseLayout.spacingSM) {
                    ForEach(viewModel.messages, id: \.id) { message in
                        MessageBubbleView(message: message)
                            .id(message.id)
                            .onChange(of: message.content) { _, _ in
                                if message.messageState == .streaming {
                                    withAnimation(.easeOut(duration: 0.3)) {
                                        scrollView.scrollTo(message.id, anchor: .bottom)
                                    }
                                }
                            }
                    }

                    if viewModel.isLoading {
                        HStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: BabyPulseColors.primary))
                                .scaleEffect(0.8)
                            Spacer()
                        }
                        .padding(.horizontal, BabyPulseLayout.paddingMD)
                    }
                }
                .padding(.vertical, BabyPulseLayout.spacingSM)
            }
            .onChange(of: viewModel.messages) { _, _ in
                if let lastMessage = viewModel.messages.last {
                    withAnimation(.easeOut(duration: 0.3)) {
                        scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
        .overlay(
            // Error message overlay with better styling
            viewModel.errorMessage != nil ?
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.white)
                        
                        Text(viewModel.errorMessage!)
                            .font(BabyPulseTypography.footnote())
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingMD)
                    .padding(.vertical, BabyPulseLayout.spacingSM)
                    .background(
                        Capsule()
                            .fill(BabyPulseColors.error)
                    )
                    .padding(.bottom, BabyPulseLayout.paddingMD)
                }
                : nil
        )
    }

    private var bottomSection: some View {
        VStack(spacing: 0) {
            // Suggested questions with improved integration
            // Show suggested questions when there are no user messages or only 1 assistant message (welcome message)
            if !viewModel.suggestedQuestions.isEmpty && shouldShowSuggestedQuestions {
                SuggestedQuestionsView(
                    questions: viewModel.suggestedQuestions,
                    onQuestionSelected: { question in
                        viewModel.askSuggestedQuestion(question)
                    }
                )
            }

            // Input field
            ChatInputView(
                text: $viewModel.inputText,
                onSend: {
                    isInputFocused = false
                    viewModel.sendMessage()
                },
                isLoading: viewModel.isLoading,
                isStreaming: viewModel.isStreaming
            )
        }
        .background(
            Rectangle()
                .fill(Color(.systemBackground))
                .shadow(
                    color: Color.black.opacity(0.04),
                    radius: 8,
                    x: 0,
                    y: -2
                )
        )
    }
    
    // MARK: - Helper Properties
    
    private var shouldShowSuggestedQuestions: Bool {
        // Show suggested questions if:
        // 1. No messages at all, OR
        // 2. Only welcome/assistant messages exist (no user messages), OR  
        // 3. Very few user messages (early conversation stage)
        
        if viewModel.messages.isEmpty {
            return true
        }
        
        // Check if there are user messages
        let userMessages = viewModel.messages.filter { $0.chatRole == .user }
        
        // Show if no user messages or only 1-2 user messages (early conversation)
        return userMessages.count <= 2
    }
}

#Preview {
    ChatView()
        .modelContainer(for: [Baby.self, ChatThread.self, ChatMessage.self], inMemory: true)
}
