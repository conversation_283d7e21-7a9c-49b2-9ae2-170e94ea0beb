import SwiftUI

// Import the ButtonStyles file
import BabyPulse

struct SuggestedQuestionsView: View {
    let questions: [String]
    let onQuestionSelected: (String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
            // More subtle title
            HStack {
                Text("Suggested Questions")
                    .font(BabyPulseTypography.caption())
                    .fontWeight(.medium)
                    .foregroundColor(BabyPulseColors.textSecondary)
                
                Spacer()
                
                Image(systemName: "lightbulb.fill")
                    .font(.system(size: 10))
                    .foregroundColor(BabyPulseColors.primary.opacity(0.6))
            }
            .padding(.horizontal, BabyPulseLayout.paddingMD)

            // Horizontal scrolling for suggestion chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: BabyPulseLayout.spacingSM) {
                    ForEach(Array(questions.enumerated()), id: \.offset) { index, question in
                        Button(action: {
                            self.onQuestionSelected(question)
                        }) {
                            Text(question)
                                .font(BabyPulseTypography.footnote())
                                .fontWeight(.medium)
                                .multilineTextAlignment(.center)
                                .fixedSize(horizontal: false, vertical: false)
                                .padding(.horizontal, BabyPulseLayout.paddingMD)
                                .padding(.vertical, BabyPulseLayout.spacingSM)
                                .background(
                                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                                        .fill(BabyPulseColors.primary.opacity(0.08))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                                                .stroke(BabyPulseColors.primary.opacity(0.2), lineWidth: 0.5)
                                        )
                                )
                                .foregroundColor(BabyPulseColors.text)
                                .frame(minWidth: 120, maxWidth: 1200)
                        }
                        .buttonStyle(ScaleButtonStyle())
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)
            }
        }
        .padding(.vertical, BabyPulseLayout.spacingSM)
        .background(
            Rectangle()
                .fill(BabyPulseColors.background.opacity(0.8))
                .overlay(
                    Rectangle()
                        .frame(height: 0.5)
                        .foregroundColor(BabyPulseColors.mediumGray.opacity(0.5)),
                    alignment: .top
                )
        )
    }
}

#Preview {
    SuggestedQuestionsView(
        questions: [
            "Is my baby sleeping enough?",
            "How much should my baby be eating?",
            "When should I be concerned about diaper changes?",
            "What are normal growth patterns?",
            "How can I establish a better sleep routine?"
        ],
        onQuestionSelected: { _ in }
    )
}
