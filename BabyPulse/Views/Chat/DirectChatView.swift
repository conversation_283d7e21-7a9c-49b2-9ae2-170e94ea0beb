import SwiftUI
import SwiftData
import OSLog
//import BabyPulse

struct DirectChatView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var babies: [Baby]
    @Query private var threads: [ChatThread]
    @State private var refreshTrigger = false
    @State private var selectedBabyID: UUID?
    @State private var showBabySelector = false
    @State private var tabTransitioning = false

    private let logger = Logger(subsystem: "com.babypulse", category: "DirectChatView")

    var body: some View {
        ZStack {
            // Background
            Color(.systemBackground)
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Compact header
                compactChatHeader

                // Main content area
                if babies.isEmpty {
                    emptyStateView
                } else if babies.count == 1 {
                    singleBabyChatView
                } else {
                    multipleBabiesView
                }
            }
            .opacity(tabTransitioning ? 0.0 : 1.0)
        }
        .navigationBarHidden(true)
        .onAppear {
            setupNotifications()
            if let firstBaby = babies.first {
                selectedBabyID = firstBaby.id
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                fetchNetworkData()
            }
        }
        .sheet(isPresented: $showBabySelector) {
            compactBabySelectorSheet
        }
    }

    // Compact header with better space utilization
    private var compactChatHeader: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            // Baby info section
            HStack(spacing: BabyPulseLayout.spacingSM) {
                // Single avatar for selected baby
                if let selectedBaby = babies.first(where: { $0.id == selectedBabyID }) ?? babies.first {
                    compactBabyAvatar(for: selectedBaby)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(selectedBabyName)
                        .font(BabyPulseTypography.title3())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.text)
                        .lineLimit(1)
                    
                    Text("AI Chat Assistant")
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
                
                // Baby selector button (only if multiple babies)
                if babies.count > 1 {
                    Button(action: {
                        showBabySelector = true
                    }) {
                        Image(systemName: "chevron.down")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(BabyPulseColors.primary)
                            .frame(width: 24, height: 24)
                            .background(
                                Circle()
                                    .fill(BabyPulseColors.primary.opacity(0.1))
                            )
                    }
                }
            }

            Spacer()
        }
        .padding(.horizontal, BabyPulseLayout.paddingMD)
        .padding(.vertical, BabyPulseLayout.spacingSM)
        .background(
            Rectangle()
                .fill(Color(.systemBackground))
                .overlay(
                    Rectangle()
                        .frame(height: 0.5)
                        .foregroundColor(BabyPulseColors.mediumGray.opacity(0.3)),
                    alignment: .bottom
                )
        )
    }

    private func compactBabyAvatar(for baby: Baby) -> some View {
        ZStack {
            Circle()
                .fill(BabyPulseColors.primary.opacity(0.1))
                .frame(width: 32, height: 32)

            if let photoData = baby.photoData, let image = UIImage(data: photoData) {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 28, height: 28)
                    .clipShape(Circle())
            } else {
                Text(baby.name.prefix(1).uppercased())
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(BabyPulseColors.primary)
            }
        }
    }

    // Streamlined empty state
    private var emptyStateView: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            Spacer()

            ZStack {
                Circle()
                    .fill(BabyPulseColors.primary.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: "bubble.left.and.bubble.right")
                    .font(.system(size: 36))
                    .foregroundColor(BabyPulseColors.primary)
            }

            VStack(spacing: BabyPulseLayout.spacingSM) {
                Text("Start Your Baby's Journey")
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)

                Text("Add your baby's profile to unlock personalized chat assistance")
                    .font(BabyPulseTypography.body())
                    .multilineTextAlignment(.center)
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
            }

            Button(action: {
                NotificationCenter.default.post(name: NSNotification.Name("NavigateToSettings"), object: nil)
            }) {
                HStack(spacing: BabyPulseLayout.spacingSM) {
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .semibold))
                    
                    Text("Add Baby Profile")
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, BabyPulseLayout.paddingLG)
                .padding(.vertical, BabyPulseLayout.paddingMD)
                .background(
                    Capsule()
                        .fill(BabyPulseColors.primary)
                        .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 4, x: 0, y: 2)
                )
            }
            .buttonStyle(ScaleButtonStyle())

            Spacer()
        }
        .padding(BabyPulseLayout.paddingLG)
    }

    // Single baby chat view
    private var singleBabyChatView: some View {
        Group {
            if let thread = getOrCreateThreadForSingleBaby() {
                ChatView(thread: thread, showBackButton: false, showHeader: false)
                    .transition(.opacity)
                    .onAppear {
                        logger.debug("DirectChatView: Showing direct chat for single baby: \(babies[0].name)")
                    }
            } else {
                errorStateView
            }
        }
    }

    // Multiple babies view
    private var multipleBabiesView: some View {
        Group {
            if let selectedBaby = babies.first(where: { $0.id == selectedBabyID }) {
                if let thread = getOrCreateThreadForBaby(selectedBaby) {
                    ChatView(thread: thread, showBackButton: false, showHeader: false)
                        .transition(.opacity)
                } else {
                    errorStateView
                }
            } else {
                if let firstBaby = babies.first {
                    if let thread = getOrCreateThreadForBaby(firstBaby) {
                        ChatView(thread: thread, showBackButton: false, showHeader: false)
                            .transition(.opacity)
                            .onAppear {
                                selectedBabyID = firstBaby.id
                            }
                    }
                }
            }
        }
    }

    // Compact error state
    private var errorStateView: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 32))
                .foregroundColor(BabyPulseColors.warning)

            Text("Could not load chat")
                .font(BabyPulseTypography.title3())
                .foregroundColor(BabyPulseColors.text)

            Text("Please try again in a moment")
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)

            Button(action: {
                refreshTrigger.toggle()
            }) {
                Text("Retry")
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                    .padding(.vertical, BabyPulseLayout.spacingSM)
                    .background(
                        Capsule()
                            .fill(BabyPulseColors.primary)
                    )
            }
            .buttonStyle(ScaleButtonStyle())
        }
        .padding(BabyPulseLayout.paddingLG)
    }

    // Compact baby selector sheet
    private var compactBabySelectorSheet: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Handle
            Capsule()
                .fill(BabyPulseColors.mediumGray)
                .frame(width: 36, height: 4)
                .padding(.top, BabyPulseLayout.spacingSM)

            // Title
            Text("Select Baby")
                .font(BabyPulseTypography.title3())
                .fontWeight(.semibold)
                .foregroundColor(BabyPulseColors.text)

            // Baby list
            ScrollView {
                VStack(spacing: BabyPulseLayout.spacingSM) {
                    ForEach(babies) { baby in
                        compactBabyRow(baby)
                    }
                }
                .padding(.horizontal, BabyPulseLayout.paddingMD)
            }

            Spacer()
        }
        .background(Color(.systemBackground))
        .presentationDetents([.medium])
    }

    private func compactBabyRow(_ baby: Baby) -> some View {
        Button(action: {
            selectedBabyID = baby.id
            updateSelectedBaby(baby)
            showBabySelector = false
        }) {
            HStack(spacing: BabyPulseLayout.spacingMD) {
                compactBabyAvatar(for: baby)

                VStack(alignment: .leading, spacing: 2) {
                    Text(baby.name)
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.medium)
                        .foregroundColor(BabyPulseColors.text)

                    Text(baby.gender.rawValue.capitalized)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Spacer()

                if selectedBabyID == baby.id {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(BabyPulseColors.primary)
                }
            }
            .padding(BabyPulseLayout.paddingMD)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(selectedBabyID == baby.id ? BabyPulseColors.primary.opacity(0.1) : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper methods

    private var selectedBabyName: String {
        if babies.isEmpty {
            return "Baby Chat"
        } else if babies.count == 1 {
            return babies[0].name
        } else if let selectedID = selectedBabyID, let baby = babies.first(where: { $0.id == selectedID }) {
            return baby.name
        } else {
            return babies.first?.name ?? "Select Baby"
        }
    }

    private func getOrCreateThreadForSingleBaby() -> ChatThread? {
        guard let baby = babies.first else { return nil }
        return getOrCreateThreadForBaby(baby)
    }

    private func getOrCreateThreadForBaby(_ baby: Baby) -> ChatThread? {
        if let existingThread = threads.first(where: { $0.baby?.id == baby.id }) {
            return existingThread
        } else {
            return ChatThread.createThreadWithWelcomeMessage(for: baby, in: modelContext)
        }
    }

    private func updateSelectedBaby(_ baby: Baby) {
        logger.debug("Selected baby: \(baby.name)")
    }

    private func setupNotifications() {
        // Setup needed notifications
    }

    private func fetchNetworkData() {
        // Fetch any needed network data
    }
}

#Preview {
    DirectChatView()
        .modelContainer(for: [Baby.self, ChatThread.self, ChatMessage.self], inMemory: true)
}

