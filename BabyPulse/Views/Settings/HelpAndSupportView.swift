import SwiftUI
import MessageUI // For sending email

struct HelpAndSupportView: View {
    @State private var result: Result<MFMailComposeResult, Error>? = nil
    @State private var isShowingMailView = false
    @State private var selectedTab = 0
    @State private var searchText = ""
    @Environment(\.openURL) var openURL

    // Configuration
    let supportEmail = "<EMAIL>" // Replace with your actual support email
    let faqURL = "https://www.babypulseapp.com/faq" // Replace with your actual FAQ URL

    var body: some View {
        VStack(spacing: 0) {
            // Search bar
            searchBar

            // Tab picker
            Picker("Support Options", selection: $selectedTab) {
                Text("FAQ").tag(0)
                Text("Troubleshooting").tag(1)
                Text("Contact").tag(2)
            }
            .pickerStyle(.segmented)
            .padding()

            // Content based on selected tab
            TabView(selection: $selectedTab) {
                FAQView(searchText: searchText)
                    .tag(0)

                TroubleshootingView()
                    .tag(1)

                ContactView(
                    isShowingMailView: $isShowingMailView,
                    result: $result,
                    supportEmail: supportEmail
                )
                .tag(2)
            }
            .tabViewStyle(.page(indexDisplayMode: .never))
        }
        .navigationTitle("Help & Support")
        .navigationBarTitleDisplayMode(.inline)
        .background(BabyPulseColors.background.opacity(0.5))
        .sheet(isPresented: $isShowingMailView) {
            MailView(result: self.$result, supportEmail: supportEmail)
        }
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(BabyPulseColors.textSecondary)

            TextField("Search for help...", text: $searchText)
                .textFieldStyle(.plain)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal)
        .padding(.top, 8)
    }
}

// MARK: - FAQ View

struct FAQView: View {
    let searchText: String

    private let faqItems = [
        FAQItem(
            question: "How do I add a feeding entry?",
            answer: "Tap the + button at the bottom right of the screen, then select 'Feeding'. Choose the feeding type (bottle or breastfeeding), enter the amount or duration, and tap 'Save'."
        ),
        FAQItem(
            question: "Can I track multiple babies?",
            answer: "Yes! You can add multiple baby profiles in the Settings > Baby Profile section. Tap 'Add Baby' to create a new profile and switch between them easily."
        ),
        FAQItem(
            question: "How do I sync my data across devices?",
            answer: "Data sync requires a premium subscription. Go to Settings > Account to sign in, then enable backup in Settings > Backup & Sync. Your data will automatically sync across all your devices."
        ),
        FAQItem(
            question: "What do the growth charts show?",
            answer: "Growth charts display your baby's weight, height, and head circumference over time. They help you track your baby's development and identify growth patterns. You can view percentiles and compare against standard growth curves."
        ),
        FAQItem(
            question: "How do I change the units (metric/imperial)?",
            answer: "Go to Settings > Units to switch between metric (kg, cm) and imperial (lb, in) units. All your existing data will be converted automatically."
        ),
        FAQItem(
            question: "Can I export my baby's data?",
            answer: "Yes! In Settings > Privacy & Data, you can export all your data as a JSON file. This creates a backup of all entries, growth data, and baby information."
        ),
        FAQItem(
            question: "How do I set up notifications?",
            answer: "Go to Settings > Notifications to enable feeding reminders, sleep tracking alerts, and other notifications. You can customize which types of notifications you receive and set quiet hours."
        ),
        FAQItem(
            question: "What are AI Insights?",
            answer: "AI Insights analyze your baby's patterns and provide personalized recommendations about feeding, sleep, and development. These insights help you understand your baby's needs better."
        ),
        FAQItem(
            question: "How do I delete an entry?",
            answer: "In the Logs view, swipe left on any entry to see the delete option. You can also tap on an entry to edit or delete it from the detail view."
        ),
        FAQItem(
            question: "Is my data private and secure?",
            answer: "Yes, your baby's data is encrypted and stored securely. We never share personal information with third parties. You can review our privacy policy in Settings > About > Privacy Policy."
        )
    ]

    private var filteredFAQs: [FAQItem] {
        if searchText.isEmpty {
            return faqItems
        } else {
            return faqItems.filter {
                $0.question.localizedCaseInsensitiveContains(searchText) ||
                $0.answer.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredFAQs, id: \.question) { faq in
                    FAQCard(faq: faq)
                }

                if filteredFAQs.isEmpty && !searchText.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "questionmark.circle")
                            .font(.system(size: 48))
                            .foregroundColor(BabyPulseColors.textSecondary)

                        Text("No results found")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.text)

                        Text("Try a different search term or browse all FAQs")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 40)
                }
            }
            .padding()
        }
    }
}

struct FAQItem {
    let question: String
    let answer: String
}

struct FAQCard: View {
    let faq: FAQItem
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            } label: {
                HStack {
                    Text(faq.question)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .animation(.easeInOut(duration: 0.3), value: isExpanded)
                }
                .padding()
            }
            .buttonStyle(.plain)

            if isExpanded {
                Text(faq.answer)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .padding(.horizontal)
                    .padding(.bottom)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Troubleshooting View

struct TroubleshootingView: View {
    private let troubleshootingItems = [
        TroubleshootingItem(
            title: "App Crashes or Freezes",
            icon: "exclamationmark.triangle.fill",
            color: .red,
            steps: [
                "Force close the app and restart it",
                "Restart your device",
                "Check for app updates in the App Store",
                "Free up storage space on your device",
                "If the problem persists, contact support"
            ]
        ),
        TroubleshootingItem(
            title: "Data Not Syncing",
            icon: "arrow.triangle.2.circlepath",
            color: .orange,
            steps: [
                "Check your internet connection",
                "Verify you're signed in to your account",
                "Ensure you have a premium subscription",
                "Try manually syncing in Settings > Backup & Sync",
                "Sign out and sign back in to your account"
            ]
        ),
        TroubleshootingItem(
            title: "Notifications Not Working",
            icon: "bell.slash.fill",
            color: .purple,
            steps: [
                "Check notification permissions in iOS Settings",
                "Ensure notifications are enabled in app Settings",
                "Check Do Not Disturb mode is off",
                "Verify quiet hours settings",
                "Restart the app and try again"
            ]
        ),
        TroubleshootingItem(
            title: "Charts Not Loading",
            icon: "chart.bar.fill",
            color: .blue,
            steps: [
                "Make sure you have logged some data",
                "Check the date range selection",
                "Try refreshing by pulling down on the charts",
                "Verify the selected baby profile has data",
                "Clear app cache by restarting the app"
            ]
        ),
        TroubleshootingItem(
            title: "Can't Add or Edit Entries",
            icon: "pencil.slash",
            color: .green,
            steps: [
                "Check if you have a baby profile created",
                "Verify all required fields are filled",
                "Try closing and reopening the entry form",
                "Restart the app",
                "Contact support if the issue continues"
            ]
        )
    ]

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(troubleshootingItems, id: \.title) { item in
                    TroubleshootingCard(item: item)
                }
            }
            .padding()
        }
    }
}

struct TroubleshootingItem {
    let title: String
    let icon: String
    let color: Color
    let steps: [String]
}

struct TroubleshootingCard: View {
    let item: TroubleshootingItem
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            } label: {
                HStack {
                    Image(systemName: item.icon)
                        .foregroundColor(.white)
                        .font(.system(size: 16, weight: .semibold))
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(item.color)
                        )

                    Text(item.title)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .animation(.easeInOut(duration: 0.3), value: isExpanded)
                }
                .padding()
            }
            .buttonStyle(.plain)

            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Try these steps:")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)
                        .padding(.bottom, 4)

                    ForEach(Array(item.steps.enumerated()), id: \.offset) { index, step in
                        HStack(alignment: .top, spacing: 12) {
                            Text("\(index + 1).")
                                .font(BabyPulseTypography.body())
                                .foregroundColor(item.color)
                                .fontWeight(.semibold)

                            Text(step)
                                .font(BabyPulseTypography.body())
                                .foregroundColor(BabyPulseColors.textSecondary)
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Contact View

struct ContactView: View {
    @Binding var isShowingMailView: Bool
    @Binding var result: Result<MFMailComposeResult, Error>?
    let supportEmail: String
    @Environment(\.openURL) var openURL

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Contact options
                VStack(spacing: 16) {
                    ContactOption(
                        icon: "envelope.fill",
                        title: "Email Support",
                        description: "Get help from our support team",
                        action: {
                            if MFMailComposeViewController.canSendMail() {
                                isShowingMailView = true
                            } else {
                                if let emailUrl = URL(string: "mailto:\(supportEmail)") {
                                    openURL(emailUrl)
                                }
                            }
                        }
                    )

                    ContactOption(
                        icon: "questionmark.circle.fill",
                        title: "Feature Request",
                        description: "Suggest new features or improvements",
                        action: {
                            if MFMailComposeViewController.canSendMail() {
                                isShowingMailView = true
                            } else {
                                if let emailUrl = URL(string: "mailto:\(supportEmail)?subject=Feature Request") {
                                    openURL(emailUrl)
                                }
                            }
                        }
                    )

                    ContactOption(
                        icon: "ant.fill",
                        title: "Report a Bug",
                        description: "Help us fix issues you've encountered",
                        action: {
                            if MFMailComposeViewController.canSendMail() {
                                isShowingMailView = true
                            } else {
                                if let emailUrl = URL(string: "mailto:\(supportEmail)?subject=Bug Report") {
                                    openURL(emailUrl)
                                }
                            }
                        }
                    )
                }

                // Response time info
                VStack(spacing: 12) {
                    Text("Support Information")
                        .font(BabyPulseTypography.title3())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.text)

                    VStack(spacing: 8) {
                        SupportInfoRow(
                            icon: "clock.fill",
                            title: "Response Time",
                            value: "Within 24 hours"
                        )

                        SupportInfoRow(
                            icon: "globe",
                            title: "Languages",
                            value: "English, Spanish, French"
                        )

                        SupportInfoRow(
                            icon: "calendar",
                            title: "Support Hours",
                            value: "Monday - Friday, 9 AM - 6 PM EST"
                        )
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.tertiarySystemBackground))
                )
            }
            .padding()
        }
    }
}

struct ContactOption: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .foregroundColor(.white)
                    .font(.system(size: 20, weight: .semibold))
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(BabyPulseColors.primary)
                            .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 4, x: 0, y: 2)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)

                    Text(description)
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .font(.caption)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

struct SupportInfoRow: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(BabyPulseColors.primary)
                .frame(width: 20)

            Text(title)
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.text)

            Spacer()

            Text(value)
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)
        }
    }
}

// MARK: - Mail View (Updated)

struct MailView: UIViewControllerRepresentable {
    @Binding var result: Result<MFMailComposeResult, Error>?
    let supportEmail: String

    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        @Binding var result: Result<MFMailComposeResult, Error>?

        init(result: Binding<Result<MFMailComposeResult, Error>?>) {
            _result = result
        }

        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            defer {
                controller.dismiss(animated: true)
            }
            if let error = error {
                self.result = .failure(error)
                return
            }
            self.result = .success(result)
        }
    }

    func makeCoordinator() -> Coordinator {
        return Coordinator(result: $result)
    }

    func makeUIViewController(context: UIViewControllerRepresentableContext<MailView>) -> MFMailComposeViewController {
        let vc = MFMailComposeViewController()
        vc.mailComposeDelegate = context.coordinator
        vc.setToRecipients([supportEmail])
        vc.setSubject("BabyPulse App Support")

        // Pre-fill with device and app information
        let appVersion = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "Unknown"
        let buildNumber = Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "Unknown"
        let deviceModel = UIDevice.current.model
        let systemVersion = UIDevice.current.systemVersion

        let messageBody = """


        ---
        App Version: \(appVersion) (\(buildNumber))
        Device: \(deviceModel)
        iOS Version: \(systemVersion)
        """

        vc.setMessageBody(messageBody, isHTML: false)
        return vc
    }

    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: UIViewControllerRepresentableContext<MailView>) {}
}

#Preview {
    NavigationStack {
        HelpAndSupportView()
    }
}