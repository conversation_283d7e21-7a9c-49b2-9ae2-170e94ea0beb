//
//  BabyProfileRow.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct BabyProfileRow: View {
    let baby: Baby
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                // Baby photo or placeholder
                ZStack {
                    Circle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 48, height: 48)
                    
                    if let photoData = baby.photoData, let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 48, height: 48)
                            .clipShape(Circle())
                    } else {
                        Image(systemName: "person.fill")
                            .font(.system(size: 24))
                            .foregroundColor(BabyPulseColors.secondary)
                    }
                }
                .overlay(
                    Circle()
                        .stroke(isSelected ? BabyPulseColors.primary : Color.clear, lineWidth: 2)
                )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(baby.name)
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.text)
                    
                    Text("Age: \(baby.ageDescription)")
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(BabyPulseColors.primary)
                        .font(.system(size: 20))
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    let baby = Baby(
        name: "Emma",
        birthDate: Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date(),
        gender: .female
    )
    
    return BabyProfileRow(baby: baby, isSelected: true, onSelect: {})
        .padding()
        .previewLayout(.sizeThatFits)
}
