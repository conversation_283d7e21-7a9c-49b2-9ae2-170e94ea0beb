import SwiftUI

struct AboutView: View {
    @Environment(\.openURL) var openURL
    @State private var showingCredits = false
    
    // URLs - Replace these with your actual URLs
    private let privacyPolicyURL = "https://www.babypulseapp.com/privacy"
    private let termsOfServiceURL = "https://www.babypulseapp.com/terms"
    private let websiteURL = "https://www.babypulseapp.com"
    private let githubURL = "https://github.com/yourusername/babypulse" // If open source
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                // Header Section
                headerSection
                
                // App Information
                appInfoSection
                
                // Features Overview
                featuresSection
                
                // Legal and Links
                linksSection
                
                // Credits
                creditsSection
                
                // Build Information
                buildInfoSection
                
                Spacer(minLength: 32)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 24)
        }
        .navigationTitle("About BabyPulse")
        .navigationBarTitleDisplayMode(.inline)
        .background(BabyPulseColors.background.opacity(0.5))
        .sheet(isPresented: $showingCredits) {
            CreditsView()
        }
    }
    
    // MARK: - UI Components
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // App Icon
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [BabyPulseColors.primary, BabyPulseColors.secondary]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: "heart.fill")
                        .font(.system(size: 48, weight: .semibold))
                        .foregroundColor(.white)
                )
                .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 10, x: 0, y: 5)
            
            VStack(spacing: 8) {
                Text("BabyPulse")
                    .font(BabyPulseTypography.title1())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)
                
                Text("Version \(appVersion()) (\(buildNumber()))")
                    .font(BabyPulseTypography.callout())
                    .foregroundColor(BabyPulseColors.textSecondary)
                
                Text("Your baby's health companion")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var appInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("About the App")
                .font(BabyPulseTypography.title3())
                .fontWeight(.semibold)
                .foregroundColor(BabyPulseColors.text)
            
            Text("BabyPulse is designed to help parents track their baby's health, growth, and development with ease. Our intuitive interface makes it simple to log feeding times, sleep patterns, diaper changes, and important milestones.")
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)
                .lineSpacing(2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Key Features")
                .font(BabyPulseTypography.title3())
                .fontWeight(.semibold)
                .foregroundColor(BabyPulseColors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                FeatureCard(
                    icon: "drop.fill",
                    title: "Feeding Tracker",
                    description: "Log feedings with amount and duration",
                    color: BabyPulseColors.feeding
                )
                
                FeatureCard(
                    icon: "moon.fill",
                    title: "Sleep Tracking",
                    description: "Monitor sleep patterns and duration",
                    color: BabyPulseColors.sleep
                )
                
                FeatureCard(
                    icon: "heart.fill",
                    title: "Diaper Changes",
                    description: "Track diaper changes and types",
                    color: BabyPulseColors.diaper
                )
                
                FeatureCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Growth Charts",
                    description: "Visualize weight and height progress",
                    color: BabyPulseColors.primary
                )
                
                FeatureCard(
                    icon: "brain.head.profile",
                    title: "AI Insights",
                    description: "Get personalized insights and tips",
                    color: BabyPulseColors.secondary
                )
                
                FeatureCard(
                    icon: "arrow.triangle.2.circlepath",
                    title: "Cloud Sync",
                    description: "Backup and sync across devices",
                    color: .green
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var linksSection: some View {
        VStack(spacing: 12) {
            LinkButton(
                icon: "globe",
                title: "Visit Our Website",
                url: websiteURL
            )
            
            LinkButton(
                icon: "hand.raised.fill",
                title: "Privacy Policy",
                url: privacyPolicyURL
            )
            
            LinkButton(
                icon: "doc.text.fill",
                title: "Terms of Service",
                url: termsOfServiceURL
            )
            
            // Uncomment if your app is open source
            // LinkButton(
            //     icon: "laptopcomputer",
            //     title: "View Source Code",
            //     url: githubURL
            // )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var creditsSection: some View {
        VStack(spacing: 12) {
            Button {
                showingCredits = true
            } label: {
                HStack {
                    Image(systemName: "person.3.fill")
                        .foregroundColor(BabyPulseColors.primary)
                    
                    Text("View Credits & Acknowledgments")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .font(.caption)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.tertiarySystemBackground))
                )
            }
        }
    }
    
    private var buildInfoSection: some View {
        VStack(spacing: 8) {
            Text("Build Information")
                .font(BabyPulseTypography.footnote())
                .foregroundColor(BabyPulseColors.textSecondary)
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Version:")
                    Text("Build:")
                    Text("iOS Target:")
                }
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textSecondary)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(appVersion())
                    Text(buildNumber())
                    Text(iOSVersion())
                }
                .font(BabyPulseTypography.caption())
                .foregroundColor(BabyPulseColors.textSecondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
    
    // MARK: - Helper Functions
    
    private func appVersion() -> String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "Unknown"
    }
    
    private func buildNumber() -> String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "Unknown"
    }
    
    private func iOSVersion() -> String {
        return Bundle.main.object(forInfoDictionaryKey: "MinimumOSVersion") as? String ?? "iOS 17.0+"
    }
}

// MARK: - Supporting Views

struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 48, height: 48)
                .background(
                    Circle()
                        .fill(color)
                        .shadow(color: color.opacity(0.3), radius: 4, x: 0, y: 2)
                )
            
            VStack(spacing: 4) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.text)
                    .multilineTextAlignment(.center)
                
                Text(description)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
}

struct LinkButton: View {
    let icon: String
    let title: String
    let url: String
    @Environment(\.openURL) var openURL
    
    var body: some View {
        Button {
            if let url = URL(string: url) {
                openURL(url)
            }
        } label: {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(BabyPulseColors.primary)
                    .frame(width: 24)
                
                Text(title)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.text)
                
                Spacer()
                
                Image(systemName: "arrow.up.right.square")
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .font(.caption)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.tertiarySystemBackground))
            )
        }
    }
}

struct CreditsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Credits & Acknowledgments")
                        .font(BabyPulseTypography.title2())
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        CreditSection(
                            title: "Development Team",
                            items: [
                                "iOS Development: BabyPulse Team",
                                "UI/UX Design: BabyPulse Design Team",
                                "Backend Services: BabyPulse Engineering"
                            ]
                        )
                        
                        CreditSection(
                            title: "Third Party Libraries",
                            items: [
                                "SwiftData: Apple Inc.",
                                "SwiftUI: Apple Inc.",
                                "RevenueCat: RevenueCat Inc.",
                                "Supabase: Supabase Inc."
                            ]
                        )
                        
                        CreditSection(
                            title: "Icons & Graphics",
                            items: [
                                "SF Symbols: Apple Inc.",
                                "App Icon: BabyPulse Design Team",
                                "Illustrations: BabyPulse Creative Team"
                            ]
                        )
                        
                        CreditSection(
                            title: "Special Thanks",
                            items: [
                                "Beta Testers: Our amazing community",
                                "Feedback Contributors: App Store users",
                                "Medical Advisors: Pediatric consultants"
                            ]
                        )
                    }
                    
                    Text("Thank you to all the parents who helped shape BabyPulse into the app it is today!")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.textSecondary)
                        .italic()
                        .padding(.top)
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CreditSection: View {
    let title: String
    let items: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(BabyPulseTypography.title3())
                .fontWeight(.semibold)
                .foregroundColor(BabyPulseColors.primary)
            
            ForEach(items, id: \.self) { item in
                Text("• \(item)")
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

#Preview {
    NavigationStack {
        AboutView()
    }
} 