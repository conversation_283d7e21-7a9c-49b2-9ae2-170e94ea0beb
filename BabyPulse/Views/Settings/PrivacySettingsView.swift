//
//  PrivacySettingsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct PrivacySettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingDeleteConfirmation = false
    @State private var showingExportCompletion = false
    @State private var isExporting = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header
                PrivacyHeaderView()

                // Data Collection
                VStack(spacing: 16) {
                    Text("Data Collection")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    ToggleRow(
                        icon: "chart.bar.fill",
                        iconColor: .blue,
                        title: "Analytics",
                        isOn: $viewModel.analyticsEnabled,
                        description: "Share anonymous usage data to help improve the app"
                    )

                    ToggleRow(
                        icon: "sparkles",
                        iconColor: .orange,
                        title: "Personalization",
                        isOn: $viewModel.personalizationEnabled,
                        description: "Allow the app to personalize content based on your usage"
                    )

                    ToggleRow(
                        icon: "rectangle.stack.badge.person.crop",
                        iconColor: .purple,
                        title: "Data Sharing",
                        isOn: $viewModel.dataSharingEnabled,
                        description: "Share anonymized data for research purposes"
                    )

                    ToggleRow(
                        icon: "location.fill",
                        iconColor: .green,
                        title: "Location Services",
                        isOn: $viewModel.locationEnabled,
                        description: "Use your location for weather-based advice"
                    )
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                // Data Management
                VStack(spacing: 16) {
                    Text("Data Management")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Button {
                        isExporting = true
                        Task {
                            try? await Task.sleep(nanoseconds: 2_000_000_000) // Simulate export for 2 seconds
                            showingExportCompletion = true
                            isExporting = false
                        }
                    } label: {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                                .font(.title3)
                            Text("Export Your Data")
                                .font(BabyPulseTypography.bodyBold())
                        }
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(BabyPulseColors.primary.opacity(0.1))
                        .cornerRadius(12)
                    }
                    .disabled(isExporting)
                    .overlay {
                        if isExporting {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                                .scaleEffect(1.2)
                        }
                    }

                    Button {
                        showingDeleteConfirmation = true
                    } label: {
                        HStack {
                            Image(systemName: "trash")
                                .font(.title3)
                            Text("Delete All Data")
                                .font(BabyPulseTypography.bodyBold())
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .cornerRadius(12)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                // Cloud data policy
                VStack(spacing: 16) {
                    Text("Cloud Data Storage")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    infoBox(
                        icon: "lock.shield",
                        title: "Your data is encrypted",
                        description: "All data stored in the cloud is encrypted end-to-end"
                    )

                    infoBox(
                        icon: "hourglass",
                        title: "Data retention",
                        description: "Your data is stored for as long as you have an account"
                    )

                    infoBox(
                        icon: "hand.raised",
                        title: "Data protection",
                        description: "We never sell your personal information to third parties"
                    )

                    Button {
                        if let url = URL(string: "https://babypulse.app/privacy") {
                            UIApplication.shared.open(url)
                        }
                    } label: {
                        HStack {
                            Text("View Privacy Policy")
                            Image(systemName: "arrow.up.right.square")
                        }
                        .foregroundColor(BabyPulseColors.primary)
                    }
                    .padding(.top, 8)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                )
            }
            .padding()
        }
        .navigationTitle("Privacy & Data")
        .navigationBarTitleDisplayMode(.inline)
        .background(BabyPulseColors.background.opacity(0.5))
        .onAppear {
            viewModel.loadData()
        }
        .alert(
            "Delete All Data",
            isPresented: $showingDeleteConfirmation
        ) {
            Button("Cancel", role: .cancel) {}

            Button("Delete", role: .destructive) {
                Task {
                    await viewModel.deleteAllData()
                }
            }
        } message: {
            Text("Are you sure you want to delete all your data? This action cannot be undone.")
        }
        .alert(
            "Data Exported",
            isPresented: $showingExportCompletion
        ) {
            Button("OK", role: .cancel) {}
        } message: {
            Text("Your data has been exported successfully. You can find the exported file in your Downloads folder.")
        }
        .alert(
            "Error",
            isPresented: Binding<Bool>(
                get: { viewModel.errorMessage != nil },
                set: { if !$0 { viewModel.errorMessage = nil } }
            ),
            actions: {
                Button("OK", role: .cancel) {}
            },
            message: {
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                }
            }
        )
    }

    @ViewBuilder
    private func infoBox(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(BabyPulseColors.primary)
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.text)

                Text(description)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground).opacity(0.5))
                .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 2)
        )
    }
}

struct PrivacyHeaderView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "hand.raised.fill")
                .font(.system(size: 60))
                .foregroundColor(BabyPulseColors.primary)

            Text("Privacy & Data")
                .font(BabyPulseTypography.title2())
                .foregroundColor(BabyPulseColors.text)

            Text("Control how your data is used and managed")
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
}

struct ToggleRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    @Binding var isOn: Bool
    var description: String? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.white)
                    .font(.subheadline)
                    .frame(width: 24, height: 24)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(iconColor)
                    )

                Text(title)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.text)

                Spacer()

                Toggle("", isOn: $isOn)
                    .labelsHidden()
            }

            if let description = description {
                Text(description)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .padding(.leading, 32)
            }
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    NavigationStack {
        PrivacySettingsView(viewModel: SettingsViewModel())
    }
}
