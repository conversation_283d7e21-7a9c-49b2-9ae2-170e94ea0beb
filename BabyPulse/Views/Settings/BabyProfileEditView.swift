//
//  BabyProfileEditView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import PhotosUI

struct BabyProfileEditView: View {
    @ObservedObject var viewModel: SettingsViewModel
    let isEditing: Bool
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        Form {
            Section {
                // Photo selection
                HStack {
                    Spacer()

                    PhotosPicker(selection: $viewModel.tempSelectedPhoto, matching: .images) {
                        ZStack {
                            Circle()
                                .fill(BabyPulseColors.lightGray)
                                .frame(width: 100, height: 100)

                            if let photoData = viewModel.tempPhotoData, let uiImage = UIImage(data: photoData) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 100, height: 100)
                                    .clipShape(Circle())
                            } else {
                                Image(systemName: "person.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(BabyPulseColors.secondary)
                            }

                            Circle()
                                .stroke(BabyPulseColors.primary, lineWidth: 2)
                                .frame(width: 100, height: 100)

                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Image(systemName: "camera.fill")
                                        .foregroundColor(.white)
                                        .padding(8)
                                        .background(Circle().fill(BabyPulseColors.primary))
                                }
                            }
                            .padding(8)
                        }
                        .frame(width: 100, height: 100)
                    }
                    .onChange(of: viewModel.tempSelectedPhoto) { _, _ in
                        Task {
                            await viewModel.loadPhotoData()
                        }
                    }

                    Spacer()
                }
                .padding(.vertical, 8)

                // Name field
                VStack(alignment: .leading, spacing: 4) {
                    TextField("Baby's Name", text: $viewModel.tempBabyName)
                        .font(BabyPulseTypography.body())
                        .padding(.vertical, 8)

                    if let nameError = viewModel.nameError {
                        Text(nameError)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.error)
                    }
                }

                // Birth date picker
                DatePicker(
                    "Birth Date",
                    selection: $viewModel.tempBabyBirthDate,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(.compact)
                .padding(.vertical, 8)

                // Gender picker
                Picker("Gender", selection: $viewModel.tempBabyGender) {
                    Text("Male").tag(Gender.male)
                    Text("Female").tag(Gender.female)
                    Text("Other").tag(Gender.other)
                }
                .pickerStyle(.segmented)
                .padding(.vertical, 8)
            } header: {
                Text("Baby Information")
            } footer: {
                if !isEditing && !viewModel.babies.isEmpty {
                    Text("Note: Full multi-baby support is coming in the next version. Adding multiple babies may have limited functionality.")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
            }
        }
        .navigationTitle(isEditing ? "Edit Baby Profile" : "Add Baby Profile")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    viewModel.saveBabyProfile()
                    if viewModel.nameError == nil {
                        dismiss()
                    }
                }
                .bold()
                .foregroundColor(BabyPulseColors.primary)
            }
        }
    }
}

#Preview {
    NavigationStack {
        BabyProfileEditView(viewModel: SettingsViewModel(), isEditing: false)
    }
}
