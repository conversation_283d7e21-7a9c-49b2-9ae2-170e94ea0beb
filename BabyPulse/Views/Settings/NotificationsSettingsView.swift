//
//  NotificationsSettingsView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import BabyPulse

struct NotificationsSettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingNotificationPermissionAlert = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header section
                NotificationsHeaderView()

                // Main Toggle Section
                VStack(spacing: 16) {
                    // Global notifications toggle
                    let notificationsEnabledBinding = Binding<Bool>(
                        get: { viewModel.notificationsEnabled },
                        set: { newValue in
                            if newValue && !viewModel.hasNotificationPermission {
                                showingNotificationPermissionAlert = true
                            }

                            // Update on the main thread to avoid threading issues
                            DispatchQueue.main.async {
                                viewModel.updateNotificationsEnabled(newValue)
                            }
                        }
                    )

                    BPToggleRow(
                        icon: "bell.fill",
                        iconColor: .purple,
                        title: "All Notifications",
                        isOn: notificationsEnabledBinding
                    )

                    if viewModel.notificationsEnabled {
                        Divider()

                        Text("Notification Types")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.top, 8)

                        // Individual notification types
                        BPToggleRow(
                            icon: "clock.fill",
                            iconColor: .blue,
                            title: "Feeding Reminders",
                            isOn: $viewModel.feedingNotificationsEnabled
                        )

                        BPToggleRow(
                            icon: "bed.double.fill",
                            iconColor: .indigo,
                            title: "Sleep Schedule",
                            isOn: $viewModel.sleepNotificationsEnabled
                        )

                        BPToggleRow(
                            icon: "chart.bar.fill",
                            iconColor: .green,
                            title: "Growth Milestones",
                            isOn: $viewModel.growthNotificationsEnabled
                        )

                        BPToggleRow(
                            icon: "heart.fill",
                            iconColor: .red,
                            title: "Health Alerts",
                            isOn: $viewModel.healthNotificationsEnabled,
                            description: "Important alerts about your baby's health"
                        )

                        BPToggleRow(
                            icon: "brain.fill",
                            iconColor: .orange,
                            title: "Development Insights",
                            isOn: $viewModel.insightNotificationsEnabled,
                            description: "Weekly insights about your baby's development"
                        )
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                // Quiet Hours Section
                if viewModel.notificationsEnabled {
                    VStack(spacing: 16) {
                        Text("Quiet Hours")
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(BabyPulseColors.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        BPToggleRow(
                            icon: "moon.fill",
                            iconColor: .indigo,
                            title: "Enable Quiet Hours",
                            isOn: $viewModel.quietHoursEnabled
                        )

                        if viewModel.quietHoursEnabled {
                            VStack(spacing: 12) {
                                HStack {
                                    Text("Start Time")
                                        .font(BabyPulseTypography.body())
                                        .foregroundColor(BabyPulseColors.text)

                                    Spacer()

                                    DatePicker(
                                        "",
                                        selection: $viewModel.quietHoursStart,
                                        displayedComponents: .hourAndMinute
                                    )
                                    .labelsHidden()
                                }

                                HStack {
                                    Text("End Time")
                                        .font(BabyPulseTypography.body())
                                        .foregroundColor(BabyPulseColors.text)

                                    Spacer()

                                    DatePicker(
                                        "",
                                        selection: $viewModel.quietHoursEnd,
                                        displayedComponents: .hourAndMinute
                                    )
                                    .labelsHidden()
                                }

                                HStack {
                                    Text("During quiet hours, only critical health alerts will be delivered")
                                        .font(BabyPulseTypography.caption())
                                        .foregroundColor(BabyPulseColors.textSecondary)
                                        .multilineTextAlignment(.leading)

                                    Spacer()
                                }
                                .padding(.top, 8)
                            }
                            .padding(.leading, 32)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                    )
                }

                // Information Section
                VStack(spacing: 16) {
                    Text("About Notifications")
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    VStack(alignment: .leading, spacing: 8) {
                        InfoRow(
                            icon: "gear",
                            title: "System Settings",
                            description: "You can also manage notifications in the iOS Settings app"
                        )

                        InfoRow(
                            icon: "exclamationmark.triangle",
                            title: "Critical Alerts",
                            description: "Health alerts can be delivered even during Focus modes"
                        )

                        InfoRow(
                            icon: "lock.shield",
                            title: "Privacy",
                            description: "Notification content is never shared with third parties"
                        )
                    }

                    Button {
                        UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!)
                    } label: {
                        HStack {
                            Image(systemName: "switch.2")
                            Text("Open System Settings")
                        }
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.primary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(BabyPulseColors.primary, lineWidth: 1)
                        )
                    }
                    .padding(.top, 8)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                )
            }
            .padding()
        }
        .navigationTitle("Notifications")
        .navigationBarTitleDisplayMode(.inline)
        .background(BabyPulseColors.background.opacity(0.5))
        .onAppear {
            viewModel.loadData()
            viewModel.checkNotificationPermission()
        }
        .alert(
            "Error",
            isPresented: Binding<Bool>(
                get: { viewModel.errorMessage != nil },
                set: { if !$0 { viewModel.errorMessage = nil } }
            ),
            actions: {
                Button("OK", role: .cancel) {}
            },
            message: {
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                }
            }
        )
        .alert("Notification Permission", isPresented: $showingNotificationPermissionAlert) {
            Button("Cancel", role: .cancel) {
                // Revert the toggle if user cancels
                viewModel.updateNotificationsEnabled(false)
            }

            Button("Settings") {
                UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!)
            }
        } message: {
            Text("BabyPulse needs permission to send notifications. Please enable notifications in Settings.")
        }
    }
}

struct NotificationsHeaderView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "bell.badge.fill")
                .font(.system(size: 60))
                .foregroundColor(BabyPulseColors.primary)

            Text("Notifications")
                .font(BabyPulseTypography.title2())
                .foregroundColor(BabyPulseColors.text)

            Text("Stay updated on your baby's schedule and get important reminders")
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
}

struct InfoRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .foregroundColor(BabyPulseColors.primary)
                .font(.body)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.text)

                Text(description)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

#Preview {
    NavigationStack {
        NotificationsSettingsView(viewModel: SettingsViewModel())
    }
}
