import SwiftUI
import StoreKit

struct SubscriptionView: View {
    @StateObject private var viewModel = SubscriptionViewModel()
    @EnvironmentObject private var supabaseService: SupabaseService
    @Environment(\.dismiss) private var dismiss
    @State private var showingLoginPrompt = false
    
    var body: some View {
        List {
            // Header
            Section {
                VStack(spacing: BabyPulseLayout.spacingMD) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.yellow)
                        .padding(.top, BabyPulseLayout.spacingLG)
                    
                    Text("BabyPulse Premium")
                        .font(BabyPulseTypography.title2())
                        .foregroundColor(BabyPulseColors.secondary)
                    
                    Text("Unlock all premium features and sync your data across devices.")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.bottom, BabyPulseLayout.spacingMD)
                }
                .frame(maxWidth: .infinity)
                .listRowInsets(EdgeInsets())
                .background(Color.clear)
            }
            .listRowBackground(Color.clear)
            
            // Current status
            Section("Current Status") {
                HStack {
                    Image(systemName: statusIcon)
                        .foregroundColor(statusColor)
                    
                    Text(statusText)
                        .font(BabyPulseTypography.body())
                }
            }
            
            // Premium features
            Section("Premium Features") {
                FeatureRow(icon: "arrow.triangle.2.circlepath", title: "Cloud Sync", description: "Sync your data across all your devices")
                FeatureRow(icon: "chart.bar.fill", title: "Advanced Analytics", description: "Get detailed insights and trends")
                FeatureRow(icon: "person.2.fill", title: "Multiple Babies", description: "Track unlimited baby profiles")
                FeatureRow(icon: "square.and.arrow.up.fill", title: "Data Export", description: "Export your data in various formats")
                FeatureRow(icon: "xmark.octagon.fill", title: "Ad-Free Experience", description: "Enjoy the app without advertisements")
            }
            
            // Subscription options
            if !viewModel.hasPremiumAccess() {
                Section("Subscription Options") {
                    ForEach(viewModel.products, id: \.id) { product in
                        Button {
                            viewModel.selectedProductId = product.id
                        } label: {
                            HStack {
                                VStack(alignment: .leading) {
                                    Text(product.displayName)
                                        .font(BabyPulseTypography.bodyBold())
                                        .foregroundColor(BabyPulseColors.secondary)
                                    
                                    Text(product.description)
                                        .font(BabyPulseTypography.caption())
                                        .foregroundColor(BabyPulseColors.secondary.opacity(0.8))
                                }
                                
                                Spacer()
                                
                                Text(viewModel.formattedPrice(for: product))
                                    .font(BabyPulseTypography.bodyBold())
                                    .foregroundColor(BabyPulseColors.primary)
                                
                                if viewModel.selectedProductId == product.id {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(BabyPulseColors.primary)
                                        .padding(.leading, 4)
                                }
                            }
                        }
                        .buttonStyle(.plain)
                    }
                }
                
                // Purchase button
                Section {
                    Button {
                        if !supabaseService.isAuthenticated {
                            showingLoginPrompt = true
                        } else {
                            Task {
                                await viewModel.purchaseSelectedProduct()
                            }
                        }
                    } label: {
                        Text("Subscribe Now")
                            .font(BabyPulseTypography.bodyBold())
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(BabyPulseColors.primary)
                            .cornerRadius(BabyPulseLayout.cornerRadiusMD)
                    }
                    .listRowInsets(EdgeInsets())
                    .disabled(viewModel.isLoading || viewModel.selectedProductId == nil)
                    
                    Button {
                        Task {
                            await viewModel.restorePurchases()
                        }
                    } label: {
                        Text("Restore Purchases")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.primary)
                            .frame(maxWidth: .infinity)
                            .padding()
                    }
                    .listRowInsets(EdgeInsets())
                    .disabled(viewModel.isLoading)
                }
            }
            
            // Terms and privacy
            Section {
                VStack(spacing: BabyPulseLayout.spacingSM) {
                    Text("By subscribing, you agree to our Terms of Service and Privacy Policy.")
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.secondary.opacity(0.6))
                        .multilineTextAlignment(.center)
                    
                    Text("Subscriptions will automatically renew unless canceled at least 24 hours before the end of the current period. You can cancel anytime in your App Store settings.")
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.secondary.opacity(0.6))
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, BabyPulseLayout.paddingSM)
            }
            .listRowBackground(Color.clear)
        }
        .navigationTitle("Subscription")
        .navigationBarTitleDisplayMode(.inline)
        .overlay {
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.2))
            }
        }
        .alert("Error", isPresented: .init(get: { viewModel.errorMessage != nil }, set: { if !$0 { viewModel.errorMessage = nil } })) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Sign In Required", isPresented: $showingLoginPrompt) {
            Button("Sign In") {
                // Navigate to account view
                dismiss()
            }
            
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("You need to sign in before subscribing to premium features.")
        }
        .alert("Purchase Successful", isPresented: $viewModel.purchaseSuccess) {
            Button("OK") {
                viewModel.purchaseSuccess = false
                dismiss()
            }
        } message: {
            Text("Thank you for subscribing to BabyPulse Premium! You now have access to all premium features.")
        }
    }
    
    // Status icon
    private var statusIcon: String {
        switch viewModel.subscriptionStatus {
        case .free:
            return "xmark.circle"
        case .trial:
            return "clock.fill"
        case .premium:
            return "checkmark.seal.fill"
        case .expired:
            return "exclamationmark.triangle"
        }
    }
    
    // Status color
    private var statusColor: Color {
        switch viewModel.subscriptionStatus {
        case .free:
            return .gray
        case .trial:
            return .orange
        case .premium:
            return .green
        case .expired:
            return .red
        }
    }
    
    // Status text
    private var statusText: String {
        switch viewModel.subscriptionStatus {
        case .free:
            return "Free Plan"
        case .trial:
            return "Trial Period"
        case .premium:
            return "Premium Active"
        case .expired:
            return "Subscription Expired"
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(BabyPulseColors.primary)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.secondary)
                
                Text(description)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.secondary.opacity(0.8))
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    NavigationStack {
        SubscriptionView()
            .environmentObject(SupabaseService.shared)
    }
}
