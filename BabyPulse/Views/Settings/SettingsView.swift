import SwiftUI
import SwiftData
import StoreKit // Import StoreKit for app rating

#if DEBUG
// Import test view for debugging
import struct BabyPulse.TestingUtilities
#endif

struct SettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var viewModel = SettingsViewModel()
    @EnvironmentObject private var supabaseService: SupabaseService
    @EnvironmentObject private var revenueCatService: RevenueCatService

    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                BabyPulseColors.background.opacity(0.5)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // The List below should automatically respect the safe area.
                    List {
                    // Profile Section
                    Section("Profile") {
                        if viewModel.babies.isEmpty {
                            NavigationLink(destination: BabyProfileListView(viewModel: viewModel)) {
                                SettingsRowView(
                                    icon: "person.fill",
                                    iconColor: .blue,
                                    title: "Baby Profile",
                                    value: "Add Baby"
                                )
                            }
                            .listRowBackground(Color(.systemBackground).opacity(0.8))
                        } else {
                            let selectedBaby = viewModel.babies.first(where: { $0.id == viewModel.selectedBabyID }) ?? viewModel.babies.first!

                            NavigationLink(destination: BabyProfileListView(viewModel: viewModel)) {
                                BabyProfileRow(
                                    baby: selectedBaby,
                                    isSelected: true,
                                    onSelect: {}
                                )
                            }
                            .listRowBackground(Color(.systemBackground).opacity(0.8))
                        }
                    }
                    .listSectionSeparator(.hidden, edges: .top)

                    // Preferences Section
                    Section("Preferences") {
                        NavigationLink(destination: NotificationsSettingsView(viewModel: viewModel)) {
                            SettingsRowView(
                                icon: "bell.fill",
                                iconColor: .purple,
                                title: "Notifications",
                                value: viewModel.notificationsEnabled ? "On" : "Off"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        NavigationLink(destination: UnitsSettingsView(viewModel: viewModel)) {
                            SettingsRowView(
                                icon: "ruler",
                                iconColor: .green,
                                title: "Units",
                                value: viewModel.unitSystem.rawValue.capitalized
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        // Using a local binding to avoid direct binding to the viewModel property
                        let darkModeBinding = Binding<Bool>(
                            get: { viewModel.darkModeEnabled },
                            set: { newValue in
                                // Update on the main thread to avoid threading issues
                                DispatchQueue.main.async {
                                    viewModel.updateDarkModeEnabled(newValue)
                                }
                            }
                        )

                        HStack {
                            SettingsRowView(
                                icon: "moon.fill",
                                iconColor: .yellow,
                                title: "Appearance"
                            )

                            Spacer()

                            Picker("", selection: darkModeBinding) {
                                Text("Light").tag(false)
                                Text("Dark").tag(true)
                                Text("System").tag(nil as Bool?)
                            }
                            .pickerStyle(.segmented)
                            .frame(width: 180)
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))
                    }

                    // Premium Features Section
                    Section("Premium Features") {
                        NavigationLink(destination: AccountView()) {
                            SettingsRowView(
                                icon: "person.crop.circle.fill",
                                iconColor: .blue,
                                title: "Account",
                                value: supabaseService.isAuthenticated ? "Signed In" : "Sign In"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        NavigationLink(destination: SubscriptionView()) {
                            SettingsRowView(
                                icon: "crown.fill",
                                iconColor: .yellow,
                                title: "Subscription",
                                value: revenueCatService.hasPremiumAccess() ? "Premium" : "Upgrade"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        NavigationLink(destination: BackupSettingsView()) {
                            SettingsRowView(
                                icon: "arrow.triangle.2.circlepath",
                                iconColor: .green,
                                title: "Backup & Sync",
                                value: supabaseService.isAuthenticated && revenueCatService.hasPremiumAccess() ? "Enabled" : "Premium"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))
                    }

                    // Privacy Section
                    Section("Privacy") {
                        NavigationLink(destination: PrivacySettingsView(viewModel: viewModel)) {
                            SettingsRowView(
                                icon: "hand.raised.fill",
                                iconColor: .red,
                                title: "Privacy & Data"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))
                    }

                    // About Section
                    Section("About") {
                        NavigationLink(destination: AboutView()) {
                            SettingsRowView(
                                icon: "info.circle.fill",
                                iconColor: .blue,
                                title: "About BabyPulse"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        NavigationLink(destination: HelpAndSupportView()) {
                            SettingsRowView(
                                icon: "questionmark.circle.fill",
                                iconColor: .purple,
                                title: "Help & Support"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        Button(action: {
                            // Request in-app review
                            if let scene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
                                SKStoreReviewController.requestReview(in: scene)
                            }
                        }) {
                            SettingsRowView(
                                icon: "star.fill",
                                iconColor: .yellow,
                                title: "Rate the App"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        NavigationLink(destination: WhatsNewView()) {
                            SettingsRowView(
                                icon: "sparkles",
                                iconColor: .orange,
                                title: "What's New"
                            )
                            .overlay(
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 10, height: 10)
                                    .offset(x: -8, y: -8),
                                alignment: .topTrailing
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))
                    }

                    #if DEBUG
                    // Debug Section (only visible in DEBUG builds)
                    Section("Developer Tools") {
                        NavigationLink {
                            TemplateTestView()
                        } label: {
                            SettingsRowView(
                                icon: "ladybug.fill",
                                iconColor: .red,
                                title: "Template Testing"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        Button {
                            TestingUtilities.resetApp(modelContext: modelContext)
                            // Notify user that they need to restart the app to see changes
                            viewModel.errorMessage = "App reset completed. Please restart the app to see changes."
                        } label: {
                            SettingsRowView(
                                icon: "arrow.counterclockwise",
                                iconColor: .red,
                                title: "Reset App"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        Button {
                            TestingUtilities.skipOnboarding(modelContext: modelContext)
                            // Notify user that they need to restart the app to see changes
                            viewModel.errorMessage = "Onboarding skipped. Please restart the app to see changes."
                        } label: {
                            SettingsRowView(
                                icon: "forward.fill",
                                iconColor: .blue,
                                title: "Skip Onboarding"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))

                        Button {
                            // First get the current baby
                            let babyDescriptor = FetchDescriptor<Baby>()
                            if let babies = try? modelContext.fetch(babyDescriptor), let baby = babies.first {
                                // Generate 30 days of test data for charts
                                TestingUtilities.generateChartTestData(for: baby, modelContext: modelContext, days: 30)
                                viewModel.errorMessage = "Chart data generated successfully for 30 days."
                            } else {
                                viewModel.errorMessage = "No baby found. Please create a baby profile first."
                            }
                        } label: {
                            SettingsRowView(
                                icon: "chart.line.uptrend.xyaxis",
                                iconColor: .green,
                                title: "Generate Chart Data"
                            )
                        }
                        .listRowBackground(Color(.systemBackground).opacity(0.8))
                    }
                    #endif
                    }
                    .listStyle(.insetGrouped)
                    .background(BabyPulseColors.background.opacity(0.5))
                    .scrollContentBackground(.hidden)
                }
            }
            .onAppear {
                viewModel.modelContext = modelContext
                viewModel.loadData()
            }
            .alert(
                "Error",
                isPresented: Binding<Bool>(
                    get: { viewModel.errorMessage != nil },
                    set: { if !$0 { viewModel.errorMessage = nil } }
                ),
                actions: {
                    Button("OK", role: .cancel) {}
                },
                message: {
                    if let errorMessage = viewModel.errorMessage {
                        Text(errorMessage)
                    }
                }
            )
        }
    }


}

struct SettingsRowView: View {
    let icon: String
    let iconColor: Color
    let title: String
    var value: String? = nil
    @State private var isPressed = false

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .foregroundColor(.white)
                .font(.system(size: 16, weight: .semibold))
                .frame(width: 30, height: 30)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [iconColor, iconColor.opacity(0.8)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: iconColor.opacity(0.3), radius: 3, x: 0, y: 2)
                )
                .accessibilityHidden(true)

            Text(title)
                .font(BabyPulseTypography.bodyBold())
                .foregroundColor(BabyPulseColors.text)
                .lineLimit(1)
                .accessibilityLabel(title)

            Spacer()

            if let value = value {
                Text(value)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color(UIColor.tertiarySystemBackground))
                    )
                    .lineLimit(1)
                    .fixedSize(horizontal: true, vertical: false)
                    .accessibilityLabel(value)
            } else {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.vertical, 10)
        .contentShape(Rectangle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2), value: isPressed)
        .onLongPressGesture(minimumDuration: .infinity, maximumDistance: .infinity, pressing: { pressing in
            self.isPressed = pressing
        }, perform: {})
    }
}

struct WhatsNewView: View {
    var body: some View {
        List {
            Section {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "sparkles")
                            .foregroundColor(.orange)
                        Text("Version 1.2.0")
                            .font(.headline)
                    }

                    Text("• Improved settings interface")
                    Text("• Enhanced baby profile management")
                    Text("• Better unit conversion tools")
                    Text("• More detailed sync status")
                    Text("• Improved dark mode controls")
                }
                .padding(.vertical, 8)
            }

            Section {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "clock.arrow.circlepath")
                            .foregroundColor(.blue)
                        Text("Version 1.1.0")
                            .font(.headline)
                    }

                    Text("• Added baby growth tracking")
                    Text("• Improved sync reliability")
                    Text("• Enhanced chart visualizations")
                    Text("• Bug fixes and performance improvements")
                }
                .padding(.vertical, 8)
            }
        }
        .navigationTitle("What's New")
        .navigationBarTitleDisplayMode(.inline)
        .listStyle(.insetGrouped)
    }
}

#Preview {
    SettingsView()
        .modelContainer(for: [Baby.self, UserPreferences.self], inMemory: true)
}

#if DEBUG
// Simple view to test prompt templates
struct TemplateTestView: View {
    @State private var message = "Ready to test"
    @State private var isLoading = false

    var body: some View {
        VStack(spacing: 20) {
            Text("Template Testing")
                .font(.title)
                .padding()

            Text(message)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
                .multilineTextAlignment(.leading)

            Button("Test Template Loading") {
                testTemplateLoading()
            }
            .buttonStyle(.borderedProminent)

            Spacer()
        }
        .padding()
        .disabled(isLoading)
        .overlay {
            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .background(Color.white.opacity(0.8))
                    .cornerRadius(10)
                    .frame(width: 100, height: 100)
            }
        }
    }

    private func testTemplateLoading() {
        isLoading = true
        message = "Testing template loading..."

        // Test if the templates are loaded correctly
        let templateManager = PromptTemplateManager.shared

        // Try to get templates for growth and health insights
        let weightGainTemplate = templateManager.getTemplate(named: "weight_gain")
        let heightIncreaseTemplate = templateManager.getTemplate(named: "height_increase")
        let headCircumferenceTemplate = templateManager.getTemplate(named: "head_circumference_increase")
        let temperatureTemplate = templateManager.getTemplate(named: "temperature_fluctuation")
        let symptomTemplate = templateManager.getTemplate(named: "symptom_pattern")

        // Check if templates were found
        var foundTemplates: [String] = []
        var missingTemplates: [String] = []

        if weightGainTemplate != nil { foundTemplates.append("weight_gain") } else { missingTemplates.append("weight_gain") }
        if heightIncreaseTemplate != nil { foundTemplates.append("height_increase") } else { missingTemplates.append("height_increase") }
        if headCircumferenceTemplate != nil { foundTemplates.append("head_circumference_increase") } else { missingTemplates.append("head_circumference_increase") }
        if temperatureTemplate != nil { foundTemplates.append("temperature_fluctuation") } else { missingTemplates.append("temperature_fluctuation") }
        if symptomTemplate != nil { foundTemplates.append("symptom_pattern") } else { missingTemplates.append("symptom_pattern") }

        // Update message
        if missingTemplates.isEmpty {
            message = "All templates loaded successfully:\n\n" + foundTemplates.joined(separator: "\n")
        } else {
            message = "Missing templates:\n" + missingTemplates.joined(separator: "\n") + "\n\nFound templates:\n" + foundTemplates.joined(separator: "\n")
        }

        isLoading = false
    }
}
#endif
