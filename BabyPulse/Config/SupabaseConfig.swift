import Foundation

/// Configuration for Supabase services
enum SupabaseConfig {
    /// The URL of the Supabase project
    static let projectURL = "https://fzyjdcdkqsafgboxodmi.supabase.co"
    
    /// The anonymous API key for the Supabase project
    static let anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ6eWpkY2RrcXNhZmdib3hvZG1pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1NTgzMDcsImV4cCI6MjA2MTEzNDMwN30.dpr_8yWwHTWp-S-ddCa3gsH5jdQevIZaezg_4oSxb4Q"
    
    /// The URL for the Supabase Storage service
    static let storageURL = "\(projectURL)/storage/v1"
    
    /// The URL for the Supabase Edge Functions
    static let functionsURL = "\(projectURL)/functions/v1"
    
    /// The name of the storage bucket for baby media
    static let mediaBucketName = "baby-media"
}
