import Foundation

/// Configuration for RevenueCat services
enum RevenueCatConfig {
    /// The API key for the RevenueCat project
    static let apiKey = "appl_YOUR_API_KEY_HERE" // Replace with your actual API key
    
    /// The entitlement ID for premium features
    static let premiumEntitlementID = "premium"
    
    /// Product IDs for subscriptions
    enum ProductIDs {
        /// Monthly subscription
        static let monthly = "ai.babypulse.subscription.monthly"
        
        /// Annual subscription
        static let annual = "ai.babypulse.subscription.annual"
        
        /// Lifetime purchase
        static let lifetime = "ai.babypulse.purchase.lifetime"
        
        /// All product IDs
        static let all = [monthly, annual, lifetime]
    }
}
