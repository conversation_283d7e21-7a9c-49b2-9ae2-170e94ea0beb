# BabyPulse - Technology Stack & Architecture

## Development Environment
- **Xcode:** 16+
- **Swift:** 5.9+
- **iOS Target:** 17.0+ (iPhone & iPad)
- **Version Control:** Git
- **Dependency Management:** Swift Package Manager (SPM)

## Architecture Overview
BabyPulse will implement the MVVM (Model-View-ViewModel) architecture pattern, which aligns perfectly with SwiftUI's declarative approach to UI development. This architecture provides a clean separation of concerns, testability, and maintainability.

### MVVM Implementation
![MVVM Architecture](https://miro.medium.com/max/1400/1*FVJ8d3ZBPg_wHasFrHZ3wA.png)

1. **Model Layer:**
   - Swift data models using the `@Model` macro for SwiftData
   - Repository pattern for data access abstraction
   - Service classes for external API interactions (OpenRouter, Supabase)

2. **View Layer:**
   - Pure SwiftUI views with minimal business logic
   - Composition-based approach with reusable components
   - Styling applied through custom ViewModifiers and Extensions

3. **ViewModel Layer:**
   - State management and business logic
   - Data transformation for view consumption
   - Command handling and user action processing
   - Asynchronous operations management

## Navigation Architecture
BabyPulse will utilize SwiftUI's native navigation components:

1. **TabView:** Main navigation between primary app sections (Home, Logs, Chat, Settings)
2. **NavigationStack:** Hierarchical navigation within each tab
3. **Modal Presentations:** Using `.sheet()` and `.fullScreenCover()` for modal workflows

### Navigation Flow Diagram
```
App
├─ TabView
│  ├─ Home Tab (NavigationStack)
│  │  ├─ HomeView
│  │  ├─ InsightDetailView
│  │  └─ RiskAssessmentView
│  │
│  ├─ Logs Tab (NavigationStack)
│  │  ├─ LogsView
│  │  ├─ LogDetailView
│  │  └─ FilterView
│  │
│  ├─ Chat Tab (NavigationStack)
│  │  ├─ ChatView
│  │  └─ ChatHistoryView
│  │
│  └─ Settings Tab (NavigationStack)
│     ├─ SettingsView
│     ├─ ProfileSettingsView
│     ├─ NotificationSettingsView
│     ├─ PrivacySettingsView
│     └─ SubscriptionView
│
├─ Modal Flows
│  ├─ OnboardingFlow (fullScreenCover)
│  ├─ LogEntryFlow (sheet)
│  └─ SubscriptionFlow (sheet)
```

## Dependency Injection
BabyPulse will use Environment-based Dependency Injection, leveraging SwiftUI's built-in environment system:

```swift
// Define environment keys
struct DataRepositoryKey: EnvironmentKey {
    static let defaultValue: DataRepositoryProtocol = MockDataRepository()
}

// Extend EnvironmentValues
extension EnvironmentValues {
    var dataRepository: DataRepositoryProtocol {
        get { self[DataRepositoryKey.self] }
        set { self[DataRepositoryKey.self] = newValue }
    }
}

// Usage in Views
struct ContentView: View {
    @Environment(\.dataRepository) private var dataRepository
    
    var body: some View {
        // Use dataRepository
    }
}

// Provide dependencies
@main
struct BabyPulseApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.dataRepository, ProductionDataRepository())
        }
    }
}
```

## Data Management

### Local Data Storage
- **SwiftData:** Primary local database for all app data
  - Models defined with `@Model` macro
  - Queries using `@Query` property wrapper
  - Relationships managed through SwiftData's relationship capabilities
  - Migrations handled through SwiftData's versioning system

### Cloud Synchronization (Premium Tier)
- **Supabase:** Backend-as-a-Service platform
  - **Authentication:** User account management
  - **PostgreSQL:** Cloud database for data synchronization
  - **Storage:** Cloud storage for images and attachments
  - **Row Level Security (RLS):** Data access control policies

### Synchronization Strategy
- Offline-first approach with local-first data storage
- Background synchronization when network is available
- Conflict resolution with timestamp-based "last write wins" strategy
- Selective sync for premium users (all data) vs. free users (limited history)

## External Services Integration

### LLM Integration
- **OpenRouter API:** Client-side API calls to access LLM models
  - Primary models: Gemini 2.5 Pro/DeepSeek
  - Prompt engineering for specific use cases
  - Response parsing and formatting
  - Error handling and fallback strategies

### Monetization
- **Google AdMob:** Banner and interstitial ads for free tier
- **RevenueCat:** Subscription management
  - In-app purchases for premium features
  - Subscription tiers and trial management
  - Receipt validation and entitlement checking

## Core Libraries & Frameworks

### SwiftUI
- Primary UI framework for all views
- Custom components and modifiers
- Animations and transitions
- Accessibility implementation

### Swift Charts
- Data visualization for growth trends
- Feeding, diaper, and sleep pattern charts
- Interactive chart elements

### URLSession
- Networking layer for API communication
- Async/await implementation
- Request/response handling
- Error management

### SF Symbols
- Consistent iconography throughout the app
- Dynamic configuration for accessibility
- Custom rendering modes for brand alignment

### Swift Package Manager (SPM)
- Dependency management for third-party libraries
- Version control and updates
- Module organization

## Performance Considerations
- Lazy loading of views and data
- Background processing for intensive operations
- Memory management for image assets
- Battery optimization strategies

## Security Measures
- Secure storage of sensitive data
- API key management
- Authentication token handling
- Data encryption for cloud sync
- Privacy-focused design
