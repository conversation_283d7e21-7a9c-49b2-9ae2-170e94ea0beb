# BabyPulse Data Structure Optimization Plan

## Current State Analysis

### SwiftData Models
The app uses SwiftData for local storage with the following models:
- `Baby`: Core model for baby profiles
- `UserPreferences`: User settings and preferences
- `FeedingEntry`: Tracking feeding activities
- `DiaperEntry`: Tracking diaper changes
- `SleepEntry`: Tracking sleep patterns
- `GrowthEntry`: Tracking growth measurements
- `HealthEntry`: Tracking health-related data
- `ChatMessage`: Messages in the chat system
- `ChatThread`: Threads containing chat messages

### Supabase Tables
The Supabase database has the following tables:
- `babies`: Stores baby profiles
- `user_preferences`: Stores user settings
- `feeding_entries`: Stores feeding activities
- `health_entry`: Stores health-related data
- `growth_entry`: Stores growth measurements
- `chat_thread`: Stores chat threads
- `chat_message`: Stores chat messages
- `chat_messages`: Redundant table with `chat_message`
- `session`: Purpose unclear, possibly for tracking activities
- `measurement`: Purpose unclear, possibly related to `session`
- `event`: Purpose unclear, possibly for notifications
- `media`: Purpose unclear, possibly for storing media files

### Identified Issues
1. **Missing Tables**: No tables for `diaper_entries` and `sleep_entries`
2. **Redundant Tables**: Both `chat_message` and `chat_messages` exist
3. **Unused Tables**: `session`, `measurement`, `event`, and `media` don't have clear counterparts in SwiftData
4. **Naming Inconsistencies**: Different naming conventions between SwiftData and Supabase
5. **Sync Implementation**: The `syncData` method in `SupabaseService` is just a mock implementation

## Optimization Plan

### Phase 1: Database Schema Optimization

1. **Create Missing Tables**
   - `diaper_entries`: For tracking diaper changes
   ```sql
   CREATE TABLE diaper_entries (
       id UUID PRIMARY KEY,
       baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
       user_id UUID REFERENCES auth.users(id),
       timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
       diaper_type TEXT NOT NULL,
       poop_color TEXT,
       poop_consistency TEXT,
       notes TEXT,
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
   );

   -- Add indexes
   CREATE INDEX idx_diaper_entries_baby_id ON diaper_entries(baby_id);
   CREATE INDEX idx_diaper_entries_timestamp ON diaper_entries(timestamp);

   -- Add RLS policy
   ALTER TABLE diaper_entries ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "Users can only access their own diaper entries"
       ON diaper_entries
       USING (user_id = auth.uid());
   ```

   - `sleep_entries`: For tracking sleep patterns
   ```sql
   CREATE TABLE sleep_entries (
       id UUID PRIMARY KEY,
       baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
       user_id UUID REFERENCES auth.users(id),
       start_ts TIMESTAMP WITH TIME ZONE NOT NULL,
       end_ts TIMESTAMP WITH TIME ZONE,
       duration INTEGER,
       location TEXT,
       notes TEXT,
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
   );

   -- Add indexes
   CREATE INDEX idx_sleep_entries_baby_id ON sleep_entries(baby_id);
   CREATE INDEX idx_sleep_entries_start_ts ON sleep_entries(start_ts);

   -- Add RLS policy
   ALTER TABLE sleep_entries ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "Users can only access their own sleep entries"
       ON sleep_entries
       USING (user_id = auth.uid());
   ```

2. **Consolidate Redundant Tables**
   - Keep `chat_message` (singular)
   - Drop `chat_messages` (plural)
   ```sql
   -- First, ensure all data is migrated if needed
   -- Then drop the redundant table
   DROP TABLE IF EXISTS chat_messages;
   ```

3. **Evaluate Unused Tables**
   - Determine if `session`, `measurement`, `event`, and `media` tables are needed
   - Document their purpose if keeping them
   - Drop them if not needed
   ```sql
   -- Example of dropping an unused table
   DROP TABLE IF EXISTS session;
   DROP TABLE IF EXISTS measurement;
   ```

4. **Add Proper Indexes**
   - Add indexes for frequently queried fields
   ```sql
   -- Example for feeding_entries
   CREATE INDEX IF NOT EXISTS idx_feeding_entries_baby_id ON feeding_entries(baby_id);
   CREATE INDEX IF NOT EXISTS idx_feeding_entries_timestamp ON feeding_entries(timestamp);
   
   -- Example for health_entry
   CREATE INDEX IF NOT EXISTS idx_health_entry_baby_id ON health_entry(baby_id);
   CREATE INDEX IF NOT EXISTS idx_health_entry_ts ON health_entry(ts);
   
   -- Example for growth_entry
   CREATE INDEX IF NOT EXISTS idx_growth_entry_baby_id ON growth_entry(baby_id);
   CREATE INDEX IF NOT EXISTS idx_growth_entry_ts ON growth_entry(ts);
   ```

5. **Ensure RLS Policies**
   - Add Row Level Security policies to all tables
   ```sql
   -- Example for health_entry
   ALTER TABLE health_entry ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "Users can only access their own health entries"
       ON health_entry
       USING (user_id = auth.uid());
       
   -- Example for growth_entry
   ALTER TABLE growth_entry ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "Users can only access their own growth entries"
       ON growth_entry
       USING (user_id = auth.uid());
   ```

### Phase 2: Sync Logic Implementation

1. **Update SupabaseService.syncData Method**
   - Implement proper API calls to insert/update data
   - Add error handling and retry logic
   ```swift
   func syncData(type: String, babyId: String, data: [String: Any]) async throws {
       guard let supabaseClient = client else {
           throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Supabase client not initialized"])
       }
       
       // Determine the table name based on the type
       let tableName: String
       switch type {
       case "feeding":
           tableName = "feeding_entries"
       case "diaper":
           tableName = "diaper_entries"
       case "sleep":
           tableName = "sleep_entries"
       case "health":
           tableName = "health_entry"
       case "growth":
           tableName = "growth_entry"
       default:
           throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
       }
       
       do {
           // Upsert the data (insert or update)
           let response = try await supabaseClient.database
               .from(tableName)
               .upsert(data)
               .execute()
           
           // Check for errors
           if let error = response.error {
               throw error
           }
           
           print("Successfully synced \(type) data for baby \(babyId)")
       } catch {
           print("Error syncing \(type) data for baby \(babyId): \(error)")
           throw error
       }
   }
   ```

2. **Implement Bidirectional Sync**
   - Add methods to fetch data from Supabase
   - Add logic to merge remote and local data
   ```swift
   func fetchData<T: Decodable>(type: String, babyId: String) async throws -> [T] {
       guard let supabaseClient = client else {
           throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Supabase client not initialized"])
       }
       
       // Determine the table name based on the type
       let tableName: String
       switch type {
       case "feeding":
           tableName = "feeding_entries"
       case "diaper":
           tableName = "diaper_entries"
       case "sleep":
           tableName = "sleep_entries"
       case "health":
           tableName = "health_entry"
       case "growth":
           tableName = "growth_entry"
       default:
           throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
       }
       
       do {
           // Fetch data for the specified baby
           let response = try await supabaseClient.database
               .from(tableName)
               .select()
               .eq("baby_id", value: babyId)
               .execute()
           
           // Check for errors
           if let error = response.error {
               throw error
           }
           
           // Decode the response
           let data = try JSONDecoder().decode([T].self, from: response.data)
           return data
       } catch {
           print("Error fetching \(type) data for baby \(babyId): \(error)")
           throw error
       }
   }
   ```

3. **Add Conflict Resolution**
   - Use timestamps to determine which version is newer
   - Implement a strategy for handling conflicts
   ```swift
   func resolveConflicts<T: Identifiable & Timestamped>(local: [T], remote: [T]) -> (toUpdate: [T], toInsert: [T]) {
       var toUpdate: [T] = []
       var toInsert: [T] = []
       
       // Create dictionaries for faster lookup
       let localDict = Dictionary(uniqueKeysWithValues: local.map { ($0.id, $0) })
       let remoteDict = Dictionary(uniqueKeysWithValues: remote.map { ($0.id, $0) })
       
       // Find items to update (exist in both local and remote)
       for (id, remoteItem) in remoteDict {
           if let localItem = localDict[id] {
               // If remote is newer, update local
               if remoteItem.updatedAt > localItem.updatedAt {
                   toUpdate.append(remoteItem)
               }
           } else {
               // If exists in remote but not local, insert
               toInsert.append(remoteItem)
           }
       }
       
       return (toUpdate, toInsert)
   }
   ```

### Phase 3: Code Refactoring

1. **Update SyncManager**
   - Ensure all entry types have proper sync methods
   - Standardize the sync process across all entry types
   - Add bidirectional sync support

2. **Update SwiftData Models**
   - Ensure property names match column names (accounting for camelCase vs. snake_case)
   - Add any missing properties

3. **Add Comprehensive Error Handling**
   - Handle network errors
   - Handle validation errors
   - Handle authentication errors

### Phase 4: Documentation and Testing

1. **Document the Database Schema**
   - Create a comprehensive documentation of all tables and columns
   - Include relationships between tables

2. **Test the Sync Process**
   - Test syncing each entry type
   - Test bidirectional sync
   - Test conflict resolution

3. **Monitor Performance**
   - Ensure sync operations are efficient
   - Optimize queries if needed

## Conclusion

This optimization plan addresses the identified issues with the current data structure and synchronization mechanism. By implementing these changes, we will ensure a consistent and efficient data flow between the SwiftData models and the Supabase database, making the sync process more reliable and maintainable.

The plan is divided into phases to allow for incremental implementation and testing. Each phase builds on the previous one, ensuring that the system remains functional throughout the optimization process.
