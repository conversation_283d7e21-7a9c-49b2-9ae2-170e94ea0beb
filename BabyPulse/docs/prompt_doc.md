We have an interactive prototype located at /Users/<USER>/Documents/BabyPulse/BabyPulse/BabyPulse/UIs for the "BabyPulse" iOS application, built using HTML, CSS, and JavaScript. This prototype captures the core user experience and visual design intent.

**Objective:**
Generate a series of comprehensive **BabyPulse iOS Development Documents** which will be saved at /Users/<USER>/Documents/BabyPulse/BabyPulse/BabyPulse/docs. These documents will serve as the authoritative blueprint for the iOS engineering team, translating the prototype into a high-quality, production-ready application. The primary goal is to ensure fidelity to the prototype's design and functionality while establishing a robust, maintainable, and scalable foundation using modern iOS development practices.

**Mandatory Technical Stack:**
The specification document must be based on and incorporate the following pre-defined technology stack:

* **Environment:** Xcode 16+, Swift 5.9+, iOS 17.0+ Target (iPhone & iPad), Git
* **Architecture:** MVVM, SwiftUI Native State Management & Navigation (`NavigationStack`/`SplitView`), Environment DI
* **Local Data:** SwiftData
* **Cloud Sync/Backend (Premium Tier):** Supabase (Auth, PostgreSQL, Storage)
* **LLM:** OpenRouter API (Client-side calls, Gemini 2.5 Pro/DeepSeek)
* **Monetization:** Google AdMob, RevenueCat
* **Core Libs:** Swift Charts, URLSession, SF Symbols, SPM

**Required Specification Document Sections:**
The generated document must be well-structured and include, at minimum, the following sections:

1.  **Product Requirements Document (PRD) Summary:** Condensed overview of vision, goals, target users, and core features based on the initial product description.
2.  **Technology Stack & Architecture:** Detailed breakdown based on the mandatory stack above, explaining architectural choices (MVVM, Navigation, DI).
3.  **UI Inventory & Functional Mapping:** A systematic breakdown of prototype screens to SwiftUI Views.
4.  **Swift Data Model Design:** Detailed schema definition for SwiftData and Supabase PostgreSQL alignment.
5. **Phased Development Plan:** A granular, interface-driven roadmap for implementation.
6.  **API Design & Integration:** Specifications for interacting with OpenRouter (LLM) and Supabase services, including request/response formats, authentication, and error handling.
7.  **Front-end Implementation Guidance:** Principles for building the SwiftUI views and managing state.
8.  **Back-end Implementation Guidance:** Notes on Supabase setup, PostgreSQL schema, Row Level Security (RLS) policies, and potential Edge Function needs (e.g., LLM proxy).
9.  **Swift/SwiftUI Design System & Style Guide:** Actionable guidelines derived *directly* from the prototype.


**Detailed Requirements for Key Specification Sections:**

* **1. Swift/SwiftUI Design System & Style Guide:**
    * Translate CSS variables/styles into a concrete SwiftUI `Color` palette (in `Assets.xcassets`).
    * Define typographic styles mapping prototype usage to SwiftUI `Font` modifiers, ensuring Dynamic Type compatibility.
    * Create specifications for reusable SwiftUI components (e.g., `CardView`, primary/secondary `Button` styles, styled `TextField`/`Picker`) derived from prototype elements.
    * Document intended animations/transitions and propose SwiftUI implementation strategies (`.animation`, `.transition`).
* **2. UI Inventory & Functional Mapping:**
    * Provide a clear mapping from each prototype HTML file/section to its corresponding SwiftUI `View` struct.
    * Detail the specific functionality, user interactions, and data display requirements for *each* SwiftUI View.
    * Define the application's navigation flow using `TabView`, `NavigationStack`, and modal presentations (`.sheet`/`.fullScreenCover`).
* **3. Swift Data Model Design:**
    * Define robust Swift data models (`@Model` for SwiftData) based on analysis of prototype forms and data requirements. Ensure models support relationships and are suitable for both local persistence and potential Supabase synchronization (`Codable`).
    * Specify the SwiftData schema and relationships. Outline the Supabase PostgreSQL table structure and necessary Row Level Security (RLS) policies.
    * Plan the state management strategy within SwiftUI (when to use `@State`, `@Binding`, `@StateObject`, `@Query`, `@EnvironmentObject`, `@AppStorage`, etc.) for data flow and UI updates.
* **4. Phased Development Plan:**
    * **CRITICAL:** This plan *must* be structured strictly by interface/screen, enabling iterative development and validation.
    * Each phase must correspond to the end-to-end development of one or more related views/features.
    * For *each phase*, clearly define:
        * Target SwiftUI Views to be built/updated.
        * Required State Management & Data Flow implementation.
        * Key Interactions & Gestures to be implemented.
        * Clear Acceptance Criteria for phase completion.
* **5. Platform Best Practices & Considerations:**
    * Address requirements for creating adaptive layouts suitable for various iPhone and iPad screen sizes.
    * Ensure support for Dark Mode and Dynamic Type is planned for.
    * Outline necessary Accessibility implementations (`.accessibilityLabel`, etc.).
    * Include recommendations for performance optimization (e.g., lazy loading, background tasks).
    * Highlight potential App Store review sensitivities (LLM usage disclosure, medical disclaimers, data privacy, IAP clarity).

**Overall Expectations for the Specification Document:**
The final document must be technically sound, unambiguous, and directly usable by the iOS development team. It should ensure the application built is not only functionally and visually aligned with the prototype but also engineered for quality, maintainability, and scalability, fully leveraging the capabilities of the specified Swift/SwiftUI technology stack.

Please generate this specification document adhering to all the requirements outlined above and save them at /Users/<USER>/Documents/BabyPulse/BabyPulse/BabyPulse/docs