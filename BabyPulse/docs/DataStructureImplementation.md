# BabyPulse Data Structure Implementation

This document describes the implementation of the data structure optimization plan for the BabyPulse app.

## Database Schema Optimization

### Created Missing Tables

1. **diaper_entries**: For tracking diaper changes
```sql
CREATE TABLE diaper_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    diaper_type TEXT NOT NULL,
    poop_color TEXT,
    poop_consistency TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_diaper_entries_baby_id ON diaper_entries(baby_id);
CREATE INDEX idx_diaper_entries_timestamp ON diaper_entries(timestamp);

-- Add RLS policy
ALTER TABLE diaper_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own diaper entries"
    ON diaper_entries
    USING (user_id = auth.uid());
```

2. **sleep_entries**: For tracking sleep patterns
```sql
CREATE TABLE sleep_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    start_ts TIMESTAMP WITH TIME ZONE NOT NULL,
    end_ts TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    location TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_sleep_entries_baby_id ON sleep_entries(baby_id);
CREATE INDEX idx_sleep_entries_start_ts ON sleep_entries(start_ts);

-- Add RLS policy
ALTER TABLE sleep_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own sleep entries"
    ON sleep_entries
    USING (user_id = auth.uid());
```

### Removed Redundant Tables

1. **chat_messages**: Dropped in favor of the singular `chat_message` table
```sql
DROP TABLE IF EXISTS chat_messages;
```

### Removed Unused Tables

1. **session**, **measurement**, **event**, **media**: These tables were not used by the SwiftData models
```sql
-- First, drop dependent views
DROP MATERIALIZED VIEW IF EXISTS daily_metrics;
DROP VIEW IF EXISTS daily_metrics_v2;
DROP VIEW IF EXISTS session_with_duration;

-- Then drop the tables
DROP TABLE IF EXISTS measurement;
DROP TABLE IF EXISTS session;
DROP TABLE IF EXISTS event;
DROP TABLE IF EXISTS media;
```

### Added Indexes to Existing Tables

```sql
-- Add indexes to feeding_entries
CREATE INDEX IF NOT EXISTS idx_feeding_entries_baby_id ON feeding_entries(baby_id);
CREATE INDEX IF NOT EXISTS idx_feeding_entries_timestamp ON feeding_entries(timestamp);

-- Add indexes to health_entry
CREATE INDEX IF NOT EXISTS idx_health_entry_baby_id ON health_entry(baby_id);
CREATE INDEX IF NOT EXISTS idx_health_entry_ts ON health_entry(ts);

-- Add indexes to growth_entry
CREATE INDEX IF NOT EXISTS idx_growth_entry_baby_id ON growth_entry(baby_id);
CREATE INDEX IF NOT EXISTS idx_growth_entry_ts ON growth_entry(ts);

-- Add indexes to chat_thread
CREATE INDEX IF NOT EXISTS idx_chat_thread_baby_id ON chat_thread(baby_id);

-- Add indexes to chat_message
CREATE INDEX IF NOT EXISTS idx_chat_message_thread_id ON chat_message(thread_id);
```

### Ensured RLS Policies

```sql
-- Enable RLS on all tables
ALTER TABLE feeding_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_entry ENABLE ROW LEVEL SECURITY;
ALTER TABLE growth_entry ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_thread ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_message ENABLE ROW LEVEL SECURITY;

-- Create policies for all tables
CREATE POLICY "Users can only access their own feeding entries"
    ON feeding_entries
    USING (user_id = auth.uid());

CREATE POLICY "Users can only access their own health entries"
    ON health_entry
    USING (user_id = auth.uid());

CREATE POLICY "Users can only access their own growth entries"
    ON growth_entry
    USING (user_id = auth.uid());

CREATE POLICY "Users can only access their own chat threads"
    ON chat_thread
    USING (user_id = auth.uid());

CREATE POLICY "Users can only access their own chat messages"
    ON chat_message
    USING (user_id = auth.uid());
```

## Sync Logic Implementation

### Updated SupabaseService.syncData Method

```swift
/// Sync local data to Supabase
func syncData<T: Encodable>(type: String, babyId: String, data: T) async throws {
    // Convert Encodable to dictionary
    let jsonData = try JSONEncoder().encode(data)
    guard let dictionary = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] else {
        throw NSError(domain: "SupabaseService", code: 3, userInfo: [NSLocalizedDescriptionKey: "Failed to convert data to dictionary"])
    }
    
    // Use the dictionary version
    try await syncData(type: type, babyId: babyId, data: dictionary)
}

/// Sync dictionary data to Supabase
func syncData(type: String, babyId: String, data: [String: Any]) async throws {
    // Determine the table name based on the type
    let tableName: String
    switch type {
    case "baby":
        tableName = "babies"
    case "feeding":
        tableName = "feeding_entries"
    case "diaper":
        tableName = "diaper_entries"
    case "sleep":
        tableName = "sleep_entries"
    case "health":
        tableName = "health_entry"
    case "growth":
        tableName = "growth_entry"
    case "chat":
        tableName = "chat_message"
    case "thread":
        tableName = "chat_thread"
    default:
        throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
    }
    
    do {
        // Ensure user_id is included in the data
        var dataWithUserId = data
        if let userId = currentUser?.id {
            dataWithUserId["user_id"] = userId
        }
        
        // Upsert the data (insert or update)
        let response = try await client.database
            .from(tableName)
            .upsert(dataWithUserId)
            .execute()
        
        print("Successfully synced \(type) data for baby \(babyId)")
    } catch {
        print("Error syncing \(type) data for baby \(babyId): \(error)")
        throw error
    }
}
```

### Added Bidirectional Sync

```swift
/// Fetch data from Supabase
func fetchData<T: Decodable>(type: String, babyId: String) async throws -> [T] {
    // Determine the table name based on the type
    let tableName: String
    switch type {
    case "baby":
        tableName = "babies"
    case "feeding":
        tableName = "feeding_entries"
    case "diaper":
        tableName = "diaper_entries"
    case "sleep":
        tableName = "sleep_entries"
    case "health":
        tableName = "health_entry"
    case "growth":
        tableName = "growth_entry"
    case "chat":
        tableName = "chat_message"
    case "thread":
        tableName = "chat_thread"
    default:
        throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
    }
    
    do {
        // Fetch data for the specified baby
        let response = try await client.database
            .from(tableName)
            .select()
            .eq("baby_id", value: babyId)
            .execute()
        
        // Decode the response
        return try JSONDecoder().decode([T].self, from: response.data)
    } catch {
        print("Error fetching \(type) data for baby \(babyId): \(error)")
        throw error
    }
}
```

### Updated SyncManager

The SyncManager was updated to:

1. Include proper baby_id, created_at, and updated_at fields in all sync operations
2. Update the sync status of entries during the sync process
3. Handle sync failures and retries

```swift
/// Sync pending operations
func syncPendingOperations() {
    guard !isSyncing, isOnline, !syncQueue.isEmpty else { return }

    isSyncing = true

    Task {
        // Create a local copy of the queue to avoid concurrent access issues
        let operationsToSync = syncQueue
        var successfulIndices: [Int] = []

        for (index, operation) in operationsToSync.enumerated() {
            do {
                // Use the syncData method from SupabaseService
                try await supabaseService.syncData(
                    type: operation.type,
                    babyId: operation.babyId,
                    data: operation.data
                )

                // Mark as successful
                successfulIndices.append(index)
                
                // Update the sync status of the corresponding entry
                if let entryId = operation.data["id"] as? String, let id = UUID(uuidString: entryId) {
                    await updateSyncStatus(id: id, type: operation.type, status: .synced)
                }
            } catch {
                print("Error syncing operation: \(error)")
                
                // Update the sync status to failed
                if let entryId = operation.data["id"] as? String, let id = UUID(uuidString: entryId) {
                    await updateSyncStatus(id: id, type: operation.type, status: .syncFailed)
                }
                
                // Continue with next operation
            }
        }

        // Remove successful operations from the queue
        await MainActor.run {
            // Create a new queue without the successful operations
            let newQueue = syncQueue.enumerated().filter { index, _ in
                !successfulIndices.contains(index)
            }.map { _, operation in
                operation
            }

            syncQueue = newQueue
            saveSyncQueue()
            isSyncing = false
        }
    }
}
```

## Conclusion

The data structure optimization plan has been successfully implemented. The Supabase database schema now matches the SwiftData models, with proper tables, indexes, and RLS policies. The sync mechanism has been improved to handle all entry types consistently and to track the sync status of each entry.

Future improvements could include:
1. Implementing the updateSyncStatus method to actually update the sync status in the SwiftData models
2. Adding conflict resolution for concurrent updates
3. Implementing a full bidirectional sync for premium users
4. Adding a sync status UI to show the user the sync status of their data
