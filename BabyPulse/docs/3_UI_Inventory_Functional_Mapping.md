# BabyPulse - UI Inventory & Functional Mapping

This document provides a comprehensive mapping from the HTML prototype to the corresponding SwiftUI implementation, detailing the structure, functionality, and data requirements for each view.

## Application Structure

The BabyPulse app is organized into the following main sections:

1. **Onboarding Flow**
2. **Main Tab Interface**
   - Home/Insights Tab
   - Logs Tab
   - Chat Tab
   - Settings Tab
3. **Modal Workflows**
   - Log Entry Flow
   - Profile Creation/Editing

## 1. Onboarding Flow

### OnboardingView
**Prototype Source:** `onboarding.html`

**SwiftUI Structure:**
```swift
struct OnboardingView: View {
    @StateObject private var viewModel: OnboardingViewModel
    @State private var currentStep: OnboardingStep = .welcome
    
    var body: some View {
        VStack {
            // Progress indicator
            ProgressDotsView(currentStep: currentStep)
            
            // Content area (changes based on currentStep)
            switch currentStep {
                case .welcome:
                    WelcomeStepView(onContinue: { currentStep = .babyProfile })
                case .babyProfile:
                    BabyProfileStepView(viewModel: viewModel, onContinue: { currentStep = .preferences })
                case .preferences:
                    PreferencesStepView(viewModel: viewModel, onContinue: { viewModel.completeOnboarding() })
            }
            
            // Navigation buttons
            OnboardingNavigationView(
                currentStep: $currentStep,
                onContinue: { /* Handle continue action */ },
                onSkip: { viewModel.skipOnboarding() }
            )
        }
        .background(Color.backgroundPrimary)
    }
}
```

**Functionality:**
- Multi-step onboarding process with progress tracking
- Baby profile creation (name, DOB, sex, optional birth measurements)
- Preference settings (units, notifications)
- Skip option for quick access to the app

**Data Requirements:**
- Baby profile information
- User preferences
- Onboarding completion status

## 2. Main Tab Interface

### MainTabView
**Prototype Source:** `index.html` (tab structure)

**SwiftUI Structure:**
```swift
struct MainTabView: View {
    @State private var selectedTab: Tab = .home
    @StateObject private var homeViewModel = HomeViewModel()
    @StateObject private var logsViewModel = LogsViewModel()
    @StateObject private var chatViewModel = ChatViewModel()
    @StateObject private var settingsViewModel = SettingsViewModel()
    
    var body: some View {
        TabView(selection: $selectedTab) {
            NavigationStack {
                HomeView(viewModel: homeViewModel)
            }
            .tabItem {
                Label("Home", systemImage: "house.fill")
            }
            .tag(Tab.home)
            
            NavigationStack {
                LogsView(viewModel: logsViewModel)
            }
            .tabItem {
                Label("Logs", systemImage: "list.bullet")
            }
            .tag(Tab.logs)
            
            NavigationStack {
                ChatView(viewModel: chatViewModel)
            }
            .tabItem {
                Label("Chat", systemImage: "message.fill")
            }
            .tag(Tab.chat)
            
            NavigationStack {
                SettingsView(viewModel: settingsViewModel)
            }
            .tabItem {
                Label("Settings", systemImage: "gear")
            }
            .tag(Tab.settings)
        }
        .sheet(isPresented: $showLogEntrySheet) {
            LogEntryView()
        }
    }
}
```

**Functionality:**
- Tab-based navigation between main app sections
- Independent navigation stack for each tab
- Shared state management across tabs when needed
- Floating action button for quick log entry

### HomeView
**Prototype Source:** `home.html`

**SwiftUI Structure:**
```swift
struct HomeView: View {
    @ObservedObject var viewModel: HomeViewModel
    @State private var showLogEntrySheet = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // App Bar with baby profile
                BabyProfileHeaderView(
                    babyName: viewModel.currentBaby.name,
                    babyAge: viewModel.currentBabyAge,
                    profileImage: viewModel.currentBaby.profileImage,
                    onProfileTap: { /* Handle profile tap */ }
                )
                
                // Today's Summary Card
                TodaySummaryCardView(summaryData: viewModel.todaySummary)
                
                // Risk Lighthouse Banner
                RiskBannerView(
                    status: viewModel.riskStatus,
                    message: viewModel.riskMessage
                )
                
                // Insights Feed
                Text("Insights")
                    .font(.title)
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                ForEach(viewModel.insights) { insight in
                    InsightCardView(insight: insight)
                }
            }
            .padding()
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { /* Open settings */ }) {
                    Image(systemName: "gear")
                }
            }
        }
        .overlay(alignment: .bottomTrailing) {
            FloatingActionButton(action: { showLogEntrySheet = true })
        }
        .sheet(isPresented: $showLogEntrySheet) {
            LogEntryView()
        }
    }
}
```

**Functionality:**
- Display current baby profile information
- Show today's summary metrics (feeding, sleep, diapers)
- Present risk assessment banner
- Display personalized insights cards
- Quick access to log entry via FAB

**Data Requirements:**
- Current baby profile
- Today's activity summary
- Risk assessment status
- Personalized insights list

### LogsView
**Prototype Source:** `logs.html`

**SwiftUI Structure:**
```swift
struct LogsView: View {
    @ObservedObject var viewModel: LogsViewModel
    @State private var showLogEntrySheet = false
    @State private var showFilterSheet = false
    @State private var selectedDate: Date = Date()
    
    var body: some View {
        VStack(spacing: 0) {
            // Date navigation
            DateNavigationView(
                selectedDate: $selectedDate,
                onPreviousDay: { viewModel.loadPreviousDay() },
                onNextDay: { viewModel.loadNextDay() }
            )
            
            // Timeline
            ScrollView {
                LazyVStack(spacing: 16, pinnedViews: [.sectionHeaders]) {
                    ForEach(viewModel.logEntries) { entry in
                        TimelineEntryView(entry: entry)
                    }
                }
                .padding()
            }
        }
        .navigationTitle("Activity Log")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showFilterSheet = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { /* Show calendar */ }) {
                    Image(systemName: "calendar")
                }
            }
        }
        .overlay(alignment: .bottomTrailing) {
            FloatingActionButton(action: { showLogEntrySheet = true })
        }
        .sheet(isPresented: $showLogEntrySheet) {
            LogEntryView()
        }
        .sheet(isPresented: $showFilterSheet) {
            FilterView(filters: $viewModel.activeFilters)
        }
    }
}
```

**Functionality:**
- Timeline view of baby activities
- Date navigation (previous/next day)
- Calendar date picker
- Filtering options
- Detailed log entries with type-specific information
- Quick access to log entry via FAB

**Data Requirements:**
- Log entries for selected date
- Filter settings
- Date navigation state

### ChatView
**Prototype Source:** `chat.html`

**SwiftUI Structure:**
```swift
struct ChatView: View {
    @ObservedObject var viewModel: ChatViewModel
    @State private var messageText: String = ""
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Message list
            ScrollViewReader { scrollView in
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(viewModel.messages) { message in
                            MessageBubbleView(message: message)
                                .id(message.id)
                        }
                    }
                    .padding()
                }
                .onChange(of: viewModel.messages) { _, _ in
                    if let lastMessage = viewModel.messages.last {
                        scrollView.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
            
            // Suggested questions
            if !viewModel.suggestedQuestions.isEmpty {
                SuggestedQuestionsView(
                    questions: viewModel.suggestedQuestions,
                    onQuestionTap: { question in
                        messageText = question
                        viewModel.sendMessage(question)
                    }
                )
            }
            
            // Input area
            HStack {
                TextField("Ask a question...", text: $messageText)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(20)
                    .focused($isInputFocused)
                
                Button(action: {
                    if !messageText.isEmpty {
                        viewModel.sendMessage(messageText)
                        messageText = ""
                    }
                }) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.primary)
                }
            }
            .padding()
        }
        .navigationTitle("Baby Assistant")
    }
}
```

**Functionality:**
- Conversational interface with AI assistant
- Message history display
- Suggested questions for quick interaction
- Text input for custom questions
- Auto-scrolling to latest messages

**Data Requirements:**
- Message history
- Suggested questions
- LLM integration for responses

### SettingsView
**Prototype Source:** `settings.html`

**SwiftUI Structure:**
```swift
struct SettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    var body: some View {
        List {
            // Profile Section
            Section("Profile") {
                ForEach(viewModel.babyProfiles) { profile in
                    BabyProfileRow(profile: profile)
                }
                
                Button(action: { viewModel.showAddBabyProfile() }) {
                    Label("Add Another Baby", systemImage: "plus")
                }
            }
            
            // Preferences Section
            Section("Preferences") {
                NavigationLink(destination: NotificationsSettingsView()) {
                    SettingsRowView(
                        icon: "bell.fill",
                        iconColor: .purple,
                        title: "Notifications"
                    )
                }
                
                NavigationLink(destination: UnitsSettingsView()) {
                    SettingsRowView(
                        icon: "ruler",
                        iconColor: .green,
                        title: "Units (Imperial/Metric)",
                        value: viewModel.currentUnitSystem.rawValue
                    )
                }
                
                Toggle(isOn: $viewModel.isDarkModeEnabled) {
                    SettingsRowView(
                        icon: "moon.fill",
                        iconColor: .yellow,
                        title: "Dark Mode"
                    )
                }
            }
            
            // Data & Privacy Section
            Section("Data & Privacy") {
                NavigationLink(destination: BackupSettingsView()) {
                    SettingsRowView(
                        icon: "cloud.upload.fill",
                        iconColor: .blue,
                        title: "Backup & Sync"
                    )
                }
                
                NavigationLink(destination: PrivacySettingsView()) {
                    SettingsRowView(
                        icon: "shield.fill",
                        iconColor: .red,
                        title: "Privacy Settings"
                    )
                }
                
                NavigationLink(destination: ExportDataView()) {
                    SettingsRowView(
                        icon: "square.and.arrow.up",
                        iconColor: .gray,
                        title: "Export Data"
                    )
                }
            }
            
            // About Section
            Section("About") {
                NavigationLink(destination: HelpSupportView()) {
                    SettingsRowView(
                        icon: "questionmark.circle.fill",
                        iconColor: .purple,
                        title: "Help & Support"
                    )
                }
                
                NavigationLink(destination: TermsPrivacyView()) {
                    SettingsRowView(
                        icon: "doc.text.fill",
                        iconColor: .blue,
                        title: "Terms & Privacy Policy"
                    )
                }
                
                SettingsRowView(
                    icon: "info.circle.fill",
                    iconColor: .gray,
                    title: "App Version",
                    value: viewModel.appVersion
                )
            }
            
            // Sign Out Button
            Section {
                Button(action: { viewModel.signOut() }) {
                    Text("Sign Out")
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
            }
        }
        .navigationTitle("Settings")
    }
}
```

**Functionality:**
- Baby profile management
- Notification settings
- Unit preferences
- Dark mode toggle
- Backup and sync options
- Privacy settings
- Data export
- Help and support access
- Terms and privacy policy
- App version information
- Sign out functionality

**Data Requirements:**
- Baby profiles list
- User preferences
- App version
- Authentication state

## 3. Modal Workflows

### LogEntryView
**Prototype Source:** `log_entry.html`

**SwiftUI Structure:**
```swift
struct LogEntryView: View {
    @StateObject private var viewModel = LogEntryViewModel()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            Group {
                if viewModel.currentStep == .categorySelection {
                    LogCategorySelectionView(
                        onCategorySelected: { category in
                            viewModel.selectedCategory = category
                            viewModel.currentStep = .entryForm
                        }
                    )
                } else {
                    LogEntryFormView(
                        category: viewModel.selectedCategory,
                        onBack: { viewModel.currentStep = .categorySelection },
                        onSave: { entryData in
                            viewModel.saveLogEntry(entryData)
                            dismiss()
                        }
                    )
                }
            }
            .navigationTitle(viewModel.currentStep == .categorySelection ? "Add New Entry" : viewModel.selectedCategory.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct LogCategorySelectionView: View {
    let onCategorySelected: (LogCategory) -> Void
    
    var body: some View {
        VStack {
            Text("What would you like to log?")
                .font(.title2)
                .fontWeight(.semibold)
                .padding(.bottom)
            
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                ForEach(LogCategory.allCases) { category in
                    CategoryButton(
                        category: category,
                        onTap: { onCategorySelected(category) }
                    )
                }
            }
        }
        .padding()
    }
}

struct LogEntryFormView: View {
    let category: LogCategory
    let onBack: () -> Void
    let onSave: (LogEntryData) -> Void
    
    @StateObject private var formState: LogEntryFormState
    
    init(category: LogCategory, onBack: @escaping () -> Void, onSave: @escaping (LogEntryData) -> Void) {
        self.category = category
        self.onBack = onBack
        self.onSave = onSave
        self._formState = StateObject(wrappedValue: LogEntryFormState(category: category))
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Dynamic form based on category
                switch category {
                case .feeding:
                    FeedingEntryForm(formState: formState)
                case .diaper:
                    DiaperEntryForm(formState: formState)
                case .sleep:
                    SleepEntryForm(formState: formState)
                case .growth:
                    GrowthEntryForm(formState: formState)
                case .health:
                    HealthEntryForm(formState: formState)
                case .milestone:
                    MilestoneEntryForm(formState: formState)
                case .dailyEvent:
                    DailyEventEntryForm(formState: formState)
                case .note:
                    NoteEntryForm(formState: formState)
                }
                
                // Save button
                Button(action: {
                    let entryData = formState.generateEntryData()
                    onSave(entryData)
                }) {
                    Text("Save")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.primary)
                        .cornerRadius(12)
                }
                .padding(.top)
            }
            .padding()
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: onBack) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("Back")
                    }
                }
            }
        }
    }
}
```

**Functionality:**
- Two-step process: category selection followed by entry form
- Dynamic forms based on selected category
- Form validation
- Data saving
- Navigation between steps
- Cancel and back options

**Data Requirements:**
- Log entry categories
- Form state for each category
- Validation rules
- Data repository for saving entries

## Component Mapping

### Reusable Components

#### CardView
**Prototype Source:** Card elements in various HTML files

**SwiftUI Implementation:**
```swift
struct CardView<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .padding()
            .background(Color.card)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 2)
    }
}
```

#### FloatingActionButton
**Prototype Source:** `.fab-button` in HTML files

**SwiftUI Implementation:**
```swift
struct FloatingActionButton: View {
    let action: () -> Void
    let icon: String
    
    init(action: @escaping () -> Void, icon: String = "plus") {
        self.action = action
        self.icon = icon
    }
    
    var body: some View {
        Button(action: {
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(Color.primary)
                .cornerRadius(28)
                .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
        }
        .padding()
    }
}
```

#### MetricItemView
**Prototype Source:** Metric items in `home.html`

**SwiftUI Implementation:**
```swift
struct MetricItemView: View {
    let icon: String
    let iconColor: Color
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            ZStack {
                Circle()
                    .fill(iconColor.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .foregroundColor(iconColor)
                    .font(.system(size: 18))
            }
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
        }
    }
}
```

#### InsightCardView
**Prototype Source:** Insight cards in `home.html`

**SwiftUI Implementation:**
```swift
struct InsightCardView: View {
    let insight: Insight
    
    var body: some View {
        CardView {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    ZStack {
                        Circle()
                            .fill(insight.category.color.opacity(0.2))
                            .frame(width: 32, height: 32)
                        
                        Image(systemName: insight.category.icon)
                            .foregroundColor(insight.category.color)
                    }
                    
                    Text(insight.title)
                        .font(.headline)
                        .foregroundColor(insight.category.color)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                
                Text(insight.metric)
                    .font(.title3)
                    .fontWeight(.bold)
                
                Text(insight.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
}
```

#### TimelineEntryView
**Prototype Source:** Timeline items in `logs.html`

**SwiftUI Implementation:**
```swift
struct TimelineEntryView: View {
    let entry: LogEntry
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Category icon
            ZStack {
                Circle()
                    .fill(entry.category.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: entry.category.icon)
                    .foregroundColor(entry.category.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // Time
                Text(entry.formattedTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                // Content card
                CardView {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(entry.title)
                            .font(.headline)
                        
                        Text(entry.subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        if let details = entry.details {
                            Text(details)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // Category-specific content
                        switch entry.category {
                        case .feeding:
                            if let feedingData = entry.feedingData {
                                FeedingDetailsView(data: feedingData)
                            }
                        case .diaper:
                            if let diaperData = entry.diaperData {
                                DiaperDetailsView(data: diaperData)
                            }
                        case .sleep:
                            if let sleepData = entry.sleepData {
                                SleepDetailsView(data: sleepData)
                            }
                        case .growth:
                            if let growthData = entry.growthData {
                                GrowthDetailsView(data: growthData)
                            }
                        default:
                            EmptyView()
                        }
                    }
                }
            }
        }
    }
}
```

#### MessageBubbleView
**Prototype Source:** Message bubbles in `chat.html`

**SwiftUI Implementation:**
```swift
struct MessageBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
            }
            
            VStack(alignment: message.isFromUser ? .trailing : .leading, spacing: 4) {
                Text(message.content)
                    .padding()
                    .background(message.isFromUser ? Color.primary : Color(.systemGray6))
                    .foregroundColor(message.isFromUser ? .white : .primary)
                    .cornerRadius(18, corners: message.isFromUser ? [.topLeft, .topRight, .bottomLeft] : [.topLeft, .topRight, .bottomRight])
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: message.isFromUser ? .trailing : .leading)
            
            if !message.isFromUser {
                Spacer()
            }
        }
    }
}
```

## Navigation Flow

### Main Navigation
- TabView for primary navigation between Home, Logs, Chat, and Settings
- Each tab contains its own NavigationStack for hierarchical navigation
- Modal presentations for workflows like log entry and onboarding

### Deep Linking
- Support for deep linking to specific sections via URL schemes
- State restoration for app resumption

### Gesture Navigation
- Swipe gestures for date navigation in logs view
- Pull-to-refresh for data updates
- Long press for additional options on log entries

## Accessibility Considerations
- VoiceOver support for all UI elements
- Dynamic Type compatibility for text scaling
- Sufficient color contrast for readability
- Haptic feedback for important interactions
- Keyboard navigation support
