# BabyPulse Improvement Execution Plan (v0.2)

_Author: Cascade AI – 2025-04-29_

This document turns the high-level recommendations into an actionable, phase-based roadmap.  Three parallel work-streams are defined.  Each work-stream is broken into **Phases → Tasks → Deliverables → Acceptance Tests** so any engineer can immediately pick tickets from the top of the backlog.

---

## Work-stream A – Insight Card Generation 2.0

| Phase | Duration | Goals |
|-------|----------|-------|
| A-1  Design & Data Model | 0.5 day | Introduce `InsightGenerationManager`, persistence schema, and thresholds config. |
| A-2  Infrastructure | 1 day | Refactor observer → manager, implement batching + hash cache. |
| A-3  UI Upgrade | 1 day | Severity colour, expandable card, "Generating…" banner. |
| A-4  QA & Metrics | 0.5 day | Unit + integration tests, add OSLog metrics. |

### Detailed Tasks

#### Phase A-1 – Design & Data Model
1. Create `BabyPulse/Services/InsightGenerationManager.swift` with:
   * `@Observable` properties: `lastRunAt: Date`, `eventsSinceLastRun: [InsightCategory: Int]`.
   * `shouldRun(for category:) -> Bool` implementing rule: `events ≥ threshold || (appLaunch && Δt ≥ 6h)`.
2. Add `InsightGenerationSettings.plist` in `Resources` holding per-category `eventThreshold` & `cooldownHours`.
3. Extend `Insight` model: `severity: InsightSeverity`, `actionItems: [String]`, `hashKey: String` (indexed).
   * Write lightweight migration script in `ModelVersions/…`.

#### Phase A-2 – Infrastructure
1. Refactor `LogEntryObserver` → forward events to manager.
2. Compute `hashKey = SHA256(templateName + variableValues)`; skip LLM call if identical hash exists <24h old.
3. Build *single JSON prompt* creator util; update `EnhancedInsightService` to call LLM once per baby, parse array.
4. Add background generation with `async let` and `NotificationCenter` publish `insightsGenerated`.

#### Phase A-3 – UI Upgrade
1. Update `Insight` Card View:
   * Add coloured `severityDot` (info = gray, warning = orange, urgent = red).
   * Show one `actionItem` inline; tap expands sheet with full explanation & small Swift Charts trend.
2. Add non-blocking banner `InsightGenerationView(state: .generating)`.

#### Phase A-4 – QA & Metrics
* Unit-test threshold logic with XCTest.
* Add `OSLog.insightGeneration.duration` metric around LLM call.
* End-to-end UI test: create 5 dummy diaper entries ⇒ expect one insight.

---

## Work-stream B – Chat with LLM 2.0

| Phase | Duration | Goals |
|-------|----------|-------|
| B-1  Context Summariser | 0.5 day | Reduce token cost by summarising logs. |
| B-2  Streaming & UI | 1 day | Implement SSE stream & typing animation. |
| B-3  Suggested Q revamp | 0.5 day | Tie suggestions to latest insights. |
| B-4  Trust & Safety | 0.5 day | Add disclaimer, message actions. |

### Detailed Tasks

#### Phase B-1 – Context Summariser
1. Add `BabyContextSummariser.swift` that accepts baby id, returns ≤1k-token summary (stats of last 24h, notable events).
2. Modify `ChatViewModel.getBabyDataContext()` to call summariser first.

#### Phase B-2 – Streaming & UI
1. Update `LLMService.generateResponse` to expose `AsyncThrowingStream<String>` powered by URLSession`s `bytes` sequence.
2. In `ChatViewModel`, append partial tokens to a `.streaming` `ChatMessage` in real-time.
3. Animate message bubble height and auto-scroll without jitter.

#### Phase B-3 – Suggested Questions Revamp
1. Fetch the 3 most recent `Insight` titles; feed to prompt: _“Suggest 5 follow-up questions parents might ask.”_
2. Replace hard-coded age triage list.

#### Phase B-4 – Trust & Safety
1. Prepend all assistant messages with subtle disclaimer using `AttributedString` (system font caption2, gray).
2. Long-press on `MessageBubbleView` → context menu: *Copy*, *Report*, *Helpful* (store bool in `ChatMessage.feedback`).

---

## Work-stream C – Product Completeness & Onboarding

| Phase | Duration | Goals |
|-------|----------|-------|
| C-1  Onboarding / Demo | 1 day | First-launch flow, demo data option. |
| C-2  Charts MVP | 1 day | Feed volume chart (Swift Charts). |
| C-3  Settings & API Key | 0.5 day | Paste API key, premium placeholder. |
| C-4  Accessibility & Polish | 0.5 day | Dynamic Type, VoiceOver labels, haptics. |

### Detailed Tasks

#### Phase C-1 – Onboarding
1. Add `OnboardingView` (Pager): Welcome, add baby, _“What to expect”_, toggle *Demo Mode*.
2. If demo mode → preload sample logs & insights using `TestingUtilities`.

#### Phase C-2 – Charts MVP
1. `ChartsViewModel` computes daily total feed ml over last 7 days.
2. `FeedChartView` uses `BarMark` (Swift Charts); accessible description.
3. Link card tap → navigate to chart with same category filter.

#### Phase C-3 – Settings
1. `SettingsView` with sections: *API Key*, *Appearance*, *Premium* (stub purchase flow).
2. Securely store key in Keychain; propagate to `LLMService`.

#### Phase C-4 – Accessibility & Polish
1. Audit with Xcode Accessibility Inspector; label icons.
2. Support Dynamic Type by using `Font.preferredFont(forTextStyle:)` wrappers in `BabyPulseTypography`.
3. Add subtle haptic on insight generation complete (`UIC haptic feedback`).

---

## Milestone Timeline (2 weeks, 5-dev team)

| Day | Task |
|-----|------|
| 1 | A-1, B-1 spec & code |
| 2 | A-2 infra start |
| 3 | A-2 continue, B-2 stream start |
| 4 | A-3 UI, B-2 finish |
| 5 | A QA, B-3 suggestions |
| 6 | C-1 onboarding |
| 7 | C-2 charts |
| 8 | C-3 settings |
| 9 | C-4 accessibility, B-4 safety |
| 10| Buffer, regression test, App Store TestFlight build |

---

### Acceptance Criteria Snapshot
* **Insight generation** runs ≤1 LLM call per batch and never triggers more than 4 calls/day/baby.
* **Insight UI** displays severity dot, action item, expand sheet; no spinner over 2 s.
* **Chat** context <2 k tokens, streaming latency ≤2 s for first token.
* **Onboarding** path to first insight ≤2 minutes for new user.

---

_End of document_
