# BabyPulse - Phased Development Plan

This document outlines a structured, interface-driven roadmap for implementing the BabyPulse iOS application. Each phase focuses on specific screens and features, enabling iterative development and validation.

## Development Principles

1. **Interface-First Approach:** Development is organized by screens/interfaces rather than by technical components
2. **Vertical Slices:** Each phase delivers complete, end-to-end functionality
3. **Incremental Complexity:** Phases build upon each other, starting with core functionality
4. **Continuous Testing:** Each phase includes testing criteria for validation
5. **User-Centric Milestones:** Phases are designed to deliver tangible user value at each step

## Phase Overview

1. **Foundation & Onboarding**
2. **Core Logging Functionality**
3. **Timeline & History View**
4. **Insights & Analytics**
5. **Chat Interface**
6. **Settings & Profile Management**
7. **Cloud Sync & Premium Features**
8. **Refinement & App Store Preparation**

## Detailed Phase Plan

### Phase 1: Foundation & Onboarding
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `OnboardingView` with step-based flow
- `WelcomeStepView`
- `BabyProfileStepView`
- `PreferencesStepView`
- `MainTabView` (basic structure)

#### State Management & Data Flow:
- Create core SwiftData models (`Baby`, `UserPreferences`)
- Implement `OnboardingViewModel` with state management
- Set up app initialization logic to determine if onboarding is needed

#### Key Interactions & Gestures:
- Onboarding step navigation (next/back)
- Baby profile creation form
- Preferences selection
- Onboarding completion

#### Acceptance Criteria:
- New users see onboarding flow on first launch
- Users can create a baby profile with required information
- Preferences are saved and applied to the app
- After onboarding, users are directed to the main interface
- Onboarding can be skipped for testing purposes

---

### Phase 2: Core Logging Functionality
**Target Duration:** 3 weeks

#### SwiftUI Views to Implement:
- `LogEntryView` with category selection
- `LogCategorySelectionView`
- `FeedingEntryForm`
- `DiaperEntryForm`
- `SleepEntryForm`
- `FloatingActionButton` component

#### State Management & Data Flow:
- Create log entry SwiftData models (`FeedingEntry`, `DiaperEntry`, `SleepEntry`)
- Implement `LogEntryViewModel` with form state management
- Set up repository pattern for data access

#### Key Interactions & Gestures:
- FAB button with haptic feedback
- Category selection grid
- Dynamic form fields based on category
- Form validation and submission
- Date/time selection

#### Acceptance Criteria:
- Users can access log entry from FAB button
- All core logging categories are available (feeding, diaper, sleep)
- Forms capture all required data for each category
- Entries are saved to SwiftData
- Form validation prevents invalid submissions
- Users return to previous screen after successful entry

---

### Phase 3: Home Screen & Daily Summary
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `HomeView` with complete layout
- `BabyProfileHeaderView`
- `TodaySummaryCardView`
- `RiskBannerView`
- `InsightCardView` (basic version)

#### State Management & Data Flow:
- Implement `HomeViewModel` with data aggregation
- Create data summary calculation logic
- Set up basic risk assessment algorithm/LLM powered insights

#### Key Interactions & Gestures:
- Pull-to-refresh for latest data
- Tappable summary metrics for detail view
- Profile header interaction

#### Acceptance Criteria:
- Home screen displays current baby profile
- Today's summary shows accurate metrics from logged data
- Risk banner displays appropriate status based on data
- Basic insights are displayed LLM powered
- UI matches design specifications

---

### Phase 4: Timeline & History View
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `LogsView` with timeline
- `TimelineEntryView`
- `DateNavigationView`
- `FilterView`
- Category-specific detail views (`FeedingDetailsView`, `DiaperDetailsView`, etc.)

#### State Management & Data Flow:
- Implement `LogsViewModel` with filtering and date navigation
- Create timeline data aggregation logic
- Set up SwiftData queries with predicates for filtering

#### Key Interactions & Gestures:
- Date navigation (previous/next day)
- Calendar date picker
- Filter selection
- Timeline entry taps for details
- Swipe actions on entries (edit/delete)

#### Acceptance Criteria:
- Timeline displays all logged entries chronologically
- Users can navigate between days
- Filtering works for all categories
- Entry details are accessible via tap
- Edit and delete functionality works
- UI is responsive and matches design specifications

---

### Phase 5: Growth & Health Tracking
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `GrowthEntryForm`
- `HealthEntryForm`
- `GrowthDetailsView`
- `HealthDetailsView`

#### State Management & Data Flow:
- Create additional SwiftData models (`GrowthEntry`, `HealthEntry`)
- Implement percentile calculation logic

#### Key Interactions & Gestures:
- Growth data entry with validation
- Health record entry (temperature, medication, symptoms)

#### Acceptance Criteria:
- Users can log growth measurements
- Health records can be created and viewed
- Data validation ensures accurate entries

---

### Phase 6: Chat Interface
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `ChatView` with message list
- `MessageBubbleView`
- `SuggestedQuestionsView`
- `ChatInputView`

#### State Management & Data Flow:
- Implement `ChatViewModel` with message handling
- Create LLM service for generating responses
- Set up message persistence in SwiftData
- Implement suggested questions generation

#### Key Interactions & Gestures:
- Text input with send button
- Message list scrolling
- Suggested question taps
- Loading states during LLM processing
- open router api (reference)
  ```
  fetch("https://openrouter.ai/api/v1/chat/completions", {
  method: "POST",
  headers: {
    "Authorization": "Bearer <OPENROUTER_API_KEY>",
    "HTTP-Referer": "<YOUR_SITE_URL>", // Optional. Site URL for rankings on openrouter.ai.
    "X-Title": "<YOUR_SITE_NAME>", // Optional. Site title for rankings on openrouter.ai.
    "Content-Type": "application/json"
  },
  body: JSON.stringify({
    "model": "google/gemini-2.5-pro-exp-03-25:free",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What is in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
            }
          }
        ]
      }
    ]
  })
});
  ```
  ```
  curl https://openrouter.ai/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENROUTER_API_KEY" \
  -d '{
  "model": "google/gemini-2.5-pro-exp-03-25:free",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "What is in this image?"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
          }
        }
      ]
    }
  ]
  
}'
  ```

#### Acceptance Criteria:
- Users can send questions about their baby's data
- LLM responses are contextual and based on logged data
- Chat history is preserved between sessions
- Suggested questions are relevant to the user's data
- UI handles loading states gracefully
- Error handling for API failures

---

### Phase 7: Insights & Analytics
**Target Duration:** 3 weeks

#### SwiftUI Views to Implement:
- Enhanced `InsightCardView` with dynamic data
- `InsightDetailView`
- `AnalyticsView` with charts and trends
- `PatternAnalysisView`

#### State Management & Data Flow:
- Implement analytics calculation engine
- Create pattern detection algorithms
- Set up LLM integration for insights generation
- Implement caching for expensive calculations

#### Key Interactions & Gestures:
- Insight card taps for detailed view
- Chart interactions (zoom, selection)
- Time period selection for analytics
- Share functionality for insights

#### Acceptance Criteria:
- Insights are generated based on actual user data
- Analytics display meaningful patterns and trends
- LLM-powered insights provide valuable information
- UI is responsive and performs well with large datasets
- Insights update when new data is logged

---


### Phase 8: Settings & Profile Management
**Target Duration:** 2 weeks

#### SwiftUI Views to Implement:
- `SettingsView` with sections
- `BabyProfileRow`
- `SettingsRowView`
- `NotificationsSettingsView`
- `UnitsSettingsView`
- `PrivacySettingsView`
- `BabyProfileEditView`

#### State Management & Data Flow:
- Implement `SettingsViewModel`
- Create settings persistence logic
- Set up multiple baby profile management

#### Key Interactions & Gestures:
- Toggle switches for settings
- Navigation to subsections
- Profile editing and creation
- Unit system switching

#### Acceptance Criteria:
- Users can manage all app settings
- Multiple baby profiles can be created and switched
- Settings changes are applied immediately
- Unit system changes update throughout the app
- UI matches design specifications

---

### Phase 9: Cloud Sync & Premium Features
**Target Duration:** 3 weeks

#### SwiftUI Views to Implement:
- `SubscriptionView`
- `BackupSettingsView`
- `SyncStatusView`
- `PremiumFeaturePromptView`

#### State Management & Data Flow:
- Implement Supabase authentication
- Create cloud synchronization service
- Set up RevenueCat integration
- Implement premium feature gating

#### Key Interactions & Gestures:
- Subscription purchase flow
- Manual sync trigger
- Account creation and login
- Premium feature upsell interactions

#### Acceptance Criteria:
- Users can create accounts and log in
- Data synchronizes between devices (for premium users)
- Subscription purchase flow works correctly
- Free users see appropriate upsell prompts
- Sync status is clearly indicated
- Error handling for network issues

---

### Phase 10: Refinement & App Store Preparation
**Target Duration:** 2 weeks

#### Tasks:
- Performance optimization
- Accessibility improvements
- Localization
- App Store screenshots and metadata
- Final UI polish
- TestFlight distribution

#### Acceptance Criteria:
- App performs well on all supported devices
- Accessibility features are fully implemented
- App Store assets are prepared
- Documentation is complete
- All critical bugs are resolved

## Development Timeline

| Phase | Duration | Cumulative Time |
|-------|----------|-----------------|
| 1: Foundation & Onboarding | 2 weeks | 2 weeks |
| 2: Core Logging Functionality | 3 weeks | 5 weeks |
| 3: Home Screen & Daily Summary | 2 weeks | 7 weeks |
| 4: Timeline & History View | 2 weeks | 9 weeks |
| 5: Growth & Health Tracking | 2 weeks | 11 weeks |
| 6: Insights & Analytics | 3 weeks | 14 weeks |
| 7: Chat Interface | 2 weeks | 16 weeks |
| 8: Settings & Profile Management | 2 weeks | 18 weeks |
| 9: Cloud Sync & Premium Features | 3 weeks | 21 weeks |
| 10: Refinement & App Store Preparation | 2 weeks | 23 weeks |

**Total Development Time:** Approximately 23 weeks (5-6 months)

## Milestone Releases

### Alpha Release (Week 7)
- Complete Phases 1-3
- Core functionality for logging and viewing baby data
- Internal testing only

### Beta Release (Week 14)
- Complete Phases 1-6
- Full tracking capabilities with insights
- Limited TestFlight distribution

### Production Release (Week 23)
- All phases complete
- Full feature set including premium capabilities
- App Store submission

## Testing Strategy

Each phase will include:
1. **Unit Tests:** For view models and business logic
2. **UI Tests:** For critical user flows
3. **Integration Tests:** For data persistence and synchronization
4. **Manual Testing:** For UI/UX validation

## Risk Mitigation

| Risk | Mitigation Strategy |
|------|---------------------|
| LLM API costs | Implement caching and rate limiting |
| SwiftData complexity | Start with simpler models, evolve gradually |
| Sync conflicts | Implement robust conflict resolution strategy |
| Performance issues | Regular profiling throughout development |
| App rejection | Early review of App Store guidelines |

## Conclusion

This phased development plan provides a structured approach to building the BabyPulse iOS application. By focusing on interface-driven development with clear milestones, the team can deliver incremental value while maintaining a cohesive vision for the final product.
