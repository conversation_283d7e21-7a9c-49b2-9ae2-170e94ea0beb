# Manual Testing Plan for Insight Generation System

## 1. Test Data Creation Workflow
```mermaid
graph TD
    A[Start] --> B[Launch iOS Simulator]
    B --> C[Create Test Entries via UI]
    C --> D[HealthEntry: Vitals/Notes]
    C --> E[DiaperEntry: Wet/Soiled Counts]
    C --> F[FeedingEntry: Volume/Duration]
    C --> G[SleepEntry: Start/End Times]
    D --> H[Sync to CoreData via SyncManager]
    E --> H
    F --> H
    G --> H
    H --> I[Test Data Ready]
```

**Steps:**
1. Use simulator's keyboard to input:
   - 5+ HealthEntries with varying vitals
   - 10+ DiaperEntries across different days
   - 8+ FeedingEntries with different feeding types
   - 7+ SleepEntries with partial night data
2. Validate entries appear in CoreData using Xcode's Debug View hierarchy

## 2. Insight Rendering Verification
```mermaid
graph LR
    J[Generate Insights] --> K[Fetch Entries from CoreData]
    K --> L[Process with InsightService]
    L --> M[Format with DesignSystem]
    M --> N[Render in InsightsView]
    N --> O[Verify: 
        - Card Layout
        - Date Grouping
        - Risk Banners
        - Action Buttons]
```

**Validation Steps:**
1. Navigate to Insights tab in simulator
2. Check for:
   - Correct date grouping (today/yesterday/older)
   - Proper risk banner display for abnormal vitals
   - Action buttons for "See Details" and "Share"
3. Use Accessibility Inspector to verify text contrast ratios

## 3. LLM Prompt Validation
**Key Validation Points:**
```yaml
# prompt_templates.yaml
health_insight_prompt:
  template: |
    Given {entry_count} health entries from {baby_name}, 
    identify patterns in: {vital_trends} 
    Provide: {actionable_recommendations}
```

**Testing Steps:**
1. Enable debug logging in LLMService.swift
2. For each insight generation:
   - Capture actual prompt sent to LLM
   - Compare with template from prompt_templates.yaml
   - Verify variable substitution:
     - entry_count matches actual entries
     - vital_trends includes all abnormal metrics
     - recommendations are baby-specific

## 4. Edge Case Testing
| Scenario | Test Steps | Expected Result |
|---------|------------|-----------------|
| **Insufficient Data** | Create 1 HealthEntry | Shows "Limited Data" banner |
| **Invalid Values** | Enter negative feeding volume | Shows validation error |
| **Conflicting Entries** | Add overlapping sleep entries | Displays conflict warning |
| **Empty State** | Delete all entries | Shows onboarding empty state |

## 5. Debugging Strategies
```mermaid
graph TD
    P[Issue Reproduction] --> Q[Enable Debug Logs]
    Q --> R[Set Breakpoints in Xcode]
    R --> S[LLMService: Prompt Generation]
    R --> T[InsightService: Data Processing]
    S --> U[Capture Network Logs]
    T --> V[Inspect CoreData Stack]
    U --> W[Analyze Response Latency]
    V --> X[Validate Data Transformations]
```

**Without Automated Tests:**
1. Use Xcode's View Debugger to inspect:
   - InsightCardView hierarchy
   - Constraint conflicts
2. Enable Slow Animations in Simulator to:
   - Verify loading state transitions
   - Test skeleton UI display
3. Use Console.app to filter logs by:
   - `LLMService.*` for prompt/response tracking
   - `InsightService.*` for processing errors