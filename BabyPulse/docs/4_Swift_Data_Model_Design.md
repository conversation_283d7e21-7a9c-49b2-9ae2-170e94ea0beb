# BabyPulse - Swift Data Model Design

This document defines the data model architecture for the BabyPulse application, covering both the local SwiftData schema and the corresponding Supabase PostgreSQL database structure for cloud synchronization.

## Data Model Overview

The BabyPulse data model is designed to support:
1. Multiple baby profiles
2. Various types of tracking entries (feeding, diapers, sleep, etc.)
3. User preferences and settings
4. Cloud synchronization for premium users
5. LLM-generated insights and chat history

## SwiftData Models

### Core Models

#### `Baby`
```swift
@Model
final class Baby {
    var id: UUID
    var name: String
    var dateOfBirth: Date
    var sexAtBirth: Sex
    var profileImageData: Data?
    var createdAt: Date
    var updatedAt: Date
    
    // Relationships
    @Relationship(.cascade) var feedingEntries: [FeedingEntry]?
    @Relationship(.cascade) var diaperEntries: [DiaperEntry]?
    @Relationship(.cascade) var sleepEntries: [SleepEntry]?
    @Relationship(.cascade) var growthEntries: [GrowthEntry]?
    @Relationship(.cascade) var healthEntries: [HealthEntry]?
    @Relationship(.cascade) var milestoneEntries: [MilestoneEntry]?
    @Relationship(.cascade) var dailyEventEntries: [DailyEventEntry]?
    @Relationship(.cascade) var noteEntries: [NoteEntry]?
    
    // Computed properties (not stored)
    var ageInDays: Int { Calendar.current.dateComponents([.day], from: dateOfBirth, to: Date()).day ?? 0 }
    
    enum Sex: String, Codable {
        case male
        case female
        case other
    }
    
    init(id: UUID = UUID(), name: String, dateOfBirth: Date, sexAtBirth: Sex, profileImageData: Data? = nil) {
        self.id = id
        self.name = name
        self.dateOfBirth = dateOfBirth
        self.sexAtBirth = sexAtBirth
        self.profileImageData = profileImageData
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}
```

#### `UserPreferences`
```swift
@Model
final class UserPreferences {
    var id: UUID
    var unitSystem: UnitSystem
    var isDarkModeEnabled: Bool
    var notificationsEnabled: Bool
    var reminderTimes: [Date]?
    var createdAt: Date
    var updatedAt: Date
    
    enum UnitSystem: String, Codable {
        case metric
        case imperial
    }
    
    init(id: UUID = UUID(), unitSystem: UnitSystem = .metric, isDarkModeEnabled: Bool = false, notificationsEnabled: Bool = true) {
        self.id = id
        self.unitSystem = unitSystem
        self.isDarkModeEnabled = isDarkModeEnabled
        self.notificationsEnabled = notificationsEnabled
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}
```

### Entry Models

#### Base Entry Model (Abstract)
```swift
@Model
class BaseEntry {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus
    
    // Relationship
    @Relationship var baby: Baby?
    
    enum SyncStatus: String, Codable {
        case notSynced
        case syncing
        case synced
        case syncFailed
    }
    
    init(id: UUID = UUID(), timestamp: Date, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
    }
}
```

#### `FeedingEntry`
```swift
@Model
final class FeedingEntry: BaseEntry {
    var type: FeedingType
    var duration: Int?  // In minutes, for breastfeeding
    var volume: Double? // In ml
    var content: BottleContent?
    var leftBreast: Bool?
    var rightBreast: Bool?
    var foodItem: String?
    var foodAmount: String?
    var reaction: String?
    
    enum FeedingType: String, Codable {
        case breastfeeding
        case bottleFeeding
        case solidFood
    }
    
    enum BottleContent: String, Codable {
        case formula
        case expressedBreastMilk
        case mixed
    }
    
    init(id: UUID = UUID(), timestamp: Date, type: FeedingType, notes: String? = nil, baby: Baby? = nil) {
        self.type = type
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `DiaperEntry`
```swift
@Model
final class DiaperEntry: BaseEntry {
    var type: DiaperType
    var poopColor: PoopColor?
    var poopConsistency: PoopConsistency?
    
    enum DiaperType: String, Codable {
        case wet
        case dirty
        case mixed
    }
    
    enum PoopColor: String, Codable {
        case yellow
        case green
        case brown
        case black
        case red
        case white
    }
    
    enum PoopConsistency: String, Codable {
        case runny
        case seedy
        case pasty
        case formed
        case hard
    }
    
    init(id: UUID = UUID(), timestamp: Date, type: DiaperType, notes: String? = nil, baby: Baby? = nil) {
        self.type = type
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `SleepEntry`
```swift
@Model
final class SleepEntry: BaseEntry {
    var endTime: Date?
    var duration: Int? // In minutes, calculated or manually entered
    var location: SleepLocation?
    
    enum SleepLocation: String, Codable {
        case crib
        case bassinet
        case bed
        case arms
        case stroller
        case carseat
        case other
    }
    
    var calculatedDuration: Int? {
        guard let endTime = endTime else { return duration }
        return Int(endTime.timeIntervalSince(timestamp) / 60)
    }
    
    init(id: UUID = UUID(), startTime: Date, endTime: Date? = nil, location: SleepLocation? = nil, notes: String? = nil, baby: Baby? = nil) {
        self.endTime = endTime
        self.location = location
        super.init(id: id, timestamp: startTime, notes: notes, baby: baby)
    }
}
```

#### `GrowthEntry`
```swift
@Model
final class GrowthEntry: BaseEntry {
    var weight: Double?      // In kg
    var height: Double?      // In cm
    var headCircumference: Double? // In cm
    
    // Calculated percentiles (not stored in database)
    var weightPercentile: Double?
    var heightPercentile: Double?
    var headCircumferencePercentile: Double?
    
    init(id: UUID = UUID(), timestamp: Date, weight: Double? = nil, height: Double? = nil, headCircumference: Double? = nil, notes: String? = nil, baby: Baby? = nil) {
        self.weight = weight
        self.height = height
        self.headCircumference = headCircumference
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `HealthEntry`
```swift
@Model
final class HealthEntry: BaseEntry {
    var type: HealthEntryType
    var temperature: Double?
    var temperatureUnit: TemperatureUnit?
    var medicationName: String?
    var medicationDosage: String?
    var symptoms: [Symptom]?
    var vaccineName: String?
    var appointmentReason: String?
    var appointmentProvider: String?
    
    enum HealthEntryType: String, Codable {
        case temperature
        case medication
        case symptom
        case vaccination
        case appointment
    }
    
    enum TemperatureUnit: String, Codable {
        case celsius
        case fahrenheit
    }
    
    enum Symptom: String, Codable, CaseIterable {
        case rash
        case cough
        case congestion
        case fever
        case vomiting
        case diarrhea
        case constipation
        case irritability
        case lethargy
        case other
    }
    
    init(id: UUID = UUID(), timestamp: Date, type: HealthEntryType, notes: String? = nil, baby: Baby? = nil) {
        self.type = type
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `MilestoneEntry`
```swift
@Model
final class MilestoneEntry: BaseEntry {
    var title: String
    var category: MilestoneCategory
    var imageData: Data?
    
    enum MilestoneCategory: String, Codable, CaseIterable {
        case motor
        case cognitive
        case social
        case language
        case other
    }
    
    init(id: UUID = UUID(), timestamp: Date, title: String, category: MilestoneCategory, notes: String? = nil, imageData: Data? = nil, baby: Baby? = nil) {
        self.title = title
        self.category = category
        self.imageData = imageData
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `DailyEventEntry`
```swift
@Model
final class DailyEventEntry: BaseEntry {
    var eventType: EventType
    var duration: Int? // In minutes
    
    enum EventType: String, Codable, CaseIterable {
        case crying
        case fussiness
        case tummyTime
        case bathTime
        case outdoors
        case play
        case other
    }
    
    init(id: UUID = UUID(), timestamp: Date, eventType: EventType, duration: Int? = nil, notes: String? = nil, baby: Baby? = nil) {
        self.eventType = eventType
        self.duration = duration
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

#### `NoteEntry`
```swift
@Model
final class NoteEntry: BaseEntry {
    var title: String
    
    init(id: UUID = UUID(), timestamp: Date, title: String, notes: String, baby: Baby? = nil) {
        self.title = title
        super.init(id: id, timestamp: timestamp, notes: notes, baby: baby)
    }
}
```

### LLM-Related Models

#### `ChatMessage`
```swift
@Model
final class ChatMessage {
    var id: UUID
    var content: String
    var isFromUser: Bool
    var timestamp: Date
    var relatedEntryIds: [String]? // References to entries this message is about
    
    // Relationship
    @Relationship var baby: Baby?
    
    init(id: UUID = UUID(), content: String, isFromUser: Bool, timestamp: Date = Date(), relatedEntryIds: [String]? = nil, baby: Baby? = nil) {
        self.id = id
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.relatedEntryIds = relatedEntryIds
        self.baby = baby
    }
}
```

#### `Insight`
```swift
@Model
final class Insight {
    var id: UUID
    var title: String
    var description: String
    var metric: String
    var category: InsightCategory
    var timestamp: Date
    var isRead: Bool
    var relatedEntryIds: [String]? // References to entries this insight is based on
    
    // Relationship
    @Relationship var baby: Baby?
    
    enum InsightCategory: String, Codable {
        case feeding
        case sleep
        case diaper
        case growth
        case health
        case milestone
        case general
    }
    
    init(id: UUID = UUID(), title: String, description: String, metric: String, category: InsightCategory, timestamp: Date = Date(), isRead: Bool = false, relatedEntryIds: [String]? = nil, baby: Baby? = nil) {
        self.id = id
        self.title = title
        self.description = description
        self.metric = metric
        self.category = category
        self.timestamp = timestamp
        self.isRead = isRead
        self.relatedEntryIds = relatedEntryIds
        self.baby = baby
    }
}
```

### Premium Features Models

#### `UserAccount`
```swift
@Model
final class UserAccount {
    var id: UUID
    var email: String
    var isEmailVerified: Bool
    var supabaseUserId: String?
    var subscriptionStatus: SubscriptionStatus
    var subscriptionExpiryDate: Date?
    var lastSyncDate: Date?
    
    enum SubscriptionStatus: String, Codable {
        case free
        case trial
        case premium
        case expired
    }
    
    init(id: UUID = UUID(), email: String, isEmailVerified: Bool = false, supabaseUserId: String? = nil, subscriptionStatus: SubscriptionStatus = .free) {
        self.id = id
        self.email = email
        self.isEmailVerified = isEmailVerified
        self.supabaseUserId = supabaseUserId
        self.subscriptionStatus = subscriptionStatus
    }
}
```

## SwiftData Schema Configuration

```swift
// In BabyPulseApp.swift
@main
struct BabyPulseApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Baby.self,
            UserPreferences.self,
            FeedingEntry.self,
            DiaperEntry.self,
            SleepEntry.self,
            GrowthEntry.self,
            HealthEntry.self,
            MilestoneEntry.self,
            DailyEventEntry.self,
            NoteEntry.self,
            ChatMessage.self,
            Insight.self,
            UserAccount.self
        ])
        
        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true
        )
        
        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .modelContainer(sharedModelContainer)
        }
    }
}
```

## Supabase PostgreSQL Schema

The Supabase database schema mirrors the SwiftData models while optimizing for PostgreSQL's relational capabilities.

### Core Tables

#### `babies`
```sql
CREATE TABLE babies (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    date_of_birth TIMESTAMP WITH TIME ZONE NOT NULL,
    sex_at_birth TEXT NOT NULL,
    profile_image_url TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE babies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own babies"
    ON babies
    USING (user_id = auth.uid());
```

#### `user_preferences`
```sql
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    unit_system TEXT NOT NULL DEFAULT 'metric',
    is_dark_mode_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    notifications_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    reminder_times JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own preferences"
    ON user_preferences
    USING (user_id = auth.uid());
```

### Entry Tables

#### `feeding_entries`
```sql
CREATE TABLE feeding_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    type TEXT NOT NULL,
    duration INTEGER,
    volume DECIMAL,
    content TEXT,
    left_breast BOOLEAN,
    right_breast BOOLEAN,
    food_item TEXT,
    food_amount TEXT,
    reaction TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE feeding_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own feeding entries"
    ON feeding_entries
    USING (user_id = auth.uid());
```

#### `diaper_entries`
```sql
CREATE TABLE diaper_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    type TEXT NOT NULL,
    poop_color TEXT,
    poop_consistency TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE diaper_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own diaper entries"
    ON diaper_entries
    USING (user_id = auth.uid());
```

#### `sleep_entries`
```sql
CREATE TABLE sleep_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    location TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE sleep_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own sleep entries"
    ON sleep_entries
    USING (user_id = auth.uid());
```

#### `growth_entries`
```sql
CREATE TABLE growth_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    weight DECIMAL,
    height DECIMAL,
    head_circumference DECIMAL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE growth_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own growth entries"
    ON growth_entries
    USING (user_id = auth.uid());
```

#### `health_entries`
```sql
CREATE TABLE health_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    type TEXT NOT NULL,
    temperature DECIMAL,
    temperature_unit TEXT,
    medication_name TEXT,
    medication_dosage TEXT,
    symptoms JSONB,
    vaccine_name TEXT,
    appointment_reason TEXT,
    appointment_provider TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE health_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own health entries"
    ON health_entries
    USING (user_id = auth.uid());
```

#### `milestone_entries`
```sql
CREATE TABLE milestone_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    title TEXT NOT NULL,
    category TEXT NOT NULL,
    image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE milestone_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own milestone entries"
    ON milestone_entries
    USING (user_id = auth.uid());
```

#### `daily_event_entries`
```sql
CREATE TABLE daily_event_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    event_type TEXT NOT NULL,
    duration INTEGER,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE daily_event_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own daily event entries"
    ON daily_event_entries
    USING (user_id = auth.uid());
```

#### `note_entries`
```sql
CREATE TABLE note_entries (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    title TEXT NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE note_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own note entries"
    ON note_entries
    USING (user_id = auth.uid());
```

### LLM-Related Tables

#### `chat_messages`
```sql
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_from_user BOOLEAN NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    related_entry_ids JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own chat messages"
    ON chat_messages
    USING (user_id = auth.uid());
```

#### `insights`
```sql
CREATE TABLE insights (
    id UUID PRIMARY KEY,
    baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    metric TEXT NOT NULL,
    category TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    related_entry_ids JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE insights ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own insights"
    ON insights
    USING (user_id = auth.uid());
```

## Data Synchronization Strategy

### Sync Process Overview

1. **Local-First Approach:**
   - All data is stored locally in SwiftData first
   - Premium users' data is synced to Supabase when network is available
   - Free users' data remains local only

2. **Conflict Resolution:**
   - Timestamp-based "last write wins" strategy
   - Each model includes `updatedAt` field for tracking changes
   - Sync conflicts are logged for manual resolution if needed

3. **Sync Status Tracking:**
   - Each entry includes a `syncStatus` field in SwiftData
   - Status transitions: notSynced → syncing → synced (or syncFailed)
   - Failed syncs are retried automatically

### Sync Implementation

```swift
// Example sync service implementation
class SyncService {
    private let supabaseClient: SupabaseClient
    private let modelContext: ModelContext
    
    init(supabaseClient: SupabaseClient, modelContext: ModelContext) {
        self.supabaseClient = supabaseClient
        self.modelContext = modelContext
    }
    
    func syncBabyProfiles() async throws {
        // Fetch babies from local database that need syncing
        let descriptor = FetchDescriptor<Baby>(
            predicate: #Predicate<Baby> { $0.syncStatus != .synced }
        )
        let babiesToSync = try modelContext.fetch(descriptor)
        
        for baby in babiesToSync {
            // Update sync status
            baby.syncStatus = .syncing
            try modelContext.save()
            
            do {
                // Convert to Supabase format and sync
                let babyData: [String: Any] = [
                    "id": baby.id.uuidString,
                    "name": baby.name,
                    "date_of_birth": baby.dateOfBirth,
                    "sex_at_birth": baby.sexAtBirth.rawValue,
                    "updated_at": baby.updatedAt
                ]
                
                // Check if baby exists in Supabase
                let response = try await supabaseClient
                    .from("babies")
                    .select("id")
                    .eq("id", baby.id.uuidString)
                    .execute()
                
                if response.count > 0 {
                    // Update existing record
                    try await supabaseClient
                        .from("babies")
                        .update(babyData)
                        .eq("id", baby.id.uuidString)
                        .execute()
                } else {
                    // Insert new record
                    try await supabaseClient
                        .from("babies")
                        .insert(babyData)
                        .execute()
                }
                
                // Update sync status on success
                baby.syncStatus = .synced
                try modelContext.save()
            } catch {
                // Handle sync failure
                baby.syncStatus = .syncFailed
                try modelContext.save()
                throw error
            }
        }
    }
    
    // Similar methods for other model types...
}
```

## Model Extensions for Supabase Integration

```swift
// Example extension to convert SwiftData models to Supabase format
extension Baby {
    func toSupabaseFormat() -> [String: Any] {
        return [
            "id": id.uuidString,
            "name": name,
            "date_of_birth": dateOfBirth,
            "sex_at_birth": sexAtBirth.rawValue,
            "updated_at": updatedAt
        ]
    }
    
    static func fromSupabaseFormat(_ data: [String: Any]) -> Baby? {
        guard 
            let idString = data["id"] as? String,
            let id = UUID(uuidString: idString),
            let name = data["name"] as? String,
            let dateOfBirth = data["date_of_birth"] as? Date,
            let sexAtBirthString = data["sex_at_birth"] as? String,
            let sexAtBirth = Sex(rawValue: sexAtBirthString)
        else {
            return nil
        }
        
        return Baby(
            id: id,
            name: name,
            dateOfBirth: dateOfBirth,
            sexAtBirth: sexAtBirth
        )
    }
}
```

## SwiftData Queries and Predicates

### Common Query Patterns

```swift
// Fetch all entries for a specific baby
func fetchEntriesForBaby<T: BaseEntry>(baby: Baby) throws -> [T] {
    let descriptor = FetchDescriptor<T>(
        predicate: #Predicate<T> { $0.baby?.id == baby.id }
    )
    return try modelContext.fetch(descriptor)
}

// Fetch entries for a date range
func fetchEntriesForDateRange<T: BaseEntry>(baby: Baby, startDate: Date, endDate: Date) throws -> [T] {
    let descriptor = FetchDescriptor<T>(
        predicate: #Predicate<T> { 
            $0.baby?.id == baby.id && 
            $0.timestamp >= startDate && 
            $0.timestamp <= endDate 
        },
        sortBy: [SortDescriptor(\.timestamp, order: .forward)]
    )
    return try modelContext.fetch(descriptor)
}

// Fetch entries by type (example for feeding entries)
func fetchFeedingEntriesByType(baby: Baby, type: FeedingEntry.FeedingType) throws -> [FeedingEntry] {
    let descriptor = FetchDescriptor<FeedingEntry>(
        predicate: #Predicate<FeedingEntry> { 
            $0.baby?.id == baby.id && 
            $0.type == type 
        },
        sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
    )
    return try modelContext.fetch(descriptor)
}
```

## Migration Strategy

### SwiftData Schema Versioning

```swift
// Example of schema versioning for future migrations
let schema = Schema([
    Baby.self,
    UserPreferences.self,
    FeedingEntry.self,
    // ... other models
], version: VersionedSchema.Version(1, 0, 0))

// Migration plan for future versions
let migrationPlan = MigrationPlan(
    from: VersionedSchema.Version(1, 0, 0),
    to: VersionedSchema.Version(2, 0, 0),
    migrateModel: { context in
        // Migration logic here
    }
)
```

## Conclusion

This data model design provides a comprehensive foundation for the BabyPulse application, supporting both local storage with SwiftData and cloud synchronization with Supabase. The model structure is designed to be:

1. **Flexible:** Supporting all required tracking categories
2. **Relational:** Maintaining proper relationships between entities
3. **Extensible:** Allowing for future feature additions
4. **Efficient:** Optimized for common query patterns
5. **Syncable:** Structured for seamless cloud synchronization

The SwiftData models and Supabase schema are aligned to ensure data integrity across platforms while leveraging the strengths of each technology.
