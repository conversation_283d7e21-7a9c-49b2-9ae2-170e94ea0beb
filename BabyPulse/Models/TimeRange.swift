//
//  TimeRange.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Time range for data analysis
enum TimeRange {
    case last24Hours
    case last7Days
    case last30Days
    case custom(DateInterval)
    
    var startDate: Date {
        switch self {
        case .last24Hours:
            return Calendar.current.date(byAdding: .hour, value: -24, to: Date()) ?? Date()
        case .last7Days:
            return Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        case .last30Days:
            return Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        case .custom(let interval):
            return interval.start
        }
    }
}
