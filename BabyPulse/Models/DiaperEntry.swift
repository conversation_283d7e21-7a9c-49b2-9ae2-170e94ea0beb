//
//  DiaperEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class DiaperEntry: Identifiable, EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus

    // Relationship
    @Relationship var baby: Baby?

    // Diaper-specific properties
    var type: DiaperType
    var poopColor: PoopColor?
    var poopConsistency: PoopConsistency?

    enum DiaperType: String, Codable, CaseIterable {
        case wet
        case dirty
        case mixed

        var description: String {
            switch self {
            case .wet:
                return "Wet"
            case .dirty:
                return "Dirty"
            case .mixed:
                return "Mixed"
            }
        }
    }

    enum PoopColor: String, Codable, CaseIterable {
        case yellow
        case green
        case brown
        case black
        case red
        case white

        var description: String {
            switch self {
            case .yellow:
                return "Yellow"
            case .green:
                return "Green"
            case .brown:
                return "Brown"
            case .black:
                return "Black"
            case .red:
                return "Red"
            case .white:
                return "White"
            }
        }
    }

    enum PoopConsistency: String, Codable, CaseIterable {
        case runny
        case seedy
        case pasty
        case formed
        case hard

        var description: String {
            switch self {
            case .runny:
                return "Runny"
            case .seedy:
                return "Seedy"
            case .pasty:
                return "Pasty"
            case .formed:
                return "Formed"
            case .hard:
                return "Hard"
            }
        }
    }

    init(id: UUID = UUID(), timestamp: Date, type: DiaperType, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
        self.type = type
    }

    // Convenience initializers for different diaper types
    static func createWet(timestamp: Date, notes: String? = nil, baby: Baby? = nil) -> DiaperEntry {
        return DiaperEntry(timestamp: timestamp, type: .wet, notes: notes, baby: baby)
    }

    static func createDirty(timestamp: Date, poopColor: PoopColor, poopConsistency: PoopConsistency, notes: String? = nil, baby: Baby? = nil) -> DiaperEntry {
        let entry = DiaperEntry(timestamp: timestamp, type: .dirty, notes: notes, baby: baby)
        entry.poopColor = poopColor
        entry.poopConsistency = poopConsistency
        return entry
    }

    static func createMixed(timestamp: Date, poopColor: PoopColor, poopConsistency: PoopConsistency, notes: String? = nil, baby: Baby? = nil) -> DiaperEntry {
        let entry = DiaperEntry(timestamp: timestamp, type: .mixed, notes: notes, baby: baby)
        entry.poopColor = poopColor
        entry.poopConsistency = poopConsistency
        return entry
    }
}
