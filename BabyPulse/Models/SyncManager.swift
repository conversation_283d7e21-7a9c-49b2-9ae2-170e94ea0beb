import Foundation
import SwiftData
import Combine

/// Manager for syncing data between SwiftData and Supabase
class SyncManager: ObservableObject {
    /// Shared instance of the sync manager
    static let shared = SyncManager()

    /// The Supabase service
    private let supabaseService = SupabaseService.shared

    /// The RevenueCat service
    private let revenueCatService = RevenueCatService.shared

    /// Queue for pending sync operations
    private var syncQueue: [SyncOperation] = []

    /// Flag indicating if a sync is in progress
    @Published private(set) var isSyncing = false

    /// Flag indicating if the device is online
    @Published private(set) var isOnline = true

    /// Network monitor for checking connectivity
    private var networkMonitor: Any? = nil
    private var cancellables = Set<AnyCancellable>()

    /// Private initializer for singleton
    private init() {
        // Set up network monitoring
        setupNetworkMonitoring()

        // Load pending sync operations from UserDefaults
        loadSyncQueue()
    }

    /// Set up network monitoring
    private func setupNetworkMonitoring() {
        // In a real implementation, you would use NWPathMonitor to check connectivity
        // For simplicity, we'll just assume the device is online
        isOnline = true
    }

    /// Load pending sync operations from UserDefaults
    private func loadSyncQueue() {
        if let data = UserDefaults.standard.data(forKey: "syncQueue"),
           let queue = try? JSONDecoder().decode([SyncOperation].self, from: data) {
            syncQueue = queue
        }
    }

    /// Save pending sync operations to UserDefaults
    private func saveSyncQueue() {
        if let data = try? JSONEncoder().encode(syncQueue) {
            UserDefaults.standard.set(data, forKey: "syncQueue")
        }
    }

    /// Add a sync operation to the queue
    func queueSyncOperation(type: String, babyId: String, data: [String: Any]) {
        // Process the data to ensure all UUIDs are converted to strings
        let processedData = processDataForJSON(data)

        let operation = SyncOperation(type: type, babyId: babyId, data: processedData)
        syncQueue.append(operation)
        saveSyncQueue()

        // Try to sync immediately if online
        if isOnline {
            syncPendingOperations()
        }
    }

    /// Process data to ensure all values are JSON-serializable
    private func processDataForJSON(_ data: [String: Any]) -> [String: Any] {
        var result: [String: Any] = [:]

        for (key, value) in data {
            if let uuid = value as? UUID {
                // Convert UUID to string
                result[key] = uuid.uuidString
            } else if let date = value as? Date {
                // Convert Date to ISO8601 string
                result[key] = ISO8601DateFormatter().string(from: date)
            } else if let dict = value as? [String: Any] {
                // Recursively process nested dictionaries
                result[key] = processDataForJSON(dict)
            } else if let array = value as? [Any] {
                // Process arrays
                result[key] = array.map { item in
                    if let uuid = item as? UUID {
                        return uuid.uuidString
                    } else if let date = item as? Date {
                        return ISO8601DateFormatter().string(from: date)
                    } else if let dict = item as? [String: Any] {
                        // We can't return a dictionary directly in a map that expects a specific type
                        // Convert the dictionary to a JSON string
                        if let jsonData = try? JSONSerialization.data(withJSONObject: processDataForJSON(dict)),
                           let jsonString = String(data: jsonData, encoding: .utf8) {
                            return jsonString
                        } else {
                            return "{}" // Return empty JSON object as fallback
                        }
                    } else if let string = item as? String {
                        return string
                    } else {
                        return "{}"
                    }
                }
            } else {
                // Pass through other values
                result[key] = value
            }
        }

        return result
    }

    /// Sync pending operations
    func syncPendingOperations() {
        guard !isSyncing, isOnline, !syncQueue.isEmpty else { return }

        isSyncing = true

        Task {
            // Create a local copy of the queue to avoid concurrent access issues
            let operationsToSync = syncQueue
            var successfulIndices: [Int] = []

            for (index, operation) in operationsToSync.enumerated() {
                do {
                    // Use the syncData method from SupabaseService
                    try await supabaseService.syncData(
                        type: operation.type,
                        babyId: operation.babyId,
                        data: operation.data
                    )

                    // Mark as successful
                    successfulIndices.append(index)

                    // Update the sync status of the corresponding entry
                    if let entryId = operation.data["id"] as? String, let id = UUID(uuidString: entryId) {
                        await updateSyncStatus(id: id, type: operation.type, status: .synced)
                    } else if let entryId = operation.data["id"] as? UUID {
                        await updateSyncStatus(id: entryId, type: operation.type, status: .synced)
                    }
                } catch {
                    print("Error syncing operation: \(error)")

                    // Update the sync status to failed
                    if let entryId = operation.data["id"] as? String, let id = UUID(uuidString: entryId) {
                        await updateSyncStatus(id: id, type: operation.type, status: .syncFailed)
                    } else if let entryId = operation.data["id"] as? UUID {
                        await updateSyncStatus(id: entryId, type: operation.type, status: .syncFailed)
                    }

                    // Continue with next operation
                }
            }

            // Remove successful operations from the queue
            // Create a local copy of successfulIndices to avoid Swift 6 concurrency warning
            let localSuccessfulIndices = successfulIndices

            await MainActor.run {
                // Create a new queue without the successful operations
                let newQueue = syncQueue.enumerated().filter { index, _ in
                    !localSuccessfulIndices.contains(index)
                }.map { _, operation in
                    operation
                }

                syncQueue = newQueue
                saveSyncQueue()
                isSyncing = false
            }
        }
    }

    /// Update the sync status of an entry
    @MainActor
    private func updateSyncStatus(id: UUID, type: String, status: SyncStatus) async {
        // This would typically use a ModelContext to find and update the entry
        // For now, we'll just print a message
        print("Updated sync status of \(type) entry \(id) to \(status)")

        // In a real implementation, you would find the entry in the database and update its status
        // For example:
        // let descriptor = FetchDescriptor<FeedingEntry>(predicate: #Predicate { $0.id == id })
        // if let entry = try? modelContext.fetch(descriptor).first {
        //     entry.syncStatus = status
        //     try? modelContext.save()
        // }
    }

    /// Sync a baby to Supabase
    func syncBaby(baby: Baby) {
        let babyData: [String: Any] = [
            "id": baby.id.uuidString,
            "name": baby.name,
            "birth_date": ISO8601DateFormatter().string(from: baby.birthDate),
            "gender": baby.gender
        ]

        queueSyncOperation(type: "baby", babyId: baby.id.uuidString, data: babyData)
    }

    /// Sync a feeding entry to Supabase
    func syncFeedingEntry(entry: FeedingEntry) async throws {
        guard let baby = entry.baby else {
            throw SyncError.missingBaby
        }

        var entryData: [String: Any] = [
            "id": entry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "timestamp": ISO8601DateFormatter().string(from: entry.timestamp),
            "feeding_type": entry.type.rawValue,
            "created_at": ISO8601DateFormatter().string(from: entry.createdAt),
            "updated_at": ISO8601DateFormatter().string(from: entry.updatedAt)
        ]

        // Add optional fields
        if let duration = entry.duration {
            entryData["duration"] = duration
        }

        if let volume = entry.volume {
            entryData["volume"] = volume
        }

        if let bottleContent = entry.content?.rawValue {
            entryData["bottle_content"] = bottleContent
        }

        if let leftBreast = entry.leftBreast {
            entryData["left_breast"] = leftBreast
        }

        if let rightBreast = entry.rightBreast {
            entryData["right_breast"] = rightBreast
        }

        if let foodItem = entry.foodItem {
            entryData["food_item"] = foodItem
        }

        if let foodAmount = entry.foodAmount {
            entryData["food_amount"] = foodAmount
        }

        if let reaction = entry.reaction {
            entryData["reaction"] = reaction
        }

        if let notes = entry.notes {
            entryData["notes"] = notes
        }

        // Update sync status
        entry.syncStatus = .syncing

        queueSyncOperation(type: "feeding", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync a diaper entry to Supabase
    func syncDiaperEntry(entry: DiaperEntry) async throws {
        guard let baby = entry.baby else {
            throw SyncError.missingBaby
        }

        var entryData: [String: Any] = [
            "id": entry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "timestamp": ISO8601DateFormatter().string(from: entry.timestamp),
            "diaper_type": entry.type.rawValue,
            "created_at": ISO8601DateFormatter().string(from: entry.createdAt),
            "updated_at": ISO8601DateFormatter().string(from: entry.updatedAt)
        ]

        // Add optional fields
        if let poopColor = entry.poopColor?.rawValue {
            entryData["poop_color"] = poopColor
        }

        if let poopConsistency = entry.poopConsistency?.rawValue {
            entryData["poop_consistency"] = poopConsistency
        }

        if let notes = entry.notes {
            entryData["notes"] = notes
        }

        // Update sync status
        entry.syncStatus = .syncing

        queueSyncOperation(type: "diaper", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync a sleep entry to Supabase
    func syncSleepEntry(entry: SleepEntry) async throws {
        guard let baby = entry.baby else {
            throw SyncError.missingBaby
        }

        var entryData: [String: Any] = [
            "id": entry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "start_ts": ISO8601DateFormatter().string(from: entry.timestamp),
            "created_at": ISO8601DateFormatter().string(from: entry.createdAt),
            "updated_at": ISO8601DateFormatter().string(from: entry.updatedAt)
        ]

        // Add optional fields
        if let endTime = entry.endTime {
            entryData["end_ts"] = ISO8601DateFormatter().string(from: endTime)
        }

        if let duration = entry.duration {
            entryData["duration"] = duration
        }

        if let location = entry.location?.rawValue {
            entryData["location"] = location
        }

        if let notes = entry.notes {
            entryData["notes"] = notes
        }

        // Update sync status
        entry.syncStatus = .syncing

        queueSyncOperation(type: "sleep", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync a health entry to Supabase
    func syncHealthEntry(entry: HealthEntry) async throws {
        guard let baby = entry.baby else {
            throw SyncError.missingBaby
        }

        var entryData: [String: Any] = [
            "id": entry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "kind": entry.type.rawValue,
            "ts": ISO8601DateFormatter().string(from: entry.timestamp),
            "created_at": ISO8601DateFormatter().string(from: entry.createdAt),
            "updated_at": ISO8601DateFormatter().string(from: entry.updatedAt)
        ]

        // Add optional fields
        if let notes = entry.notes {
            entryData["notes"] = notes
        }

        // Add type-specific fields
        switch entry.type {
        case .temperature:
            if let temperature = entry.temperature, let unit = entry.temperatureUnit {
                // Convert to Celsius if needed
                let tempCelsius = unit == .fahrenheit ? (temperature - 32) * 5/9 : temperature
                entryData["temp_c"] = tempCelsius
            }
        case .medication:
            if let medicationName = entry.medicationName {
                entryData["medication"] = medicationName
            }
            if let medicationDosage = entry.medicationDosage {
                entryData["dosage"] = medicationDosage
            }
        case .symptom:
            let symptoms = entry.getSymptoms()
            if !symptoms.isEmpty {
                entryData["symptom"] = symptoms[0].rawValue

                // Determine severity from notes
                if let notes = entry.notes {
                    if notes.lowercased().contains("severe") {
                        entryData["severity"] = "severe"
                    } else if notes.lowercased().contains("moderate") {
                        entryData["severity"] = "moderate"
                    } else {
                        entryData["severity"] = "mild"
                    }
                } else {
                    entryData["severity"] = "mild"
                }
            }
        case .vaccination:
            if let vaccineName = entry.vaccineName {
                entryData["vaccine"] = vaccineName
            }
        case .appointment:
            if let appointmentProvider = entry.appointmentProvider {
                entryData["place"] = appointmentProvider
            }
            if let appointmentReason = entry.appointmentReason {
                entryData["appt_reason"] = appointmentReason
            }
        }

        // Update sync status
        entry.syncStatus = .syncing

        queueSyncOperation(type: "health", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync a growth entry to Supabase
    func syncGrowthEntry(entry: GrowthEntry) async throws {
        guard let baby = entry.baby else {
            throw SyncError.missingBaby
        }

        // Calculate percentiles if not already done
        entry.calculatePercentiles()

        var entryData: [String: Any] = [
            "id": entry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "ts": ISO8601DateFormatter().string(from: entry.timestamp),
            "created_at": ISO8601DateFormatter().string(from: entry.createdAt),
            "updated_at": ISO8601DateFormatter().string(from: entry.updatedAt)
        ]

        // Add optional fields
        if let weight = entry.weight {
            entryData["weight_kg"] = weight
        }

        if let height = entry.height {
            entryData["height_cm"] = height
        }

        if let headCircumference = entry.headCircumference {
            entryData["head_cm"] = headCircumference
        }

        if let weightPercentile = entry.weightPercentile {
            entryData["weight_pct"] = weightPercentile
        }

        if let heightPercentile = entry.heightPercentile {
            entryData["height_pct"] = heightPercentile
        }

        if let headCircumferencePercentile = entry.headCircumferencePercentile {
            entryData["head_pct"] = headCircumferencePercentile
        }

        if let notes = entry.notes {
            entryData["notes"] = notes
        }

        // Update sync status
        entry.syncStatus = .syncing

        queueSyncOperation(type: "growth", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync all data for a given ModelContext
    func syncAllData(modelContext: ModelContext? = nil) async throws {
        // Check if user is authenticated
        guard supabaseService.isAuthenticated else {
            throw SyncError.notAuthenticated
        }

        // Check if user has premium access
        guard revenueCatService.hasPremiumAccess() else {
            throw SyncError.premiumRequired
        }

        // Check if device is online
        guard isOnline else {
            throw SyncError.offline
        }

        // Set syncing flag
        await MainActor.run {
            isSyncing = true
        }

        do {
            // Use the provided ModelContext
            guard let context = modelContext else {
                throw SyncError.noModelContext
            }

            // Sync babies
            let babyDescriptor = FetchDescriptor<Baby>()
            let babies = try context.fetch(babyDescriptor)

            for baby in babies {
                syncBaby(baby: baby)
            }

            // Sync feeding entries
            let feedingDescriptor = FetchDescriptor<FeedingEntry>()
            let feedings = try context.fetch(feedingDescriptor)

            for feeding in feedings {
                do {
                    try await syncFeedingEntry(entry: feeding)
                } catch {
                    print("Error syncing feeding entry: \(error)")
                }
            }

            // Sync diaper entries
            let diaperDescriptor = FetchDescriptor<DiaperEntry>()
            let diapers = try context.fetch(diaperDescriptor)

            for diaper in diapers {
                do {
                    try await syncDiaperEntry(entry: diaper)
                } catch {
                    print("Error syncing diaper entry: \(error)")
                }
            }

            // Sync sleep entries
            let sleepDescriptor = FetchDescriptor<SleepEntry>()
            let sleeps = try context.fetch(sleepDescriptor)

            for sleep in sleeps {
                do {
                    try await syncSleepEntry(entry: sleep)
                } catch {
                    print("Error syncing sleep entry: \(error)")
                }
            }

            // Sync health entries
            let healthDescriptor = FetchDescriptor<HealthEntry>()
            let healths = try context.fetch(healthDescriptor)

            for health in healths {
                do {
                    try await syncHealthEntry(entry: health)
                } catch {
                    print("Error syncing health entry: \(error)")
                }
            }

            // Sync growth entries
            let growthDescriptor = FetchDescriptor<GrowthEntry>()
            let growths = try context.fetch(growthDescriptor)

            for growth in growths {
                do {
                    try await syncGrowthEntry(entry: growth)
                } catch {
                    print("Error syncing growth entry: \(error)")
                }
            }

            // Process the sync queue
            syncPendingOperations()

            // Update last sync date
            let userAccount = try await getUserAccount(context: context)
            if let account = userAccount {
                await MainActor.run {
                    account.lastSyncDate = Date()
                    account.updatedAt = Date()
                }
                try context.save()
            }

            // Wait for a moment to ensure sync operations have started
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            // Reset syncing flag
            await MainActor.run {
                isSyncing = false
            }
        } catch {
            // Reset syncing flag
            await MainActor.run {
                isSyncing = false
            }

            throw error
        }
    }

    /// Get the user account for the current user
    private func getUserAccount(context: ModelContext) async throws -> UserAccount? {
        guard let supabaseUserId = supabaseService.currentUser?.id else {
            return nil
        }

        // Convert UUID to String for comparison
        let supabaseUserIdString = supabaseUserId.uuidString

        let descriptor = FetchDescriptor<UserAccount>(
            predicate: #Predicate<UserAccount> { account in
                account.supabaseUserId == supabaseUserIdString
            }
        )

        return try context.fetch(descriptor).first
    }

    /// Sync errors
    enum SyncError: Error, LocalizedError {
        case notAuthenticated
        case premiumRequired
        case offline
        case noModelContext
        case missingBaby

        var errorDescription: String? {
            switch self {
            case .notAuthenticated:
                return "You must be signed in to sync your data."
            case .premiumRequired:
                return "Sync is a premium feature. Please upgrade to premium to use this feature."
            case .offline:
                return "You are offline. Please check your internet connection and try again."
            case .noModelContext:
                return "Internal error: No model context provided."
            case .missingBaby:
                return "Cannot sync entry: No baby profile associated with this entry."
            }
        }
    }
}

/// Model for a sync operation
struct SyncOperation: Codable {
    let type: String
    let babyId: String
    let data: [String: Any]
    let timestamp: Date

    enum CodingKeys: String, CodingKey {
        case type, babyId, data, timestamp
    }

    init(type: String, babyId: String, data: [String: Any]) {
        self.type = type
        self.babyId = babyId
        self.data = data
        self.timestamp = Date()
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(String.self, forKey: .type)
        babyId = try container.decode(String.self, forKey: .babyId)

        // Decode data dictionary
        if let dataString = try? container.decode(String.self, forKey: .data),
           let data = dataString.data(using: .utf8),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            self.data = dict
        } else {
            self.data = [:]
        }

        timestamp = try container.decode(Date.self, forKey: .timestamp)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(type, forKey: .type)
        try container.encode(babyId, forKey: .babyId)

        // Encode data dictionary
        if let jsonData = try? JSONSerialization.data(withJSONObject: data),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            try container.encode(jsonString, forKey: .data)
        } else {
            // If we can't encode the data, encode an empty JSON object
            try container.encode("{}", forKey: .data)
        }

        try container.encode(timestamp, forKey: .timestamp)
    }
}
