//
//  FeedingEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class FeedingEntry: Identifiable, EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus

    // Relationship
    @Relationship var baby: Baby?

    // Feeding-specific properties
    var type: FeedingType
    var duration: Int?  // In minutes, for breastfeeding
    var volume: Double? // In ml
    var content: BottleContent?
    var leftBreast: Bool?
    var rightBreast: Bool?
    var foodItem: String?
    var foodAmount: String?
    var reaction: String?

    enum FeedingType: String, Codable {
        case breastfeeding
        case bottleFeeding
        case solidFood
    }

    enum BottleContent: String, Codable {
        case formula
        case expressedBreastMilk
        case mixed
    }

    init(id: UUID = UUID(), timestamp: Date, type: FeedingType, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
        self.type = type
    }

    // Convenience initializers for different feeding types
    static func createBreastfeeding(timestamp: Date, duration: Int, leftBreast: Bool, rightBreast: Bool, notes: String? = nil, baby: Baby? = nil) -> FeedingEntry {
        let entry = FeedingEntry(timestamp: timestamp, type: .breastfeeding, notes: notes, baby: baby)
        entry.duration = duration
        entry.leftBreast = leftBreast
        entry.rightBreast = rightBreast
        return entry
    }

    static func createBottleFeeding(timestamp: Date, volume: Double, content: BottleContent, notes: String? = nil, baby: Baby? = nil) -> FeedingEntry {
        let entry = FeedingEntry(timestamp: timestamp, type: .bottleFeeding, notes: notes, baby: baby)
        entry.volume = volume
        entry.content = content
        return entry
    }

    static func createSolidFood(timestamp: Date, foodItem: String, foodAmount: String? = nil, reaction: String? = nil, notes: String? = nil, baby: Baby? = nil) -> FeedingEntry {
        let entry = FeedingEntry(timestamp: timestamp, type: .solidFood, notes: notes, baby: baby)
        entry.foodItem = foodItem
        entry.foodAmount = foodAmount
        entry.reaction = reaction
        return entry
    }
}
