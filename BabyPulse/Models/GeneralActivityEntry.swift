//
//  GeneralActivityEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class GeneralActivityEntry: EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus
    var baby: Baby? // Assuming Baby model exists and is linkable

    var activityType: GeneralActivityType
    var customActivityName: String? // Used if activityType is .other
    var durationMinutes: Int? // Optional duration in minutes

    init(
        id: UUID = UUID(),
        timestamp: Date = Date(),
        notes: String? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        syncStatus: SyncStatus = .notSynced,
        baby: Baby? = nil,
        activityType: GeneralActivityType,
        customActivityName: String? = nil,
        durationMinutes: Int? = nil
    ) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.syncStatus = syncStatus
        self.baby = baby
        self.activityType = activityType
        self.customActivityName = customActivityName
        self.durationMinutes = durationMinutes
        
        // Ensure customActivityName is only set if type is .other
        if activityType != .other {
            self.customActivityName = nil
        }
    }
} 