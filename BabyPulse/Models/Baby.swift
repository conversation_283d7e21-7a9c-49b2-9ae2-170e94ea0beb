//
//  Baby.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class Baby {
    var id: UUID
    var name: String
    var birthDate: Date
    var gender: Gender
    var weight: Double? // in kg
    var height: Double? // in cm
    var photoData: Data?
    var createdAt: Date
    var updatedAt: Date

    // Relationships to entries
    @Relationship(deleteRule: .cascade, inverse: \FeedingEntry.baby) var feedingEntries: [FeedingEntry]? = []
    @Relationship(deleteRule: .cascade, inverse: \DiaperEntry.baby) var diaperEntries: [DiaperEntry]? = []
    @Relationship(deleteRule: .cascade, inverse: \SleepEntry.baby) var sleepEntries: [SleepEntry]? = []
    @Relationship(deleteRule: .cascade, inverse: \GrowthEntry.baby) var growthEntries: [GrowthEntry]? = []
    @Relationship(deleteRule: .cascade, inverse: \HealthEntry.baby) var healthEntries: [HealthEntry]? = []

    // New relationship for chat threads - not inverse to avoid circular references
    @Relationship var chatThreads: [ChatThread]? = []

    init(name: String, birthDate: Date, gender: Gender, weight: Double? = nil, height: Double? = nil, photoData: Data? = nil) {
        self.id = UUID()
        self.name = name
        self.birthDate = birthDate
        self.gender = gender
        self.weight = weight
        self.height = height
        self.photoData = photoData
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    var ageInDays: Int {
        Calendar.current.dateComponents([.day], from: birthDate, to: Date()).day ?? 0
    }

    var ageDescription: String {
        let days = ageInDays
        if days < 7 {
            return "\(days) day\(days == 1 ? "" : "s")"
        } else if days < 30 {
            let weeks = days / 7
            return "\(weeks) week\(weeks == 1 ? "" : "s")"
        } else if days < 365 {
            let months = days / 30
            return "\(months) month\(months == 1 ? "" : "s")"
        } else {
            let years = days / 365
            let remainingMonths = (days % 365) / 30
            if remainingMonths == 0 {
                return "\(years) year\(years == 1 ? "" : "s")"
            } else {
                return "\(years) year\(years == 1 ? "" : "s") \(remainingMonths) month\(remainingMonths == 1 ? "" : "s")"
            }
        }
    }
}

enum Gender: String, Codable {
    case male = "male"
    case female = "female"
    case other = "other"
}
