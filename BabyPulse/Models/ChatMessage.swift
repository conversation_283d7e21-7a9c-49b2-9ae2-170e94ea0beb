import Foundation
import SwiftData

/// Chat message role
enum ChatRole: String, Codable {
    case user
    case assistant
    case system
}

/// Message state for UI rendering
enum MessageState: String, Codable {
    case complete
    case streaming
}

@Model
class ChatMessage {
    var id: UUID
    var content: String
    var role: String // Using String to store ChatRole.rawValue
    var timestamp: Date
    var tokenCount: Int?
    var createdAt: Date
    var updatedAt: Date
    var state: String // Using String to store MessageState.rawValue
    var feedback: Bool? // User feedback on message helpfulness

    // Relationship to thread
    @Relationship var thread: ChatThread?

    // For backward compatibility with existing data
    @Attribute(originalName: "isFromUser") var _isFromUser: Bool?

    init(id: UUID = UUID(), content: String, role: ChatRole, timestamp: Date = Date(), tokenCount: Int? = nil, thread: ChatThread? = nil, state: MessageState = .complete, feedback: Bool? = nil) {
        self.id = id
        self.content = content
        self.role = role.rawValue
        self.timestamp = timestamp
        self.tokenCount = tokenCount
        self.thread = thread
        self.state = state.rawValue
        self.feedback = feedback
        self.createdAt = Date()
        self.updatedAt = Date()
        self._isFromUser = role == .user ? true : false
    }

    // For backward compatibility
    convenience init(id: UUID = UUID(), content: String, isFromUser: Bool, timestamp: Date = Date()) {
        self.init(
            id: id,
            content: content,
            role: isFromUser ? .user : .assistant,
            timestamp: timestamp,
            state: .complete
        )
        self._isFromUser = isFromUser
    }

    // Computed property for type-safe access
    var chatRole: ChatRole {
        get {
            ChatRole(rawValue: role) ?? .user
        }
        set {
            role = newValue.rawValue
        }
    }
    
    // Computed property for message state
    var messageState: MessageState {
        get {
            MessageState(rawValue: state) ?? .complete
        }
        set {
            state = newValue.rawValue
        }
    }

    // Backward compatibility
    var isFromUser: Bool {
        // Use the stored value if available, otherwise compute from role
        return _isFromUser ?? (chatRole == .user)
    }

    // Convenience initializers for backward compatibility
    static func createUserMessage(content: String, timestamp: Date = Date(), thread: ChatThread? = nil) -> ChatMessage {
        return ChatMessage(content: content, role: .user, timestamp: timestamp, thread: thread)
    }

    static func createAssistantMessage(content: String, timestamp: Date = Date(), tokenCount: Int? = nil, thread: ChatThread? = nil, state: MessageState = .complete, feedback: Bool? = nil) -> ChatMessage {
        return ChatMessage(content: content, role: .assistant, timestamp: timestamp, tokenCount: tokenCount, thread: thread, state: state, feedback: feedback)
    }
    
    static func createStreamingAssistantMessage(initialContent: String = "", timestamp: Date = Date(), thread: ChatThread? = nil) -> ChatMessage {
        return ChatMessage(content: initialContent, role: .assistant, timestamp: timestamp, thread: thread, state: .streaming)
    }

    static func createSystemMessage(content: String, timestamp: Date = Date(), thread: ChatThread? = nil) -> ChatMessage {
        return ChatMessage(content: content, role: .system, timestamp: timestamp, thread: thread)
    }
}
