//
//  BaseEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

// Instead of using inheritance with @Model, we'll use a protocol
// to define common properties and methods for all entry types
protocol EntryProtocol {
    var id: UUID { get set }
    var timestamp: Date { get set }
    var notes: String? { get set }
    var createdAt: Date { get set }
    var updatedAt: Date { get set }
    var syncStatus: SyncStatus { get set }
    var baby: Baby? { get set }
}

enum SyncStatus: String, Codable {
    case notSynced
    case syncing
    case synced
    case syncFailed
}
