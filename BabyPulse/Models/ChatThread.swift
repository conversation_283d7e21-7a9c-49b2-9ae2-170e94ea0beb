import Foundation
import SwiftData
import OSLog

/// Chat thread model
@Model
final class ChatThread {
    var id: UUID
    var createdAt: Date
    var updatedAt: Date

    // Relationship to baby
    @Relationship var baby: Baby?

    // Relationship to messages
    @Relationship(deleteRule: .cascade, inverse: \ChatMessage.thread)
    var messages: [ChatMessage]? = []

    init(id: UUID = UUID(), baby: Baby? = nil) {
        self.id = id
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    /// Creates a new thread with a welcome message for the given baby
    /// - Parameters:
    ///   - baby: The baby to create the thread for
    ///   - modelContext: The model context to insert the thread and message into
    /// - Returns: The created thread, or nil if there was an error
    static func createThreadWithWelcomeMessage(for baby: Baby, in modelContext: ModelContext) -> ChatThread? {
        let logger = Logger(subsystem: "com.babypulse", category: "ChatThread")
        logger.debug("Creating new thread for baby: \(baby.name)")

        // Create a new thread
        let newThread = ChatThread(baby: baby)
        modelContext.insert(newThread)

        // Add a welcome message
        let welcomeMessage = ChatMessage.createAssistantMessage(
            content: "Hello! I'm your BabyPulse assistant. I can help answer questions about \(baby.name)'s patterns and development. How can I help you today?",
            thread: newThread
        )
        modelContext.insert(welcomeMessage)

        do {
            try modelContext.save()
            logger.debug("Successfully created new thread and welcome message")
            return newThread
        } catch {
            logger.error("Error creating new thread: \(error.localizedDescription)")
            return nil
        }
    }
}
