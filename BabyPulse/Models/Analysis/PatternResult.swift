//
//  PatternResult.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents the result of pattern detection analysis
struct PatternResult {
    /// The type of pattern detected
    let type: String
    
    /// The confidence level (0-100)
    let confidence: Int
    
    /// Human-readable description of the pattern
    let description: String
    
    /// Metrics associated with the pattern
    let metrics: [String: Any]
    
    /// Whether this pattern needs attention
    var needsAttention: Bool {
        // Default implementation - can be overridden in specific pattern types
        return confidence >= 75
    }
    
    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        var variables: [String: String] = [
            "pattern_type": type,
            "pattern_description": description,
            "confidence": "\(confidence)%"
        ]
        
        // Add metrics as strings
        for (key, value) in metrics {
            if let doubleValue = value as? Double {
                variables[key] = String(format: "%.1f", doubleValue)
            } else if let intValue = value as? Int {
                variables[key] = "\(intValue)"
            } else if let stringValue = value as? String {
                variables[key] = stringValue
            } else if let boolValue = value as? Bool {
                variables[key] = boolValue ? "yes" : "no"
            }
        }
        
        return variables
    }
}
