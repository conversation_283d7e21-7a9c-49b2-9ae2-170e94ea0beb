//
//  DevelopmentalPhase.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents developmental phases like growth spurts or sleep regressions
struct DevelopmentalPhase {
    /// The name of the phase
    let name: String
    
    /// The typical age ranges in weeks when this phase occurs
    let typicalAgeWeeks: [ClosedRange<Int>]
    
    /// Description of how this phase impacts sleep
    let sleepImpact: String
    
    /// Description of how this phase impacts feeding
    let feedingImpact: String
    
    /// Common behavioral signs of this phase
    let behavioralSigns: [String]
    
    /// Typical duration of this phase in days
    let duration: ClosedRange<Int>
    
    /// Check if a phase is potentially active for a baby
    /// - Parameters:
    ///   - ageInWeeks: The baby's age in weeks
    ///   - buffer: Buffer in weeks to consider before and after the typical range
    /// - Returns: Whether the phase is potentially active
    func isPotentiallyActive(for ageInWeeks: Int, buffer: Int = 2) -> Bool {
        return typicalAgeWeeks.contains { range in
            (range.lowerBound - buffer...range.upperBound + buffer).contains(ageInWeeks)
        }
    }
    
    /// Get the most relevant age range for a baby
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: The most relevant age range, or nil if none apply
    func mostRelevantAgeRange(for ageInWeeks: Int) -> ClosedRange<Int>? {
        // First check if any range contains the exact age
        if let exactMatch = typicalAgeWeeks.first(where: { $0.contains(ageInWeeks) }) {
            return exactMatch
        }
        
        // Then check for the closest upcoming range
        let upcomingRanges = typicalAgeWeeks.filter { $0.lowerBound > ageInWeeks }
        if let nextRange = upcomingRanges.min(by: { $0.lowerBound < $1.lowerBound }) {
            return nextRange
        }
        
        // Finally, return the most recent past range
        let pastRanges = typicalAgeWeeks.filter { $0.upperBound < ageInWeeks }
        return pastRanges.max(by: { $0.upperBound < $1.upperBound })
    }
}
