//
//  DevelopmentalContext.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents developmental context for analysis results
struct DevelopmentalContext {
    /// The current developmental stage description
    let currentStage: String
    
    /// The relevant developmental norm, if any
    let relevantNorm: DevelopmentalNorm?
    
    /// The upcoming milestone, if any
    let upcomingMilestone: DevelopmentalMilestone?
    
    /// Potential developmental phases that might be occurring
    let potentialPhases: [DevelopmentalPhase]
    
    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        var variables: [String: String] = [
            "developmental_stage": currentStage
        ]
        
        if let norm = relevantNorm {
            variables["age_appropriate_range"] = norm.description
            variables["metric_unit"] = norm.unit
            variables["normal_range_lower"] = String(format: "%.1f", norm.normalRange.lowerBound)
            variables["normal_range_upper"] = String(format: "%.1f", norm.normalRange.upperBound)
        }
        
        if let milestone = upcomingMilestone {
            variables["upcoming_milestone"] = milestone.name
            if let timeUntil = milestone.estimatedTimeUntil(ageInWeeks: calculateAgeInWeeks()) {
                variables["milestone_timeframe"] = "in approximately \(timeUntil) weeks"
            }
        }
        
        if !potentialPhases.isEmpty {
            let phaseNames = potentialPhases.map { $0.name }.joined(separator: ", ")
            variables["potential_phases"] = phaseNames
            
            if let primaryPhase = potentialPhases.first {
                variables["primary_phase"] = primaryPhase.name
                variables["sleep_impact"] = primaryPhase.sleepImpact
                variables["feeding_impact"] = primaryPhase.feedingImpact
                variables["behavioral_signs"] = primaryPhase.behavioralSigns.joined(separator: ", ")
                variables["typical_duration"] = "\(primaryPhase.duration.lowerBound)-\(primaryPhase.duration.upperBound) days"
            }
        }
        
        return variables
    }
    
    // Helper method to calculate age in weeks
    private func calculateAgeInWeeks() -> Int {
        // This is a placeholder - in a real implementation, this would use the baby's birthdate
        return 0
    }
}
