//
//  CyclicalPattern.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents a cyclical pattern detected in baby data
struct CyclicalPattern {
    /// The category this pattern applies to
    let category: Insight.InsightCategory
    
    /// The specific metric showing cyclical behavior
    let metric: String
    
    /// The period of the cycle in hours
    let periodHours: Double
    
    /// The confidence level (0-100)
    let confidence: Double
    
    /// Human-readable description of the pattern
    let description: String
    
    /// The amplitude of the cycle (difference between peak and trough)
    let amplitude: Double
    
    /// The phase of the cycle (time offset from midnight)
    let phaseHours: Double
    
    /// The start time of the next predicted peak
    var nextPeakTime: Date {
        let now = Date()
        let calendar = Calendar.current
        
        // Get the start of the current day
        let startOfDay = calendar.startOfDay(for: now)
        
        // Calculate the time since the last midnight
        let hoursSinceMidnight = now.timeIntervalSince(startOfDay) / 3600
        
        // Calculate hours until next peak
        let hoursUntilNextPeak = (phaseHours - hoursSinceMidnight).truncatingRemainder(dividingBy: periodHours)
        let adjustedHoursUntilNextPeak = hoursUntilNextPeak < 0 ? hoursUntilNextPeak + periodHours : hoursUntilNextPeak
        
        // Calculate the next peak time
        return now.addingTimeInterval(adjustedHoursUntilNextPeak * 3600)
    }
    
    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        
        return [
            "cycle_category": category.description,
            "cycle_metric": metric,
            "cycle_period": String(format: "%.1f", periodHours),
            "cycle_confidence": String(format: "%.0f%%", confidence),
            "cycle_description": description,
            "cycle_amplitude": String(format: "%.1f", amplitude),
            "cycle_next_peak": formatter.string(from: nextPeakTime),
            "cycle_unit": getUnitText(for: metric)
        ]
    }
    
    /// Whether this pattern needs attention
    var needsAttention: Bool {
        // Cyclical patterns rarely need immediate attention
        // but high confidence patterns with unusual periods might be worth noting
        
        let isUnusualPeriod = isUnusualCyclePeriod(periodHours, for: category, metric: metric)
        return confidence >= 85 && isUnusualPeriod
    }
    
    // MARK: - Private Methods
    
    /// Get the unit text for a metric
    /// - Parameter metric: The metric name
    /// - Returns: The unit text
    private func getUnitText(for metric: String) -> String {
        switch (category, metric) {
        case (.sleep, "total_duration"):
            return "minutes"
        case (.sleep, "longest_stretch"):
            return "minutes"
        case (.feeding, "volume"):
            return "ml"
        case (.feeding, "duration"):
            return "minutes"
        case (.diaper, "frequency"):
            return "changes"
        case (.growth, "weight"):
            return "kg"
        case (.growth, "height"):
            return "cm"
        case (.growth, "head_circumference"):
            return "cm"
        case (.health, "temperature"):
            return "°C"
        default:
            return "units"
        }
    }
    
    /// Check if a cycle period is unusual for a given category and metric
    /// - Parameters:
    ///   - period: The period in hours
    ///   - category: The category
    ///   - metric: The metric
    /// - Returns: Whether the period is unusual
    private func isUnusualCyclePeriod(_ period: Double, for category: Insight.InsightCategory, metric: String) -> Bool {
        switch (category, metric) {
        case (.sleep, _):
            // Sleep cycles should be around 24 hours (circadian rhythm)
            return abs(period - 24) > 4 && period < 20
        case (.feeding, _):
            // Feeding cycles are typically 2-4 hours
            return period < 1.5 || period > 5
        case (.diaper, _):
            // Diaper cycles often follow feeding cycles
            return period < 1.5 || period > 6
        case (.health, "temperature"):
            // Body temperature has a natural 24-hour cycle
            return abs(period - 24) > 4
        default:
            return false
        }
    }
}
