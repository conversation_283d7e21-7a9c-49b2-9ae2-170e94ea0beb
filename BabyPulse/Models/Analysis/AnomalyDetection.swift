//
//  AnomalyDetection.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents an anomaly detected in baby data
struct AnomalyDetection {
    /// The category this anomaly applies to
    let category: Insight.InsightCategory
    
    /// The specific metric showing anomalous behavior
    let metric: String
    
    /// The expected value based on historical data
    let expectedValue: Double
    
    /// The actual observed value
    let actualValue: Double
    
    /// How many standard deviations from the norm
    let deviationSigma: Double
    
    /// Whether this anomaly is statistically significant
    let isSignificant: Bool
    
    /// Human-readable description of the anomaly
    var description: String {
        let direction = actualValue > expectedValue ? "higher" : "lower"
        let percentDifference = abs((actualValue - expectedValue) / expectedValue) * 100
        let unitText = getUnitText(for: metric)
        
        return "\(String(format: "%.1f", actualValue)) \(unitText) is \(String(format: "%.0f%%", percentDifference)) \(direction) than expected (\(String(format: "%.1f", expectedValue)) \(unitText))"
    }
    
    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        let percentDifference = abs((actualValue - expectedValue) / expectedValue) * 100
        let direction = actualValue > expectedValue ? "higher" : "lower"
        
        return [
            "anomaly_category": category.description,
            "anomaly_metric": metric,
            "anomaly_expected": String(format: "%.1f", expectedValue),
            "anomaly_actual": String(format: "%.1f", actualValue),
            "anomaly_deviation": String(format: "%.1f", deviationSigma),
            "anomaly_percent_difference": String(format: "%.0f%%", percentDifference),
            "anomaly_direction": direction,
            "anomaly_unit": getUnitText(for: metric),
            "anomaly_description": description,
            "anomaly_is_significant": isSignificant ? "yes" : "no"
        ]
    }
    
    /// Whether this anomaly needs attention
    var needsAttention: Bool {
        // Anomalies that need attention:
        // 1. Significant deviations (>2 sigma)
        // 2. Specific metrics that are concerning when they deviate
        
        if deviationSigma >= 2.5 {
            return true
        }
        
        switch (category, metric) {
        case (.health, "temperature"):
            // Any significant deviation in temperature needs attention
            return deviationSigma >= 1.5 && actualValue > expectedValue
        case (.feeding, "volume"):
            // Significant decrease in feeding volume needs attention
            return deviationSigma >= 1.5 && actualValue < expectedValue
        case (.sleep, "total_duration"):
            // Significant decrease in sleep duration needs attention
            return deviationSigma >= 1.5 && actualValue < expectedValue
        case (.diaper, "frequency"):
            // Significant decrease in diaper frequency needs attention
            return deviationSigma >= 1.5 && actualValue < expectedValue
        default:
            return deviationSigma >= 2.0
        }
    }
    
    // MARK: - Private Methods
    
    /// Get the unit text for a metric
    /// - Parameter metric: The metric name
    /// - Returns: The unit text
    private func getUnitText(for metric: String) -> String {
        switch (category, metric) {
        case (.sleep, "total_duration"):
            return "minutes"
        case (.sleep, "longest_stretch"):
            return "minutes"
        case (.feeding, "volume"):
            return "ml"
        case (.feeding, "duration"):
            return "minutes"
        case (.diaper, "frequency"):
            return "changes"
        case (.growth, "weight"):
            return "kg"
        case (.growth, "height"):
            return "cm"
        case (.growth, "head_circumference"):
            return "cm"
        case (.health, "temperature"):
            return "°C"
        default:
            return "units"
        }
    }
}
