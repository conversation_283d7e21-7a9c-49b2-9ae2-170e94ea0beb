//
//  DevelopmentalNorm.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents age-appropriate norms for baby development
struct DevelopmentalNorm {
    /// The category this norm applies to
    let category: Insight.InsightCategory
    
    /// The specific metric being measured
    let metric: String
    
    /// The age range in weeks this norm applies to
    let ageRangeWeeks: ClosedRange<Int>
    
    /// The normal range for the metric
    let normalRange: ClosedRange<Double>
    
    /// The unit of measurement
    let unit: String
    
    /// Human-readable description of the norm
    let description: String
    
    /// Check if a baby's age falls within this norm's range
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Whether the baby's age is within this norm's range
    func isApplicable(for ageInWeeks: Int) -> Bool {
        return ageRangeWeeks.contains(ageInWeeks)
    }
    
    /// Check if a value is within the normal range
    /// - Parameter value: The value to check
    /// - Returns: Whether the value is within the normal range
    func isWithinRange(_ value: Double) -> Bool {
        return normalRange.contains(value)
    }
    
    /// Calculate how far a value deviates from the normal range
    /// - Parameter value: The value to check
    /// - Returns: The percentage deviation from the normal range (0 if within range)
    func calculateDeviation(for value: Double) -> Double {
        if isWithinRange(value) {
            return 0
        }
        
        let midpoint = (normalRange.upperBound + normalRange.lowerBound) / 2
        let rangeSize = normalRange.upperBound - normalRange.lowerBound
        
        return abs(value - midpoint) / (rangeSize / 2) * 100
    }
}
