//
//  TrendAnalysis.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Direction of a trend
enum TrendDirection {
    case increasing
    case decreasing
    case stable
    case fluctuating
    
    var description: String {
        switch self {
        case .increasing: return "increasing"
        case .decreasing: return "decreasing"
        case .stable: return "stable"
        case .fluctuating: return "fluctuating"
        }
    }
}

/// Represents the result of trend analysis
struct TrendAnalysis {
    /// The category this trend applies to
    let category: Insight.InsightCategory
    
    /// The specific metric being analyzed
    let metric: String
    
    /// The direction of the trend
    let direction: TrendDirection
    
    /// The rate of change (units per day)
    let rateOfChange: Double
    
    /// How many days this trend has been sustained
    let sustainedDays: Int
    
    /// The confidence level (0-100)
    let confidence: Double
    
    /// Human-readable description of the trend
    var description: String {
        let directionText = direction.description
        let rateText = String(format: "%.2f", abs(rateOfChange))
        let unitText = getUnitText(for: metric)
        let timeText = sustainedDays > 1 ? "\(sustainedDays) days" : "24 hours"
        
        return "\(directionText.capitalized) trend of \(rateText) \(unitText) per day over the past \(timeText)"
    }
    
    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        return [
            "trend_category": category.description,
            "trend_metric": metric,
            "trend_direction": direction.description,
            "trend_rate": String(format: "%.2f", abs(rateOfChange)),
            "trend_unit": getUnitText(for: metric),
            "trend_days": "\(sustainedDays)",
            "trend_confidence": String(format: "%.0f%%", confidence),
            "trend_description": description
        ]
    }
    
    /// Whether this trend needs attention
    var needsAttention: Bool {
        // Trends that need attention:
        // 1. Decreasing trends in positive metrics (sleep duration, feeding volume)
        // 2. Increasing trends in concerning metrics (temperature, diaper frequency)
        // 3. Any trend with high confidence and significant rate of change
        
        let significantChange = abs(rateOfChange) > getSignificanceThreshold(for: metric)
        let highConfidence = confidence >= 75
        
        switch (category, metric, direction) {
        case (.sleep, "total_duration", .decreasing):
            return significantChange && highConfidence
        case (.feeding, "volume", .decreasing):
            return significantChange && highConfidence
        case (.health, "temperature", .increasing):
            return significantChange && highConfidence
        case (.diaper, "frequency", .decreasing):
            return significantChange && highConfidence
        default:
            return false
        }
    }
    
    // MARK: - Private Methods
    
    /// Get the unit text for a metric
    /// - Parameter metric: The metric name
    /// - Returns: The unit text
    private func getUnitText(for metric: String) -> String {
        switch (category, metric) {
        case (.sleep, "total_duration"):
            return "minutes"
        case (.sleep, "longest_stretch"):
            return "minutes"
        case (.feeding, "volume"):
            return "ml"
        case (.feeding, "duration"):
            return "minutes"
        case (.diaper, "frequency"):
            return "changes"
        case (.growth, "weight"):
            return "kg"
        case (.growth, "height"):
            return "cm"
        case (.growth, "head_circumference"):
            return "cm"
        case (.health, "temperature"):
            return "°C"
        default:
            return "units"
        }
    }
    
    /// Get the significance threshold for a metric
    /// - Parameter metric: The metric name
    /// - Returns: The threshold value
    private func getSignificanceThreshold(for metric: String) -> Double {
        switch (category, metric) {
        case (.sleep, "total_duration"):
            return 30.0 // 30 minutes per day is significant
        case (.sleep, "longest_stretch"):
            return 15.0 // 15 minutes per day is significant
        case (.feeding, "volume"):
            return 20.0 // 20 ml per day is significant
        case (.feeding, "duration"):
            return 5.0 // 5 minutes per day is significant
        case (.diaper, "frequency"):
            return 2.0 // 2 changes per day is significant
        case (.growth, "weight"):
            return 0.03 // 30g per day is significant
        case (.growth, "height"):
            return 0.2 // 2mm per day is significant
        case (.growth, "head_circumference"):
            return 0.1 // 1mm per day is significant
        case (.health, "temperature"):
            return 0.3 // 0.3°C per day is significant
        default:
            return 0.1 // Default threshold
        }
    }
}
