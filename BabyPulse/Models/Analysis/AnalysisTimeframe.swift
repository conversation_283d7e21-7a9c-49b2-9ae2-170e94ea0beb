//
//  AnalysisTimeframe.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Timeframes for multi-timeframe analysis
enum AnalysisTimeframe {
    case intraday
    case shortTerm
    case weekly
    case biweekly
    case monthly
    
    var description: String {
        switch self {
        case .intraday: return "throughout the day"
        case .shortTerm: return "over the past few days"
        case .weekly: return "this week"
        case .biweekly: return "over the past two weeks"
        case .monthly: return "this month"
        }
    }
    
    var startDate: Date {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .intraday: return calendar.startOfDay(for: now)
        case .shortTerm: return calendar.date(byAdding: .day, value: -3, to: now)!
        case .weekly: return calendar.date(byAdding: .day, value: -7, to: now)!
        case .biweekly: return calendar.date(byAdding: .day, value: -14, to: now)!
        case .monthly: return calendar.date(byAdding: .month, value: -1, to: now)!
        }
    }
    
    /// Convert to TimeRange for compatibility with existing code
    var asTimeRange: TimeRange {
        switch self {
        case .intraday: return .custom(DateInterval(start: startDate, end: Date()))
        case .shortTerm: return .custom(DateInterval(start: startDate, end: Date()))
        case .weekly: return .last7Days
        case .biweekly: return .custom(DateInterval(start: startDate, end: Date()))
        case .monthly: return .last30Days
        }
    }
}
