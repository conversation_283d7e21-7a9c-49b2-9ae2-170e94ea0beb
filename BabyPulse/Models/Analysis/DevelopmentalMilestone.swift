//
//  DevelopmentalMilestone.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents developmental milestones that babies typically reach
struct DevelopmentalMilestone {
    /// The name of the milestone
    let name: String
    
    /// The typical age range in weeks when this milestone is reached
    let typicalAgeWeeks: ClosedRange<Int>
    
    /// Categories related to this milestone
    let relatedCategories: [Insight.InsightCategory]
    
    /// Common patterns associated with this milestone
    let commonPatterns: [String: String] // Pattern name to description
    
    /// Check if a milestone is upcoming for a baby
    /// - Parameters:
    ///   - ageInWeeks: The baby's age in weeks
    ///   - lookAheadWeeks: How many weeks ahead to consider (default: 4)
    /// - Returns: Whether the milestone is upcoming
    func isUpcoming(for ageInWeeks: Int, lookAheadWeeks: Int = 4) -> Bool {
        return ageInWeeks < typicalAgeWeeks.lowerBound && 
               typicalAgeWeeks.lowerBound - ageInWeeks <= lookAheadWeeks
    }
    
    /// Check if a milestone is current for a baby
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Whether the milestone is current
    func isCurrent(for ageInWeeks: Int) -> Bool {
        return typicalAgeWeeks.contains(ageInWeeks)
    }
    
    /// Get the estimated time until this milestone
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Estimated weeks until milestone, or nil if already passed
    func estimatedTimeUntil(ageInWeeks: Int) -> Int? {
        if ageInWeeks >= typicalAgeWeeks.upperBound {
            return nil
        }
        
        return max(0, typicalAgeWeeks.lowerBound - ageInWeeks)
    }
}
