//
//  PromptTemplate.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Model for prompt templates used for insight generation
struct PromptTemplate: Codable {
    /// The name of the template
    let name: String

    /// The template text with placeholders for variables
    let template: String

    /// The list of dynamic variables that can be injected into the template
    let dynamicVariables: [String]

    /// Thresholds for determining when to generate insights
    let contextualThresholds: [String: String]

    /// Actionable recommendations that can be included in the insight
    let actionableRecommendations: [String]

    /// Priority scoring for determining urgency and importance
    let priorityScoring: [String: String]

    // Computed properties for easier access
    var severity: String { priorityScoring["severity"] ?? "medium" }
    var urgency: String { priorityScoring["urgency"] ?? "medium" }
    var impact: String { priorityScoring["impact"] ?? "medium" }

    // Computed property for deviation percentage (used in confidence calculation)
    var deviationPercentage: Double {
        if let deviationStr = priorityScoring["deviation_percentage"],
           let deviation = Double(deviationStr) {
            return deviation
        }
        return 0.0
    }
}
