//
//  EntryType.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

// EntryType enum for categorizing different types of entries
enum EntryType: String, Codable, CaseIterable {
    case feeding
    case diaper
    case sleep
    case growth
    case health
    case generalActivity
    case other
    
    var description: String {
        switch self {
        case .feeding:
            return "Feeding"
        case .diaper:
            return "Diaper"
        case .sleep:
            return "Sleep"
        case .growth:
            return "Growth"
        case .health:
            return "Health"
        case .generalActivity:
            return "General Activity"
        case .other:
            return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .feeding:
            return "bottle"
        case .diaper:
            return "arrow.triangle.2.circlepath"
        case .sleep:
            return "moon"
        case .growth:
            return "chart.line.uptrend.xyaxis"
        case .health:
            return "heart"
        case .generalActivity:
            return "figure.walk"
        case .other:
            return "star"
        }
    }
    
    var color: Color {
        switch self {
        case .feeding:
            return .blue
        case .diaper:
            return .yellow
        case .sleep:
            return .purple
        case .growth:
            return .green
        case .health:
            return .red
        case .generalActivity:
            return BabyPulseColors.generalActivity
        case .other:
            return Color(hex: "4CE1B6") // BabyPulseColors.primary
        }
    }
}

// Extension to convert LogCategory to EntryType
extension LogCategory {
    var toEntryType: EntryType {
        switch self {
        case .feeding:
            return .feeding
        case .diaper:
            return .diaper
        case .sleep:
            return .sleep
        case .growth:
            return .growth
        case .health:
            return .health
        case .general:
            return .generalActivity
        }
    }
}
