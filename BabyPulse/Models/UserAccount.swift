import Foundation
import SwiftData

@Model
final class UserAccount {
    var id: UUID
    var email: String
    var isEmailVerified: Bool
    var supabaseUserId: String?
    var subscriptionStatus: SubscriptionStatus
    var subscriptionExpiryDate: Date?
    var lastSyncDate: Date?
    var syncEnabled: Bool = true
    var createdAt: Date
    var updatedAt: Date

    enum SubscriptionStatus: String, Codable {
        case free
        case trial
        case premium
        case expired
    }

    var createdAtFormatted: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: createdAt)
    }

    init(id: UUID = UUID(),
         email: String,
         isEmailVerified: Bool = false,
         supabaseUserId: String? = nil,
         subscriptionStatus: SubscriptionStatus = .free,
         subscriptionExpiryDate: Date? = nil,
         lastSyncDate: Date? = nil,
         syncEnabled: Bool = true) {
        self.id = id
        self.email = email
        self.isEmailVerified = isEmailVerified
        self.supabaseUserId = supabaseUserId
        self.subscriptionStatus = subscriptionStatus
        self.subscriptionExpiryDate = subscriptionExpiryDate
        self.lastSyncDate = lastSyncDate
        self.syncEnabled = syncEnabled
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}
