//
//  HealthEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class HealthEntry: Identifiable, EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus

    // Relationship
    @Relationship var baby: Baby?

    // Health-specific properties
    var type: HealthEntryType
    var temperature: Double?
    var temperatureUnit: TemperatureUnit?
    var medicationName: String?
    var medicationDosage: String?
    var symptomsJSON: String? // Stored as JSON string array of Symptom.rawValue
    var vaccineName: String?
    var appointmentReason: String?
    var appointmentProvider: String?

    enum HealthEntryType: String, Codable, CaseIterable {
        case temperature
        case medication
        case symptom
        case vaccination
        case appointment

        var description: String {
            switch self {
            case .temperature:
                return "Temperature"
            case .medication:
                return "Medication"
            case .symptom:
                return "Symptom"
            case .vaccination:
                return "Vaccination"
            case .appointment:
                return "Appointment"
            }
        }
    }

    enum TemperatureUnit: String, Codable, CaseIterable {
        case celsius
        case fahrenheit

        var description: String {
            switch self {
            case .celsius:
                return "°C"
            case .fahrenheit:
                return "°F"
            }
        }

        var symbol: String {
            switch self {
            case .celsius:
                return "°C"
            case .fahrenheit:
                return "°F"
            }
        }
    }

    enum Symptom: String, Codable, CaseIterable {
        case rash
        case cough
        case congestion
        case fever
        case vomiting
        case diarrhea
        case constipation
        case irritability
        case lethargy
        case allergy
        case skinIrritation
        case breathingDifficulty
        case feedingIssue
        case other

        var description: String {
            switch self {
            case .rash:
                return "Rash"
            case .cough:
                return "Cough"
            case .congestion:
                return "Congestion"
            case .fever:
                return "Fever"
            case .vomiting:
                return "Vomiting"
            case .diarrhea:
                return "Diarrhea"
            case .constipation:
                return "Constipation"
            case .irritability:
                return "Irritability"
            case .lethargy:
                return "Lethargy"
            case .allergy:
                return "Allergy"
            case .skinIrritation:
                return "Skin Irritation"
            case .breathingDifficulty:
                return "Breathing Difficulty"
            case .feedingIssue:
                return "Feeding Issue"
            case .other:
                return "Other"
            }
        }
    }

    init(id: UUID = UUID(), timestamp: Date, type: HealthEntryType, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
        self.type = type
    }

    // Helper methods for symptoms
    func getSymptoms() -> [Symptom] {
        guard let symptomsJSON = symptomsJSON,
              let data = symptomsJSON.data(using: .utf8) else { return [] }

        do {
            let symptomStrings = try JSONDecoder().decode([String].self, from: data)
            return symptomStrings.compactMap { Symptom(rawValue: $0) }
        } catch {
            print("Error decoding symptoms: \(error)")
            return []
        }
    }

    func setSymptoms(_ symptomList: [Symptom]) {
        let symptomStrings = symptomList.map { $0.rawValue }
        do {
            let data = try JSONEncoder().encode(symptomStrings)
            self.symptomsJSON = String(data: data, encoding: .utf8)
        } catch {
            print("Error encoding symptoms: \(error)")
            self.symptomsJSON = "[]"
        }
    }

    // Format temperature with unit
    func formattedTemperature() -> String? {
        guard let temperature = temperature, let unit = temperatureUnit else { return nil }
        return String(format: "%.1f%@", temperature, unit.symbol)
    }
}
