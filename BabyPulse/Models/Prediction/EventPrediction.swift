//
//  EventPrediction.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents a predicted event
struct EventPrediction {
    /// The category this event belongs to
    let category: Insight.InsightCategory
    
    /// The specific type of event
    let eventType: String
    
    /// The predicted timeframe for the event
    let timeframe: DateInterval
    
    /// The probability of the event occurring (0.0-1.0)
    let probability: Double
    
    /// The evidence supporting this prediction
    let evidence: [String]
    
    /// The model or method used to make the prediction
    let predictionMethod: String
    
    /// Human-readable description of the prediction
    let description: String
    
    /// Whether this prediction needs attention
    var needsAttention: Bool {
        // Events with high probability that are imminent need attention
        return probability >= 0.7 && timeUntilEvent <= 3600 // Within 1 hour
    }
    
    /// Time until the event (in seconds)
    var timeUntilEvent: TimeInterval {
        return max(0, timeframe.start.timeIntervalSince(Date()))
    }
    
    /// Whether the event is currently occurring
    var isCurrentlyOccurring: Bool {
        let now = Date()
        return timeframe.start <= now && timeframe.end >= now
    }
    
    /// Whether the event has already occurred
    var hasOccurred: Bool {
        return Date() > timeframe.end
    }
    
    /// Human-readable timeframe description
    var timeframeDescription: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        
        return "\(formatter.string(from: timeframe.start)) to \(formatter.string(from: timeframe.end))"
    }
    
    /// Human-readable time until event
    var timeUntilEventDescription: String {
        if hasOccurred {
            return "Already occurred"
        } else if isCurrentlyOccurring {
            return "Currently occurring"
        } else {
            let seconds = timeUntilEvent
            
            if seconds < 60 {
                return "In less than a minute"
            } else if seconds < 3600 {
                let minutes = Int(seconds / 60)
                return "In \(minutes) minute\(minutes == 1 ? "" : "s")"
            } else if seconds < 86400 {
                let hours = Int(seconds / 3600)
                return "In \(hours) hour\(hours == 1 ? "" : "s")"
            } else {
                let days = Int(seconds / 86400)
                return "In \(days) day\(days == 1 ? "" : "s")"
            }
        }
    }
    
    /// Convert to a standard prediction
    /// - Returns: Prediction object
    func toPrediction() -> Prediction {
        // Calculate confidence as a percentage based on probability
        let confidence = Int(probability * 100)
        
        return Prediction(
            type: "event_\(eventType)",
            description: description,
            confidence: confidence,
            timeframe: timeframe,
            predictedValue: nil,
            confidenceInterval: nil,
            forecastModel: predictionMethod,
            errorMetrics: nil,
            eventType: eventType,
            eventProbability: probability,
            eventEvidence: evidence
        )
    }
}
