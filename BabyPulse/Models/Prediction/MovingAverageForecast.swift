//
//  MovingAverageForecast.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Forecast model using moving average technique
class MovingAverageForecast: BaseForecastModel {
    /// The window size for the moving average
    var windowSize: Int

    /// Historical errors for confidence interval calculation
    private var historicalErrors: [Double] = []

    init(
        category: Insight.InsightCategory,
        metric: String,
        windowSize: Int = 3,
        minimumDataPoints: Int = 5
    ) {
        self.windowSize = windowSize
        super.init(
            category: category,
            metric: metric,
            minimumDataPoints: minimumDataPoints
        )
    }

    override func forecast(
        timestamps: [Date],
        values: [Double],
        horizon: Int,
        interval: TimeInterval
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: "MovingAverage",
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Calculate moving average
        let movingAverage = calculateMovingAverage(sortedValues)

        // Generate forecast timestamps
        var forecastTimestamps: [Date] = []
        let lastTimestamp = sortedTimestamps.last!

        for i in 0..<horizon {
            let forecastTimestamp = lastTimestamp.addingTimeInterval(Double(i + 1) * interval)
            forecastTimestamps.append(forecastTimestamp)
        }

        // Generate forecast values
        var forecastValues: [Double] = []
        var lowerBounds: [Double] = []
        var upperBounds: [Double] = []

        for _ in 0..<horizon {
            // Use the last moving average value for all forecast points
            let forecastValue = movingAverage.last!
            forecastValues.append(forecastValue)

            // Calculate confidence interval
            let (lower, upper) = calculateConfidenceInterval(
                predictedValue: forecastValue,
                historicalErrors: historicalErrors
            )

            lowerBounds.append(lower)
            upperBounds.append(upper)
        }

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedValues, movingAverage)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: forecastTimestamps,
            forecastValues: forecastValues,
            lowerBounds: lowerBounds,
            upperBounds: upperBounds,
            confidenceLevel: 0.95,
            modelName: "MovingAverage",
            errorMetrics: errorMetrics
        )
    }

    override func forecastValue(
        timestamps: [Date],
        values: [Double],
        targetTime: Date
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: "MovingAverage",
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Calculate moving average
        let movingAverage = calculateMovingAverage(sortedValues)

        // Use the last moving average value for the forecast
        let forecastValue = movingAverage.last!

        // Calculate confidence interval
        let (lower, upper) = calculateConfidenceInterval(
            predictedValue: forecastValue,
            historicalErrors: historicalErrors
        )

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedValues, movingAverage)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: [targetTime],
            forecastValues: [forecastValue],
            lowerBounds: [lower],
            upperBounds: [upper],
            confidenceLevel: 0.95,
            modelName: "MovingAverage",
            errorMetrics: errorMetrics
        )
    }

    override func update(timestamp: Date, value: Double) {
        // Update historical errors if we have a prediction to compare with
        if let lastPrediction = lastPrediction {
            let error = value - lastPrediction
            historicalErrors.append(error)

            // Keep only the last 100 errors
            if historicalErrors.count > 100 {
                historicalErrors.removeFirst()
            }
        }

        // Store this value as the last prediction for the next update
        lastPrediction = value
    }

    // MARK: - Private Properties

    /// The last predicted value
    private var lastPrediction: Double?

    // MARK: - Private Methods

    /// Calculate moving average for a series of values
    /// - Parameter values: The values to calculate moving average for
    /// - Returns: Array of moving average values
    private func calculateMovingAverage(_ values: [Double]) -> [Double] {
        guard !values.isEmpty else { return [] }

        let actualWindowSize = min(windowSize, values.count)
        var result: [Double] = []

        for i in 0..<values.count {
            let startIndex = max(0, i - actualWindowSize + 1)
            let endIndex = i + 1

            let windowValues = Array(values[startIndex..<endIndex])
            let average = windowValues.reduce(0, +) / Double(windowValues.count)

            result.append(average)
        }

        return result
    }

    /// Calculate error metrics using historical data
    /// - Parameters:
    ///   - actualValues: The actual values
    ///   - predictedValues: The predicted values
    /// - Returns: Dictionary of error metrics
    private func calculateHistoricalErrorMetrics(_ actualValues: [Double], _ predictedValues: [Double]) -> [String: Double] {
        // We need at least windowSize+1 values to calculate errors
        guard actualValues.count > windowSize, predictedValues.count >= windowSize else {
            return [:]
        }

        // For each point after the initial window, compare the actual value with the prediction
        // from the previous window
        var errors: [Double] = []

        for i in windowSize..<actualValues.count {
            let actualValue = actualValues[i]
            let predictedValue = predictedValues[i - 1] // Prediction from previous window

            errors.append(actualValue - predictedValue)
        }

        // Calculate error metrics
        let mae = errors.map { abs($0) }.reduce(0, +) / Double(errors.count)
        let mse = errors.map { $0 * $0 }.reduce(0, +) / Double(errors.count)
        let rmse = sqrt(mse)

        // Calculate MAPE
        var mape = 0.0
        var mapeCount = 0

        for i in windowSize..<actualValues.count {
            let actualValue = actualValues[i]
            let predictedValue = predictedValues[i - 1]

            if actualValue != 0 {
                mape += abs((actualValue - predictedValue) / actualValue)
                mapeCount += 1
            }
        }

        if mapeCount > 0 {
            mape = (mape / Double(mapeCount)) * 100
        }

        return [
            "MAE": mae,
            "MSE": mse,
            "RMSE": rmse,
            "MAPE": mape
        ]
    }
}
