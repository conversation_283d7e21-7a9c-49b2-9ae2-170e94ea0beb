//
//  ExponentialSmoothingForecast.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Forecast model using exponential smoothing technique
class ExponentialSmoothingForecast: BaseForecastModel {
    /// The smoothing factor (alpha) for level
    var alpha: Double

    /// The smoothing factor (beta) for trend
    var beta: Double?

    /// The smoothing factor (gamma) for seasonality
    var gamma: Double?

    /// The seasonal period (in number of data points)
    var seasonalPeriod: Int?

    /// Historical errors for confidence interval calculation
    private var historicalErrors: [Double] = []

    /// Last smoothed level
    private var lastLevel: Double?

    /// Last smoothed trend
    private var lastTrend: Double?

    /// Last seasonal factors
    private var seasonalFactors: [Double]?

    init(
        category: Insight.InsightCategory,
        metric: String,
        alpha: Double = 0.3,
        beta: Double? = nil,
        gamma: Double? = nil,
        seasonalPeriod: Int? = nil,
        minimumDataPoints: Int = 5
    ) {
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.seasonalPeriod = seasonalPeriod

        super.init(
            category: category,
            metric: metric,
            minimumDataPoints: minimumDataPoints
        )
    }

    override func forecast(
        timestamps: [Date],
        values: [Double],
        horizon: Int,
        interval: TimeInterval
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: getModelName(),
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Fit the model to the data
        fitModel(sortedValues)

        // Generate forecast timestamps
        var forecastTimestamps: [Date] = []
        let lastTimestamp = sortedTimestamps.last!

        for i in 0..<horizon {
            let forecastTimestamp = lastTimestamp.addingTimeInterval(Double(i + 1) * interval)
            forecastTimestamps.append(forecastTimestamp)
        }

        // Generate forecast values
        var forecastValues: [Double] = []
        var lowerBounds: [Double] = []
        var upperBounds: [Double] = []

        for i in 0..<horizon {
            let forecastValue = generateForecast(for: i + 1)
            forecastValues.append(forecastValue)

            // Calculate confidence interval
            let (lower, upper) = calculateConfidenceInterval(
                predictedValue: forecastValue,
                historicalErrors: historicalErrors
            )

            lowerBounds.append(lower)
            upperBounds.append(upper)
        }

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedValues)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: forecastTimestamps,
            forecastValues: forecastValues,
            lowerBounds: lowerBounds,
            upperBounds: upperBounds,
            confidenceLevel: 0.95,
            modelName: getModelName(),
            errorMetrics: errorMetrics
        )
    }

    override func forecastValue(
        timestamps: [Date],
        values: [Double],
        targetTime: Date
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: getModelName(),
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Fit the model to the data
        fitModel(sortedValues)

        // Calculate the forecast horizon based on the target time
        let lastTimestamp = sortedTimestamps.last!
        let timeInterval = targetTime.timeIntervalSince(lastTimestamp)

        // Estimate the horizon based on the average interval between data points
        var averageInterval = 0.0
        for i in 0..<sortedTimestamps.count - 1 {
            averageInterval += sortedTimestamps[i + 1].timeIntervalSince(sortedTimestamps[i])
        }
        averageInterval /= Double(sortedTimestamps.count - 1)

        let horizon = Int(timeInterval / averageInterval) + 1

        // Generate forecast
        let forecastValue = generateForecast(for: horizon)

        // Calculate confidence interval
        let (lower, upper) = calculateConfidenceInterval(
            predictedValue: forecastValue,
            historicalErrors: historicalErrors
        )

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedValues)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: [targetTime],
            forecastValues: [forecastValue],
            lowerBounds: [lower],
            upperBounds: [upper],
            confidenceLevel: 0.95,
            modelName: getModelName(),
            errorMetrics: errorMetrics
        )
    }

    override func update(timestamp: Date, value: Double) {
        // Update historical errors if we have a prediction to compare with
        if let lastPrediction = lastPrediction {
            let error = value - lastPrediction
            historicalErrors.append(error)

            // Keep only the last 100 errors
            if historicalErrors.count > 100 {
                historicalErrors.removeFirst()
            }
        }

        // Store this value as the last prediction for the next update
        lastPrediction = value
    }

    // MARK: - Private Properties

    /// The last predicted value
    private var lastPrediction: Double?

    // MARK: - Private Methods

    /// Get the name of the model based on its configuration
    /// - Returns: Model name
    private func getModelName() -> String {
        if beta != nil && gamma != nil && seasonalPeriod != nil {
            return "Triple Exponential Smoothing"
        } else if beta != nil {
            return "Double Exponential Smoothing"
        } else {
            return "Simple Exponential Smoothing"
        }
    }

    /// Fit the model to the data
    /// - Parameter values: The values to fit the model to
    private func fitModel(_ values: [Double]) {
        guard !values.isEmpty else { return }

        if beta != nil && gamma != nil && seasonalPeriod != nil {
            // Triple exponential smoothing (Holt-Winters)
            fitTripleExponentialSmoothing(values)
        } else if beta != nil {
            // Double exponential smoothing (Holt's method)
            fitDoubleExponentialSmoothing(values)
        } else {
            // Simple exponential smoothing
            fitSimpleExponentialSmoothing(values)
        }
    }

    /// Fit simple exponential smoothing model
    /// - Parameter values: The values to fit the model to
    private func fitSimpleExponentialSmoothing(_ values: [Double]) {
        // Initialize level with the first value
        var level = values[0]

        // Apply exponential smoothing
        for i in 1..<values.count {
            let newLevel = alpha * values[i] + (1 - alpha) * level
            level = newLevel
        }

        // Store the last level
        lastLevel = level
    }

    /// Fit double exponential smoothing model
    /// - Parameter values: The values to fit the model to
    private func fitDoubleExponentialSmoothing(_ values: [Double]) {
        guard let beta = beta else { return }

        // Initialize level and trend
        var level = values[0]
        var trend = values[1] - values[0]

        // Apply double exponential smoothing
        for i in 1..<values.count {
            let oldLevel = level
            level = alpha * values[i] + (1 - alpha) * (level + trend)
            trend = beta * (level - oldLevel) + (1 - beta) * trend
        }

        // Store the last level and trend
        lastLevel = level
        lastTrend = trend
    }

    /// Fit triple exponential smoothing model
    /// - Parameter values: The values to fit the model to
    private func fitTripleExponentialSmoothing(_ values: [Double]) {
        guard let beta = beta, let gamma = gamma, let seasonalPeriod = seasonalPeriod else { return }

        // Need at least 2 * seasonalPeriod data points
        guard values.count >= 2 * seasonalPeriod else {
            // Fall back to double exponential smoothing
            fitDoubleExponentialSmoothing(values)
            return
        }

        // Initialize seasonal factors
        var seasonalFactors = [Double](repeating: 0, count: seasonalPeriod)

        // Calculate initial seasonal factors
        for i in 0..<seasonalPeriod {
            var sum = 0.0
            var count = 0

            for j in stride(from: i, to: values.count, by: seasonalPeriod) {
                sum += values[j]
                count += 1
            }

            seasonalFactors[i] = sum / Double(count)
        }

        // Normalize seasonal factors
        let seasonalSum = seasonalFactors.reduce(0, +)
        let seasonalMean = seasonalSum / Double(seasonalPeriod)

        for i in 0..<seasonalPeriod {
            seasonalFactors[i] /= seasonalMean
        }

        // Initialize level and trend
        var level = values[0]
        var trend = (values[seasonalPeriod] - values[0]) / Double(seasonalPeriod)

        // Apply triple exponential smoothing
        for i in 0..<values.count {
            let seasonalIndex = i % seasonalPeriod
            let oldLevel = level

            level = alpha * (values[i] / seasonalFactors[seasonalIndex]) + (1 - alpha) * (level + trend)
            trend = beta * (level - oldLevel) + (1 - beta) * trend
            seasonalFactors[seasonalIndex] = gamma * (values[i] / level) + (1 - gamma) * seasonalFactors[seasonalIndex]
        }

        // Store the last level, trend, and seasonal factors
        lastLevel = level
        lastTrend = trend
        self.seasonalFactors = seasonalFactors
    }

    /// Generate a forecast for a specific horizon
    /// - Parameter horizon: The forecast horizon
    /// - Returns: The forecasted value
    private func generateForecast(for horizon: Int) -> Double {
        if let lastLevel = lastLevel, let lastTrend = lastTrend, let seasonalFactors = seasonalFactors {
            // Triple exponential smoothing forecast
            let seasonalIndex = (horizon - 1) % seasonalFactors.count
            return (lastLevel + Double(horizon) * lastTrend) * seasonalFactors[seasonalIndex]
        } else if let lastLevel = lastLevel, let lastTrend = lastTrend {
            // Double exponential smoothing forecast
            return lastLevel + Double(horizon) * lastTrend
        } else if let lastLevel = lastLevel {
            // Simple exponential smoothing forecast
            return lastLevel
        } else {
            // No model fitted yet
            return 0
        }
    }

    /// Calculate error metrics using historical data
    /// - Parameter values: The actual values
    /// - Returns: Dictionary of error metrics
    private func calculateHistoricalErrorMetrics(_ values: [Double]) -> [String: Double] {
        guard values.count >= 2 else {
            return [:]
        }

        // Generate one-step-ahead forecasts for each point
        var forecasts: [Double] = [values[0]] // First forecast is just the first value

        for i in 1..<values.count {
            // Fit the model to data up to i-1
            let historicalValues = Array(values[0..<i])

            if beta != nil && gamma != nil && seasonalPeriod != nil && i >= 2 * (seasonalPeriod ?? 0) {
                // Triple exponential smoothing
                fitTripleExponentialSmoothing(historicalValues)
            } else if beta != nil && i >= 2 {
                // Double exponential smoothing
                fitDoubleExponentialSmoothing(historicalValues)
            } else {
                // Simple exponential smoothing
                fitSimpleExponentialSmoothing(historicalValues)
            }

            // Generate forecast for the next point
            let forecast = generateForecast(for: 1)
            forecasts.append(forecast)
        }

        // Calculate errors
        var errors: [Double] = []

        for i in 0..<values.count {
            errors.append(values[i] - forecasts[i])
        }

        // Calculate error metrics
        let mae = errors.map { abs($0) }.reduce(0, +) / Double(errors.count)
        let mse = errors.map { $0 * $0 }.reduce(0, +) / Double(errors.count)
        let rmse = sqrt(mse)

        // Calculate MAPE
        var mape = 0.0
        var mapeCount = 0

        for i in 0..<values.count {
            if values[i] != 0 {
                mape += abs(errors[i] / values[i])
                mapeCount += 1
            }
        }

        if mapeCount > 0 {
            mape = (mape / Double(mapeCount)) * 100
        }

        return [
            "MAE": mae,
            "MSE": mse,
            "RMSE": rmse,
            "MAPE": mape
        ]
    }
}
