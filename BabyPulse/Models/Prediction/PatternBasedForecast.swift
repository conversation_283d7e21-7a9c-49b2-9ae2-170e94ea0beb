//
//  PatternBasedForecast.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Forecast model using pattern recognition
class PatternBasedForecast: BaseForecastModel {
    /// The detected cyclical pattern
    private var cyclicalPattern: CyclicalPattern?

    /// The detected trend
    private var trendAnalysis: TrendAnalysis?

    /// Historical errors for confidence interval calculation
    private var historicalErrors: [Double] = []

    override init(
        category: Insight.InsightCategory,
        metric: String,
        minimumDataPoints: Int = 10
    ) {
        super.init(
            category: category,
            metric: metric,
            minimumDataPoints: minimumDataPoints
        )
    }

    override func forecast(
        timestamps: [Date],
        values: [Double],
        horizon: Int,
        interval: TimeInterval
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: "PatternBased",
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Detect patterns in the data
        detectPatterns(sortedTimestamps, sortedValues)

        // Generate forecast timestamps
        var forecastTimestamps: [Date] = []
        let lastTimestamp = sortedTimestamps.last!

        for i in 0..<horizon {
            let forecastTimestamp = lastTimestamp.addingTimeInterval(Double(i + 1) * interval)
            forecastTimestamps.append(forecastTimestamp)
        }

        // Generate forecast values
        var forecastValues: [Double] = []
        var lowerBounds: [Double] = []
        var upperBounds: [Double] = []

        for i in 0..<horizon {
            let forecastTimestamp = forecastTimestamps[i]
            let forecastValue = generateForecast(for: forecastTimestamp, lastValue: sortedValues.last!)
            forecastValues.append(forecastValue)

            // Calculate confidence interval
            let (lower, upper) = calculateConfidenceInterval(
                predictedValue: forecastValue,
                historicalErrors: historicalErrors
            )

            lowerBounds.append(lower)
            upperBounds.append(upper)
        }

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedTimestamps, sortedValues)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: forecastTimestamps,
            forecastValues: forecastValues,
            lowerBounds: lowerBounds,
            upperBounds: upperBounds,
            confidenceLevel: 0.95,
            modelName: "PatternBased",
            errorMetrics: errorMetrics
        )
    }

    override func forecastValue(
        timestamps: [Date],
        values: [Double],
        targetTime: Date
    ) -> ForecastResult {
        guard timestamps.count == values.count, timestamps.count >= minimumDataPoints else {
            // Return empty forecast if not enough data
            return ForecastResult(
                category: category,
                metric: metric,
                forecastTimestamps: [],
                forecastValues: [],
                lowerBounds: [],
                upperBounds: [],
                confidenceLevel: 0.95,
                modelName: "PatternBased",
                errorMetrics: [:]
            )
        }

        // Sort data by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Detect patterns in the data
        detectPatterns(sortedTimestamps, sortedValues)

        // Generate forecast
        let forecastValue = generateForecast(for: targetTime, lastValue: sortedValues.last!)

        // Calculate confidence interval
        let (lower, upper) = calculateConfidenceInterval(
            predictedValue: forecastValue,
            historicalErrors: historicalErrors
        )

        // Calculate error metrics using historical data
        let errorMetrics = calculateHistoricalErrorMetrics(sortedTimestamps, sortedValues)

        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: [targetTime],
            forecastValues: [forecastValue],
            lowerBounds: [lower],
            upperBounds: [upper],
            confidenceLevel: 0.95,
            modelName: "PatternBased",
            errorMetrics: errorMetrics
        )
    }

    override func update(timestamp: Date, value: Double) {
        // Update historical errors if we have a prediction to compare with
        if let lastPrediction = lastPrediction {
            let error = value - lastPrediction
            historicalErrors.append(error)

            // Keep only the last 100 errors
            if historicalErrors.count > 100 {
                historicalErrors.removeFirst()
            }
        }

        // Store this value as the last prediction for the next update
        lastPrediction = value
    }

    // MARK: - Private Properties

    /// The last predicted value
    private var lastPrediction: Double?

    // MARK: - Private Methods

    /// Detect patterns in the data
    /// - Parameters:
    ///   - timestamps: The timestamps
    ///   - values: The values
    private func detectPatterns(_ timestamps: [Date], _ values: [Double]) {
        // Detect cyclical pattern
        cyclicalPattern = PatternDetectionUtilities.detectCyclicalPattern(
            timestamps: timestamps,
            values: values,
            category: category,
            metric: metric
        )

        // Detect trend
        trendAnalysis = PatternDetectionUtilities.detectTrend(
            timestamps: timestamps,
            values: values,
            category: category,
            metric: metric
        )
    }

    /// Generate a forecast for a specific timestamp
    /// - Parameters:
    ///   - timestamp: The timestamp to forecast for
    ///   - lastValue: The last observed value
    /// - Returns: The forecasted value
    private func generateForecast(for timestamp: Date, lastValue: Double) -> Double {
        var forecastValue = lastValue

        // Apply trend component if available
        if let trend = trendAnalysis {
            let now = Date()
            let timeInterval = timestamp.timeIntervalSince(now)
            let daysInFuture = timeInterval / (24 * 3600)

            // Apply trend rate of change
            if trend.direction == .increasing {
                forecastValue += trend.rateOfChange * daysInFuture
            } else if trend.direction == .decreasing {
                forecastValue -= abs(trend.rateOfChange) * daysInFuture
            }
        }

        // Apply cyclical component if available
        if let cycle = cyclicalPattern {
            let calendar = Calendar.current

            // Get hour of day for the timestamp
            let hour = calendar.component(.hour, from: timestamp)
            let minute = calendar.component(.minute, from: timestamp)
            let hourOfDay = Double(hour) + Double(minute) / 60.0

            // Calculate phase within the cycle
            let phase = hourOfDay.truncatingRemainder(dividingBy: cycle.periodHours)
            let normalizedPhase = phase / cycle.periodHours // 0 to 1

            // Simple sinusoidal model for the cycle
            let cyclicalComponent = cycle.amplitude * sin(2 * .pi * normalizedPhase)

            // Apply cyclical component
            forecastValue += cyclicalComponent
        }

        return forecastValue
    }

    /// Calculate error metrics using historical data
    /// - Parameters:
    ///   - timestamps: The timestamps
    ///   - values: The values
    /// - Returns: Dictionary of error metrics
    private func calculateHistoricalErrorMetrics(_ timestamps: [Date], _ values: [Double]) -> [String: Double] {
        guard timestamps.count >= 2 else {
            return [:]
        }

        // Generate one-step-ahead forecasts for each point
        var forecasts: [Double] = [values[0]] // First forecast is just the first value

        for i in 1..<values.count {
            // Use data up to i-1 to forecast point i
            let historicalTimestamps = Array(timestamps[0..<i])
            let historicalValues = Array(values[0..<i])

            // Detect patterns in historical data
            detectPatterns(historicalTimestamps, historicalValues)

            // Generate forecast for the next point
            let forecast = generateForecast(for: timestamps[i], lastValue: historicalValues.last!)
            forecasts.append(forecast)
        }

        // Calculate errors
        var errors: [Double] = []

        for i in 0..<values.count {
            errors.append(values[i] - forecasts[i])
        }

        // Calculate error metrics
        let mae = errors.map { abs($0) }.reduce(0, +) / Double(errors.count)
        let mse = errors.map { $0 * $0 }.reduce(0, +) / Double(errors.count)
        let rmse = sqrt(mse)

        // Calculate MAPE
        var mape = 0.0
        var mapeCount = 0

        for i in 0..<values.count {
            if values[i] != 0 {
                mape += abs(errors[i] / values[i])
                mapeCount += 1
            }
        }

        if mapeCount > 0 {
            mape = (mape / Double(mapeCount)) * 100
        }

        return [
            "MAE": mae,
            "MSE": mse,
            "RMSE": rmse,
            "MAPE": mape
        ]
    }
}
