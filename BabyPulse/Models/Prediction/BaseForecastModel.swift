//
//  BaseForecastModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Base class for forecast models
class BaseForecastModel {
    /// The category this forecast applies to
    let category: Insight.InsightCategory
    
    /// The specific metric being forecasted
    let metric: String
    
    /// The minimum number of data points required for forecasting
    let minimumDataPoints: Int
    
    init(
        category: Insight.InsightCategory,
        metric: String,
        minimumDataPoints: Int = 10
    ) {
        self.category = category
        self.metric = metric
        self.minimumDataPoints = minimumDataPoints
    }
    
    /// Forecast future values
    /// - Parameters:
    ///   - timestamps: Historical timestamps
    ///   - values: Historical values
    ///   - horizon: Number of future points to forecast
    ///   - interval: Time interval between forecast points
    /// - Returns: Forecast result
    func forecast(
        timestamps: [Date],
        values: [Double],
        horizon: Int,
        interval: TimeInterval
    ) -> ForecastResult {
        // Base implementation returns empty forecast
        // Subclasses should override this method
        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: [],
            forecastValues: [],
            lowerBounds: [],
            upperBounds: [],
            confidenceLevel: 0.95,
            modelName: "Base",
            errorMetrics: [:]
        )
    }
    
    /// Forecast a value at a specific time
    /// - Parameters:
    ///   - timestamps: Historical timestamps
    ///   - values: Historical values
    ///   - targetTime: The time to forecast for
    /// - Returns: Forecast result
    func forecastValue(
        timestamps: [Date],
        values: [Double],
        targetTime: Date
    ) -> ForecastResult {
        // Base implementation returns empty forecast
        // Subclasses should override this method
        return ForecastResult(
            category: category,
            metric: metric,
            forecastTimestamps: [],
            forecastValues: [],
            lowerBounds: [],
            upperBounds: [],
            confidenceLevel: 0.95,
            modelName: "Base",
            errorMetrics: [:]
        )
    }
    
    /// Update the model with a new observation
    /// - Parameters:
    ///   - timestamp: The timestamp of the observation
    ///   - value: The observed value
    func update(timestamp: Date, value: Double) {
        // Base implementation does nothing
        // Subclasses can override to update internal state
    }
    
    /// Calculate confidence interval for a predicted value
    /// - Parameters:
    ///   - predictedValue: The predicted value
    ///   - historicalErrors: Array of historical prediction errors
    /// - Returns: Tuple of (lower bound, upper bound)
    func calculateConfidenceInterval(
        predictedValue: Double,
        historicalErrors: [Double]
    ) -> (lower: Double, upper: Double) {
        // If no historical errors, use a default interval
        guard !historicalErrors.isEmpty else {
            // Default to ±10% of the predicted value
            let interval = max(0.1 * abs(predictedValue), 1.0)
            return (predictedValue - interval, predictedValue + interval)
        }
        
        // Calculate mean and standard deviation of errors
        let meanError = historicalErrors.reduce(0, +) / Double(historicalErrors.count)
        let variance = historicalErrors.reduce(0.0) { sum, error in
            let diff = error - meanError
            return sum + (diff * diff)
        } / Double(historicalErrors.count)
        let stdDev = sqrt(variance)
        
        // For 95% confidence interval, use ±1.96 standard deviations
        // Adjust for bias by adding the mean error
        let lower = predictedValue + meanError - 1.96 * stdDev
        let upper = predictedValue + meanError + 1.96 * stdDev
        
        return (lower, upper)
    }
}
