//
//  ForecastResult.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents the result of a forecasting operation
struct ForecastResult {
    /// The category this forecast applies to
    let category: Insight.InsightCategory
    
    /// The specific metric being forecasted
    let metric: String
    
    /// The timestamps for the forecasted values
    let forecastTimestamps: [Date]
    
    /// The forecasted values
    let forecastValues: [Double]
    
    /// The lower bounds of the confidence intervals
    let lowerBounds: [Double]
    
    /// The upper bounds of the confidence intervals
    let upperBounds: [Double]
    
    /// The confidence level (0.0-1.0)
    let confidenceLevel: Double
    
    /// The model used for forecasting
    let modelName: String
    
    /// Error metrics for the forecast
    let errorMetrics: [String: Double]
    
    /// Human-readable description of the forecast
    var description: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        
        if forecastTimestamps.count == 1 {
            // Single point forecast
            let timestamp = forecastTimestamps[0]
            let value = forecastValues[0]
            let lower = lowerBounds[0]
            let upper = upperBounds[0]
            
            return "Forecast for \(metric) at \(formatter.string(from: timestamp)): \(String(format: "%.1f", value)) (\(String(format: "%.1f", lower))-\(String(format: "%.1f", upper)))"
        } else {
            // Multi-point forecast
            let startTime = forecastTimestamps.first!
            let endTime = forecastTimestamps.last!
            
            return "Forecast for \(metric) from \(formatter.string(from: startTime)) to \(formatter.string(from: endTime)): \(forecastValues.count) points"
        }
    }
    
    /// Get the forecast value for a specific timestamp
    /// - Parameter timestamp: The timestamp to get the forecast for
    /// - Returns: Tuple of (value, lower bound, upper bound), or nil if timestamp not in forecast
    func valueAt(timestamp: Date) -> (value: Double, lower: Double, upper: Double)? {
        guard let index = forecastTimestamps.firstIndex(where: { 
            abs($0.timeIntervalSince(timestamp)) < 60 // Within 1 minute
        }) else {
            return nil
        }
        
        return (forecastValues[index], lowerBounds[index], upperBounds[index])
    }
    
    /// Get the forecast value for a specific time in the future
    /// - Parameter timeInFuture: The time in the future (in seconds)
    /// - Returns: Tuple of (value, lower bound, upper bound), or nil if time not in forecast
    func valueAtTimeInFuture(timeInFuture: TimeInterval) -> (value: Double, lower: Double, upper: Double)? {
        let targetTime = Date().addingTimeInterval(timeInFuture)
        return valueAt(timestamp: targetTime)
    }
    
    /// Convert to a prediction
    /// - Returns: Prediction object
    func toPrediction() -> Prediction {
        // Use the first forecast point for the prediction
        let value = forecastValues[0]
        let lower = lowerBounds[0]
        let upper = upperBounds[0]
        let timestamp = forecastTimestamps[0]
        
        // Calculate confidence as a percentage based on the width of the confidence interval
        let intervalWidth = upper - lower
        let relativePrecision = 1.0 - min(1.0, intervalWidth / (2.0 * abs(value)))
        let confidence = Int(relativePrecision * 100)
        
        // Create a time window for the prediction
        let startTime = timestamp.addingTimeInterval(-300) // 5 minutes before
        let endTime = timestamp.addingTimeInterval(300)    // 5 minutes after
        
        // Format the description
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        
        let description = "Predicted \(metric) at \(formatter.string(from: timestamp)): \(String(format: "%.1f", value)) (\(String(format: "%.1f", lower))-\(String(format: "%.1f", upper)))"
        
        return Prediction(
            type: "forecast_\(category.rawValue)_\(metric)",
            description: description,
            confidence: confidence,
            timeframe: DateInterval(start: startTime, end: endTime),
            predictedValue: value,
            confidenceInterval: lower...upper,
            forecastModel: modelName,
            errorMetrics: errorMetrics
        )
    }
    
    /// Convert to multiple predictions
    /// - Returns: Array of prediction objects
    func toPredictions() -> [Prediction] {
        var predictions: [Prediction] = []
        
        for i in 0..<forecastTimestamps.count {
            let value = forecastValues[i]
            let lower = lowerBounds[i]
            let upper = upperBounds[i]
            let timestamp = forecastTimestamps[i]
            
            // Calculate confidence as a percentage based on the width of the confidence interval
            let intervalWidth = upper - lower
            let relativePrecision = 1.0 - min(1.0, intervalWidth / (2.0 * abs(value)))
            let confidence = Int(relativePrecision * 100)
            
            // Create a time window for the prediction
            let startTime = timestamp.addingTimeInterval(-300) // 5 minutes before
            let endTime = timestamp.addingTimeInterval(300)    // 5 minutes after
            
            // Format the description
            let formatter = DateFormatter()
            formatter.dateStyle = .none
            formatter.timeStyle = .short
            
            let description = "Predicted \(metric) at \(formatter.string(from: timestamp)): \(String(format: "%.1f", value)) (\(String(format: "%.1f", lower))-\(String(format: "%.1f", upper)))"
            
            predictions.append(Prediction(
                type: "forecast_\(category.rawValue)_\(metric)",
                description: description,
                confidence: confidence,
                timeframe: DateInterval(start: startTime, end: endTime),
                predictedValue: value,
                confidenceInterval: lower...upper,
                forecastModel: modelName,
                errorMetrics: errorMetrics
            ))
        }
        
        return predictions
    }
}
