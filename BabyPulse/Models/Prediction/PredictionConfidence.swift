//
//  PredictionConfidence.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Utility for calculating prediction confidence
struct PredictionConfidence {
    /// Calculate confidence score based on multiple factors
    /// - Parameters:
    ///   - dataQuality: Score for data quality (0-100)
    ///   - patternStrength: Score for pattern strength (0-100)
    ///   - historicalAccuracy: Score for historical accuracy (0-100)
    ///   - timeHorizon: How far in the future the prediction is (in seconds)
    /// - Returns: Confidence score (0-100)
    static func calculateConfidenceScore(
        dataQuality: Int,
        patternStrength: Int,
        historicalAccuracy: Int,
        timeHorizon: TimeInterval
    ) -> Int {
        // Base confidence from the three main factors
        let baseConfidence = Double(dataQuality + patternStrength + historicalAccuracy) / 3.0

        // Adjust for time horizon - confidence decreases as time horizon increases
        let horizonFactor = calculateTimeHorizonFactor(timeHorizon)

        // Calculate final confidence
        let finalConfidence = baseConfidence * horizonFactor

        return min(100, max(0, Int(finalConfidence)))
    }

    /// Calculate data quality score based on data characteristics
    /// - Parameters:
    ///   - dataPoints: Number of data points available
    ///   - recency: How recent the data is (in seconds)
    ///   - consistency: Measure of data consistency (0.0-1.0)
    ///   - completeness: Measure of data completeness (0.0-1.0)
    /// - Returns: Data quality score (0-100)
    static func calculateDataQualityScore(
        dataPoints: Int,
        recency: TimeInterval,
        consistency: Double,
        completeness: Double
    ) -> Int {
        // Score for number of data points (more is better)
        let dataPointsScore = min(100, dataPoints * 5) // 20+ points = 100

        // Score for recency (more recent is better)
        let recencyScore = calculateRecencyScore(recency)

        // Score for consistency and completeness
        let consistencyScore = Int(consistency * 100)
        let completenessScore = Int(completeness * 100)

        // Weighted average - broken down into steps
        let dataPointsComponent = Double(dataPointsScore) * 0.3
        let recencyComponent = Double(recencyScore) * 0.3
        let consistencyComponent = Double(consistencyScore) * 0.2
        let completenessComponent = Double(completenessScore) * 0.2

        let weightedScore = dataPointsComponent + recencyComponent + consistencyComponent + completenessComponent

        return min(100, max(0, Int(weightedScore)))
    }

    /// Calculate pattern strength score
    /// - Parameters:
    ///   - correlation: Correlation coefficient (-1.0 to 1.0)
    ///   - cyclicalStrength: Strength of cyclical pattern (0.0-1.0)
    ///   - trendStrength: Strength of trend (0.0-1.0)
    /// - Returns: Pattern strength score (0-100)
    static func calculatePatternStrengthScore(
        correlation: Double,
        cyclicalStrength: Double,
        trendStrength: Double
    ) -> Int {
        // Score for correlation (absolute value)
        let correlationScore = Int(abs(correlation) * 100)

        // Score for cyclical pattern
        let cyclicalScore = Int(cyclicalStrength * 100)

        // Score for trend
        let trendScore = Int(trendStrength * 100)

        // Use the maximum of the three scores
        let maxScore = max(correlationScore, max(cyclicalScore, trendScore))

        return min(100, max(0, maxScore))
    }

    /// Calculate historical accuracy score
    /// - Parameters:
    ///   - pastPredictions: Number of past predictions
    ///   - correctPredictions: Number of correct predictions
    /// - Returns: Historical accuracy score (0-100)
    static func calculateHistoricalAccuracyScore(
        pastPredictions: Int,
        correctPredictions: Int
    ) -> Int {
        guard pastPredictions > 0 else {
            return 50 // Default score if no past predictions
        }

        let accuracyRate = Double(correctPredictions) / Double(pastPredictions)
        return min(100, max(0, Int(accuracyRate * 100)))
    }

    // MARK: - Private Methods

    /// Calculate time horizon factor
    /// - Parameter timeHorizon: Time horizon in seconds
    /// - Returns: Factor to adjust confidence (0.0-1.0)
    private static func calculateTimeHorizonFactor(_ timeHorizon: TimeInterval) -> Double {
        // Confidence decreases as time horizon increases
        // 1 hour: 0.9, 1 day: 0.7, 1 week: 0.5, 1 month: 0.3

        if timeHorizon <= 3600 { // 1 hour
            return 0.9
        } else if timeHorizon <= 86400 { // 1 day
            return 0.7
        } else if timeHorizon <= 604800 { // 1 week
            return 0.5
        } else if timeHorizon <= 2592000 { // 1 month
            return 0.3
        } else {
            return 0.2
        }
    }

    /// Calculate recency score
    /// - Parameter recency: Recency in seconds
    /// - Returns: Recency score (0-100)
    private static func calculateRecencyScore(_ recency: TimeInterval) -> Int {
        // More recent data gets a higher score
        // Within 1 hour: 100, within 1 day: 80, within 1 week: 60, within 1 month: 40

        if recency <= 3600 { // 1 hour
            return 100
        } else if recency <= 86400 { // 1 day
            return 80
        } else if recency <= 604800 { // 1 week
            return 60
        } else if recency <= 2592000 { // 1 month
            return 40
        } else {
            return 20
        }
    }
}
