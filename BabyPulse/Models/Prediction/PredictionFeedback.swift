//
//  PredictionFeedback.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Represents feedback on a prediction
struct PredictionFeedback {
    /// The ID of the prediction
    let predictionId: UUID
    
    /// The type of prediction
    let predictionType: String
    
    /// The timestamp when the prediction was made
    let predictionTimestamp: Date
    
    /// The predicted value, if applicable
    let predictedValue: Double?
    
    /// The actual observed value, if applicable
    let actualValue: Double?
    
    /// Whether the prediction was correct
    let wasCorrect: Bool
    
    /// The error margin (actual - predicted)
    var errorMargin: Double? {
        guard let predictedValue = predictedValue, let actualValue = actualValue else {
            return nil
        }
        
        return actualValue - predictedValue
    }
    
    /// The absolute error margin
    var absoluteErrorMargin: Double? {
        guard let errorMargin = errorMargin else {
            return nil
        }
        
        return abs(errorMargin)
    }
    
    /// The relative error margin (as a percentage)
    var relativeErrorMargin: Double? {
        guard let predictedValue = predictedValue, let actualValue = actualValue, predictedValue != 0 else {
            return nil
        }
        
        return abs((actualValue - predictedValue) / predictedValue) * 100
    }
}

/// Model for storing prediction feedback in SwiftData
@Model
final class PredictionFeedbackRecord {
    var id: UUID
    var predictionId: UUID
    var predictionType: String
    var predictionTimestamp: Date
    var predictedValue: Double?
    var actualValue: Double?
    var wasCorrect: Bool
    var createdAt: Date
    
    init(
        id: UUID = UUID(),
        predictionId: UUID,
        predictionType: String,
        predictionTimestamp: Date,
        predictedValue: Double? = nil,
        actualValue: Double? = nil,
        wasCorrect: Bool
    ) {
        self.id = id
        self.predictionId = predictionId
        self.predictionType = predictionType
        self.predictionTimestamp = predictionTimestamp
        self.predictedValue = predictedValue
        self.actualValue = actualValue
        self.wasCorrect = wasCorrect
        self.createdAt = Date()
    }
    
    /// Convert to PredictionFeedback
    func toPredictionFeedback() -> PredictionFeedback {
        return PredictionFeedback(
            predictionId: predictionId,
            predictionType: predictionType,
            predictionTimestamp: predictionTimestamp,
            predictedValue: predictedValue,
            actualValue: actualValue,
            wasCorrect: wasCorrect
        )
    }
}

/// Service for managing prediction feedback
class PredictionFeedbackService {
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// Record feedback for a prediction
    /// - Parameter feedback: The feedback to record
    func recordFeedback(_ feedback: PredictionFeedback) {
        let record = PredictionFeedbackRecord(
            predictionId: feedback.predictionId,
            predictionType: feedback.predictionType,
            predictionTimestamp: feedback.predictionTimestamp,
            predictedValue: feedback.predictedValue,
            actualValue: feedback.actualValue,
            wasCorrect: feedback.wasCorrect
        )
        
        modelContext.insert(record)
        
        do {
            try modelContext.save()
        } catch {
            print("Error saving prediction feedback: \(error)")
        }
    }
    
    /// Get feedback for a specific prediction
    /// - Parameter predictionId: The ID of the prediction
    /// - Returns: The feedback, or nil if not found
    func getFeedback(for predictionId: UUID) -> PredictionFeedback? {
        let descriptor = FetchDescriptor<PredictionFeedbackRecord>(
            predicate: #Predicate { $0.predictionId == predictionId }
        )
        
        do {
            let records = try modelContext.fetch(descriptor)
            return records.first?.toPredictionFeedback()
        } catch {
            print("Error fetching prediction feedback: \(error)")
            return nil
        }
    }
    
    /// Get all feedback for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Array of feedback
    func getAllFeedback(for predictionType: String) -> [PredictionFeedback] {
        let descriptor = FetchDescriptor<PredictionFeedbackRecord>(
            predicate: #Predicate { $0.predictionType == predictionType }
        )
        
        do {
            let records = try modelContext.fetch(descriptor)
            return records.map { $0.toPredictionFeedback() }
        } catch {
            print("Error fetching prediction feedback: \(error)")
            return []
        }
    }
    
    /// Calculate accuracy rate for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Accuracy rate (0.0-1.0)
    func calculateAccuracyRate(for predictionType: String) -> Double {
        let feedback = getAllFeedback(for: predictionType)
        
        guard !feedback.isEmpty else {
            return 0.0
        }
        
        let correctCount = feedback.filter { $0.wasCorrect }.count
        return Double(correctCount) / Double(feedback.count)
    }
    
    /// Calculate average error for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Average error, or nil if no numeric predictions
    func calculateAverageError(for predictionType: String) -> Double? {
        let feedback = getAllFeedback(for: predictionType)
        
        let errorsWithValues = feedback.compactMap { $0.absoluteErrorMargin }
        
        guard !errorsWithValues.isEmpty else {
            return nil
        }
        
        return errorsWithValues.reduce(0, +) / Double(errorsWithValues.count)
    }
}
