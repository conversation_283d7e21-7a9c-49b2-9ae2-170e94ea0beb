//
//  ForecastModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Protocol defining the interface for forecasting models
protocol ForecastModel {
    /// The category this model forecasts
    var category: Insight.InsightCategory { get }

    /// The specific metric being forecasted
    var metric: String { get }

    /// The minimum number of data points required for forecasting
    var minimumDataPoints: Int { get }

    /// Forecast future values based on historical data
    /// - Parameters:
    ///   - timestamps: Array of historical timestamps
    ///   - values: Array of corresponding historical values
    ///   - horizon: The number of future points to forecast
    ///   - interval: The time interval between forecast points
    /// - Returns: Forecast result containing predicted values and confidence intervals
    func forecast(
        timestamps: [Date],
        values: [Double],
        horizon: Int,
        interval: TimeInterval
    ) -> ForecastResult

    /// Forecast a specific future value at a given time
    /// - Parameters:
    ///   - timestamps: Array of historical timestamps
    ///   - values: Array of corresponding historical values
    ///   - targetTime: The specific time to forecast for
    /// - Returns: Forecast result for the specific time
    func forecastValue(
        timestamps: [Date],
        values: [Double],
        targetTime: Date
    ) -> ForecastResult

    /// Calculate the forecast error metrics
    /// - Parameters:
    ///   - actualValues: The actual observed values
    ///   - forecastedValues: The forecasted values
    /// - Returns: Dictionary of error metrics
    func calculateErrorMetrics(
        actualValues: [Double],
        forecastedValues: [Double]
    ) -> [String: Double]

    /// Update the model with new observations
    /// - Parameters:
    ///   - timestamp: The timestamp of the new observation
    ///   - value: The observed value
    func update(timestamp: Date, value: Double)
}

// The BaseForecastModel implementation has been moved to BaseForecastModel.swift
