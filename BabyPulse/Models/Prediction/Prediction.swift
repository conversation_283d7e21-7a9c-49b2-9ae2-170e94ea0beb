//
//  Prediction.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents a prediction about future baby behavior or metrics
struct Prediction {
    /// The unique identifier for the prediction
    let id: UUID

    /// The type of prediction
    let type: String

    /// Human-readable description of the prediction
    let description: String

    /// The confidence level (0-100)
    let confidence: Int

    /// The timeframe for the prediction
    let timeframe: DateInterval

    /// The predicted value
    let predictedValue: Double?

    /// The confidence interval for the prediction
    let confidenceInterval: ClosedRange<Double>?

    /// The model used for the prediction
    let forecastModel: String?

    /// Error metrics for the prediction
    let errorMetrics: [String: Double]?

    /// Event type for event predictions
    let eventType: String?

    /// Event probability for event predictions
    let eventProbability: Double?

    /// Evidence supporting the event prediction
    let eventEvidence: [String]?

    /// Whether the prediction was correct
    var wasCorrect: Bool?

    /// Whether the prediction has been verified
    var isVerified: Bool {
        return wasCorrect != nil
    }

    /// The actual value (if verified)
    var actualValue: Double?

    /// The error (if verified)
    var error: Double? {
        guard let actualValue = actualValue, let predictedValue = predictedValue else { return nil }
        return actualValue - predictedValue
    }

    /// The absolute error (if verified)
    var absoluteError: Double? {
        guard let error = error else { return nil }
        return abs(error)
    }

    /// Whether the prediction was accurate (within confidence interval)
    var wasAccurate: Bool? {
        guard let actualValue = actualValue, let confidenceInterval = confidenceInterval else { return nil }
        return confidenceInterval.contains(actualValue)
    }

    init(
        id: UUID = UUID(),
        type: String,
        description: String,
        confidence: Int,
        timeframe: DateInterval,
        predictedValue: Double?,
        confidenceInterval: ClosedRange<Double>?,
        forecastModel: String?,
        errorMetrics: [String: Double]?,
        eventType: String? = nil,
        eventProbability: Double? = nil,
        eventEvidence: [String]? = nil,
        wasCorrect: Bool? = nil,
        actualValue: Double? = nil
    ) {
        self.id = id
        self.type = type
        self.description = description
        self.confidence = confidence
        self.timeframe = timeframe
        self.predictedValue = predictedValue
        self.confidenceInterval = confidenceInterval
        self.forecastModel = forecastModel
        self.errorMetrics = errorMetrics
        self.eventType = eventType
        self.eventProbability = eventProbability
        self.eventEvidence = eventEvidence
        self.wasCorrect = wasCorrect
        self.actualValue = actualValue
    }

    /// Verify the prediction with an actual value
    /// - Parameter value: The actual observed value
    /// - Returns: A new prediction with verification data
    func verify(with value: Double) -> Prediction {
        var wasCorrect = false

        if let confidenceInterval = confidenceInterval {
            wasCorrect = confidenceInterval.contains(value)
        } else if let predictedValue = predictedValue {
            // If no confidence interval, consider it correct if within 10% of predicted value
            let error = abs(value - predictedValue)
            let relativeError = error / abs(predictedValue)
            wasCorrect = relativeError <= 0.1
        } else if let eventProbability = eventProbability {
            // For event predictions, consider it correct if probability > 0.7 and event occurred
            // or probability < 0.3 and event didn't occur
            let eventOccurred = value > 0.5 // Assuming value > 0.5 means event occurred
            wasCorrect = (eventProbability > 0.7 && eventOccurred) || (eventProbability < 0.3 && !eventOccurred)
        }

        return Prediction(
            id: id,
            type: type,
            description: description,
            confidence: confidence,
            timeframe: timeframe,
            predictedValue: predictedValue,
            confidenceInterval: confidenceInterval,
            forecastModel: forecastModel,
            errorMetrics: errorMetrics,
            eventType: eventType,
            eventProbability: eventProbability,
            eventEvidence: eventEvidence,
            wasCorrect: wasCorrect,
            actualValue: value
        )
    }

    /// Format the prediction for display
    /// - Returns: Formatted string
    func formattedDescription() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short

        let startTime = formatter.string(from: timeframe.start)
        let endTime = formatter.string(from: timeframe.end)

        var result = "\(description)\n"
        result += "Time: \(startTime) - \(endTime)\n"
        result += "Confidence: \(confidence)%\n"

        if let predictedValue = predictedValue {
            result += "Predicted: \(String(format: "%.1f", predictedValue))\n"
        }

        if isVerified, let actualValue = actualValue, let wasCorrect = wasCorrect {
            result += "Actual: \(String(format: "%.1f", actualValue))\n"
            result += "Accuracy: \(wasCorrect ? "Correct" : "Incorrect")\n"
        }

        return result
    }
}
