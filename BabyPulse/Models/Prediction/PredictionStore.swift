//
//  PredictionStore.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Model for storing predictions in SwiftData
@Model
final class PredictionRecord {
    var id: UUID
    var type: String
    var descriptionText: String
    var confidence: Int
    var timeframeStart: Date
    var timeframeEnd: Date
    var predictedValue: Double?
    var confidenceIntervalLower: Double?
    var confidenceIntervalUpper: Double?
    var forecastModel: String?
    var eventType: String?
    var eventProbability: Double?
    var eventEvidence: [String]?
    var createdAt: Date
    var expiresAt: Date?
    var wasCorrect: Bool?

    // Relationship
    @Relationship var baby: Baby?

    init(
        id: UUID = UUID(),
        type: String,
        description: String,
        confidence: Int,
        timeframeStart: Date,
        timeframeEnd: Date,
        predictedValue: Double? = nil,
        confidenceIntervalLower: Double? = nil,
        confidenceIntervalUpper: Double? = nil,
        forecastModel: String? = nil,
        eventType: String? = nil,
        eventProbability: Double? = nil,
        eventEvidence: [String]? = nil,
        baby: Baby? = nil
    ) {
        self.id = id
        self.type = type
        self.descriptionText = description
        self.confidence = confidence
        self.timeframeStart = timeframeStart
        self.timeframeEnd = timeframeEnd
        self.predictedValue = predictedValue
        self.confidenceIntervalLower = confidenceIntervalLower
        self.confidenceIntervalUpper = confidenceIntervalUpper
        self.forecastModel = forecastModel
        self.eventType = eventType
        self.eventProbability = eventProbability
        self.eventEvidence = eventEvidence
        self.baby = baby
        self.createdAt = Date()

        // Set expiration date to 1 day after the end of the timeframe
        self.expiresAt = timeframeEnd.addingTimeInterval(86400)
    }

    /// Convert to Prediction
    func toPrediction() -> Prediction {
        var confidenceInterval: ClosedRange<Double>? = nil
        if let lower = confidenceIntervalLower, let upper = confidenceIntervalUpper {
            confidenceInterval = lower...upper
        }

        return Prediction(
            id: id,
            type: type,
            description: descriptionText,
            confidence: confidence,
            timeframe: DateInterval(start: timeframeStart, end: timeframeEnd),
            predictedValue: predictedValue,
            confidenceInterval: confidenceInterval,
            forecastModel: forecastModel,
            errorMetrics: nil,
            eventType: eventType,
            eventProbability: eventProbability,
            eventEvidence: eventEvidence,
            wasCorrect: wasCorrect
        )
    }
}

/// Service for managing predictions
class PredictionStore {
    private let modelContext: ModelContext

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    /// Save a prediction
    /// - Parameters:
    ///   - prediction: The prediction to save
    ///   - baby: The baby the prediction is for
    func savePrediction(_ prediction: Prediction, for baby: Baby) {
        let record = PredictionRecord(
            id: prediction.id,
            type: prediction.type,
            description: prediction.description,
            confidence: prediction.confidence,
            timeframeStart: prediction.timeframe.start,
            timeframeEnd: prediction.timeframe.end,
            predictedValue: prediction.predictedValue,
            confidenceIntervalLower: prediction.confidenceInterval?.lowerBound,
            confidenceIntervalUpper: prediction.confidenceInterval?.upperBound,
            forecastModel: prediction.forecastModel,
            eventType: prediction.eventType,
            eventProbability: prediction.eventProbability,
            eventEvidence: prediction.eventEvidence,
            baby: baby
        )

        modelContext.insert(record)

        do {
            try modelContext.save()
        } catch {
            print("Error saving prediction: \(error)")
        }
    }

    /// Get a prediction by ID
    /// - Parameter id: The ID of the prediction
    /// - Returns: The prediction, or nil if not found
    func getPrediction(id: UUID) -> Prediction? {
        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate { $0.id == id }
        )

        do {
            let records = try modelContext.fetch(descriptor)
            return records.first?.toPrediction()
        } catch {
            print("Error fetching prediction: \(error)")
            return nil
        }
    }

    /// Get all active predictions for a baby
    /// - Parameter baby: The baby to get predictions for
    /// - Returns: Array of predictions
    func getActivePredictions(for baby: Baby) -> [Prediction] {
        let now = Date()

        let babyId = baby.id
        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate {
                $0.baby != nil && $0.baby!.id == babyId &&
                $0.timeframeEnd >= now &&
                ($0.expiresAt == nil || $0.expiresAt! >= now)
            }
        )

        do {
            let records = try modelContext.fetch(descriptor)
            return records.map { $0.toPrediction() }
        } catch {
            print("Error fetching predictions: \(error)")
            return []
        }
    }

    /// Get all predictions of a specific type for a baby
    /// - Parameters:
    ///   - type: The type of prediction
    ///   - baby: The baby to get predictions for
    /// - Returns: Array of predictions
    func getPredictions(ofType type: String, for baby: Baby) -> [Prediction] {
        let babyId = baby.id
        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate {
                $0.baby != nil && $0.baby!.id == babyId && $0.type == type
            }
        )

        do {
            let records = try modelContext.fetch(descriptor)
            return records.map { $0.toPrediction() }
        } catch {
            print("Error fetching predictions: \(error)")
            return []
        }
    }

    /// Update the correctness of a prediction
    /// - Parameters:
    ///   - id: The ID of the prediction
    ///   - wasCorrect: Whether the prediction was correct
    func updatePredictionCorrectness(id: UUID, wasCorrect: Bool) {
        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate { $0.id == id }
        )

        do {
            let records = try modelContext.fetch(descriptor)

            if let record = records.first {
                record.wasCorrect = wasCorrect

                try modelContext.save()
            }
        } catch {
            print("Error updating prediction correctness: \(error)")
        }
    }

    /// Delete expired predictions
    func deleteExpiredPredictions() {
        let now = Date()

        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate {
                $0.expiresAt != nil && $0.expiresAt! < now
            }
        )

        do {
            let records = try modelContext.fetch(descriptor)

            for record in records {
                modelContext.delete(record)
            }

            try modelContext.save()
        } catch {
            print("Error deleting expired predictions: \(error)")
        }
    }
}
