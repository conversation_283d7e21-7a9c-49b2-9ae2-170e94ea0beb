//
//  SleepEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class SleepEntry: Identifiable, EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus

    // Relationship
    @Relationship var baby: Baby?

    // Sleep-specific properties
    var endTime: Date?
    var duration: Int? // In minutes, calculated or manually entered
    var location: SleepLocation?

    enum SleepLocation: String, Codable, CaseIterable {
        case crib
        case bassinet
        case bed
        case arms
        case stroller
        case carseat
        case other

        var description: String {
            switch self {
            case .crib:
                return "Crib"
            case .bassinet:
                return "Bassinet"
            case .bed:
                return "Bed"
            case .arms:
                return "Arms"
            case .stroller:
                return "Stroller"
            case .carseat:
                return "Car Seat"
            case .other:
                return "Other"
            }
        }
    }

    var calculatedDuration: Int? {
        guard let endTime = endTime else { return duration }
        return Int(endTime.timeIntervalSince(timestamp) / 60)
    }

    init(id: UUID = UUID(), startTime: Date, endTime: Date? = nil, location: SleepLocation? = nil, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = startTime
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
        self.endTime = endTime
        self.location = location

        // Calculate duration if both start and end times are provided
        if let endTime = endTime {
            self.duration = Int(endTime.timeIntervalSince(startTime) / 60)
        }
    }

    // Convenience initializer for completed sleep
    static func createCompletedSleep(startTime: Date, endTime: Date, location: SleepLocation? = nil, notes: String? = nil, baby: Baby? = nil) -> SleepEntry {
        return SleepEntry(startTime: startTime, endTime: endTime, location: location, notes: notes, baby: baby)
    }

    // Convenience initializer for ongoing sleep
    static func createOngoingSleep(startTime: Date, location: SleepLocation? = nil, notes: String? = nil, baby: Baby? = nil) -> SleepEntry {
        return SleepEntry(startTime: startTime, location: location, notes: notes, baby: baby)
    }

    // Method to end an ongoing sleep session
    func endSleep(at endTime: Date) {
        self.endTime = endTime
        self.duration = Int(endTime.timeIntervalSince(timestamp) / 60)
        self.updatedAt = Date()
    }
}
