//
//  LogCategory.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

enum LogCategory: String, CaseIterable, Identifiable {
    case feeding
    case diaper
    case sleep
    case growth
    case health
    case general

    var id: String { self.rawValue }

    var title: String {
        switch self {
        case .feeding:
            return "Feeding"
        case .diaper:
            return "Diaper"
        case .sleep:
            return "Sleep"
        case .growth:
            return "Growth"
        case .health:
            return "Health"
        case .general:
            return "Activity"
        }
    }

    var icon: String {
        switch self {
        case .feeding:
            return "cup.and.saucer.fill"
        case .diaper:
            return "heart.fill"
        case .sleep:
            return "moon.fill"
        case .growth:
            return "ruler.fill"
        case .health:
            return "cross.case.fill"
        case .general:
            return "figure.walk"
        }
    }

    var color: Color {
        switch self {
        case .feeding:
            return BabyPulseColors.feeding
        case .diaper:
            return BabyPulseColors.diaper
        case .sleep:
            return BabyPulseColors.sleep
        case .growth:
            return BabyPulseColors.growth
        case .health:
            return BabyPulseColors.health
        case .general:
            return BabyPulseColors.generalActivity
        }
    }
}
