//
//  GrowthEntry.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

@Model
final class GrowthEntry: Identifiable, EntryProtocol {
    var id: UUID
    var timestamp: Date
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var syncStatus: SyncStatus

    // Relationship
    @Relationship var baby: Baby?

    // Growth-specific properties
    var weight: Double?      // In kg
    var height: Double?      // In cm
    var headCircumference: Double? // In cm
    
    // Calculated percentiles (not stored in database)
    @Transient var weightPercentile: Double?
    @Transient var heightPercentile: Double?
    @Transient var headCircumferencePercentile: Double?
    
    init(id: UUID = UUID(), timestamp: Date, weight: Double? = nil, height: Double? = nil, headCircumference: Double? = nil, notes: String? = nil, baby: Baby? = nil) {
        self.id = id
        self.timestamp = timestamp
        self.notes = notes
        self.baby = baby
        self.createdAt = Date()
        self.updatedAt = Date()
        self.syncStatus = .notSynced
        self.weight = weight
        self.height = height
        self.headCircumference = headCircumference
    }
    
    // Calculate percentiles based on WHO/CDC growth charts
    // This is a placeholder - actual implementation would use reference data
    func calculatePercentiles() {
        guard let baby = baby else { return }
        
        // Calculate age in months at time of measurement
        let ageInMonths = Calendar.current.dateComponents([.month], from: baby.birthDate, to: timestamp).month ?? 0
        
        // Placeholder calculations - in a real app, these would use reference data
        if let weight = weight {
            // Simplified example calculation
            weightPercentile = calculateWeightPercentile(weight: weight, ageInMonths: ageInMonths, isMale: baby.gender == .male)
        }
        
        if let height = height {
            // Simplified example calculation
            heightPercentile = calculateHeightPercentile(height: height, ageInMonths: ageInMonths, isMale: baby.gender == .male)
        }
        
        if let headCircumference = headCircumference {
            // Simplified example calculation
            headCircumferencePercentile = calculateHeadCircumferencePercentile(headCircumference: headCircumference, ageInMonths: ageInMonths, isMale: baby.gender == .male)
        }
    }
    
    // Placeholder methods for percentile calculations
    private func calculateWeightPercentile(weight: Double, ageInMonths: Int, isMale: Bool) -> Double {
        // In a real implementation, this would use WHO/CDC growth chart data
        // For now, return a random percentile between 5 and 95
        return Double.random(in: 5...95)
    }
    
    private func calculateHeightPercentile(height: Double, ageInMonths: Int, isMale: Bool) -> Double {
        // In a real implementation, this would use WHO/CDC growth chart data
        // For now, return a random percentile between 5 and 95
        return Double.random(in: 5...95)
    }
    
    private func calculateHeadCircumferencePercentile(headCircumference: Double, ageInMonths: Int, isMale: Bool) -> Double {
        // In a real implementation, this would use WHO/CDC growth chart data
        // For now, return a random percentile between 5 and 95
        return Double.random(in: 5...95)
    }
}
