//
//  Insight.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData
import SwiftUI

@Model
final class Insight: Identifiable {
    var id: UUID
    var category: InsightCategory
    var title: String
    var metric: String
    var insightContent: String // Renamed from 'description' to avoid conflict with Swift's CustomStringConvertible protocol
    var timestamp: Date
    var isRead: Bool
    var needsAttention: Bool
    var confidence: Int
    var severity: InsightSeverity
    var actionItems: [String]
    var hashKey: String
    var feedback: Bool? // true = helpful, false = unhelpful, nil = no feedback

    // Relationship
    @Relationship var baby: Baby?

    enum InsightSeverity: String, Codable, CaseIterable {
        case info
        case warning
        case urgent

        var color: Color {
            switch self {
            case .info:
                return Color(.systemGray)
            case .warning:
                return Color.orange
            case .urgent:
                return Color.red
            }
        }
    }

    enum InsightCategory: String, Codable, CaseIterable {
        case feeding
        case sleep
        case diaper
        case growth
        case health
        case development

        var description: String {
            switch self {
            case .feeding:
                return "Feeding"
            case .sleep:
                return "Sleep"
            case .diaper:
                return "Diaper"
            case .growth:
                return "Growth"
            case .health:
                return "Health"
            case .development:
                return "Development"
            }
        }

        var icon: String {
            switch self {
            case .feeding:
                return "cup.and.saucer.fill" // Replacing milk.fill with a valid symbol
            case .sleep:
                return "moon.fill"
            case .diaper:
                return "figure.walk.circle.fill"
            case .growth:
                return "ruler"
            case .health:
                return "heart.fill"
            case .development:
                return "brain.head.profile"
            }
        }

        var color: Color {
            switch self {
            case .feeding:
                return .blue
            case .sleep:
                return .indigo
            case .diaper:
                return .green
            case .growth:
                return .orange
            case .health:
                return .red
            case .development:
                return .purple
            }
        }
    }

    init(id: UUID = UUID(), category: InsightCategory, title: String, metric: String, insightContent: String, timestamp: Date, isRead: Bool = false, needsAttention: Bool = false, confidence: Int = 75, severity: InsightSeverity = .info, actionItems: [String] = [], hashKey: String = "", baby: Baby? = nil, feedback: Bool? = nil) {
        self.id = id
        self.category = category
        self.title = title
        self.metric = metric
        self.insightContent = insightContent
        self.timestamp = timestamp
        self.isRead = isRead
        self.needsAttention = needsAttention
        self.confidence = confidence
        self.severity = severity
        self.actionItems = actionItems
        self.hashKey = hashKey
        self.baby = baby
        self.feedback = feedback
    }
}