//
//  SwiftDataExtensions.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

extension ModelContext {
    /// Safely fetch a Baby object by ID, handling invalidated objects gracefully
    /// - Parameter id: The UUID of the Baby to fetch
    /// - Returns: The Baby object if found and valid, nil otherwise
    func safeFetchBaby(id: UUID) -> Baby? {
        do {
            let descriptor = FetchDescriptor<Baby>(
                predicate: #Predicate { baby in
                    baby.id == id
                }
            )

            let babies = try fetch(descriptor)
            return babies.first
        } catch {
            print("Error safely fetching baby: \(error.localizedDescription)")
            return nil
        }
    }

    /// Safely fetch the currently selected Baby from UserPreferences
    /// - Returns: The currently selected Baby if found and valid, nil otherwise
    func fetchSelectedBaby() -> Baby? {
        do {
            // First get the UserPreferences to find the selected baby ID
            let preferencesDescriptor = FetchDescriptor<UserPreferences>()
            let preferences = try fetch(preferencesDescriptor)

            guard let userPrefs = preferences.first,
                  let selectedBabyID = userPrefs.selectedBabyID else {
                return nil
            }

            // Then fetch the Baby with that ID
            return safeFetchBaby(id: selectedBabyID)
        } catch {
            print("Error fetching selected baby: \(error.localizedDescription)")
            return nil
        }
    }

    /// Safely update the selected baby ID in UserPreferences
    /// - Parameter babyID: The UUID of the Baby to select, or nil to clear the selection
    func updateSelectedBabyID(_ babyID: UUID?) {
        do {
            let preferencesDescriptor = FetchDescriptor<UserPreferences>()
            let preferences = try fetch(preferencesDescriptor)

            if let userPreferences = preferences.first {
                userPreferences.selectedBabyID = babyID
                userPreferences.updatedAt = Date()

                try save()
            }
        } catch {
            print("Error updating selected baby ID: \(error.localizedDescription)")
        }
    }
}

extension PersistentModel {
    /// Check if this model is still valid (not invalidated)
    var isValid: Bool {
        // This will return false if the object has been invalidated
        do {
            // Try to access the modelContext property
            // This will throw if the model is invalidated
            _ = self.modelContext
            return true
        } catch {
            return false
        }
    }
}
