# BabyPulse Insight Prompt Templates
# Simplified format for compatibility with current parser

feeding_frequency:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Feedings in last 24 hours: {{recent_count}} - Average feedings per day (last 7 days): {{historical_average}} - Change: {{change_percentage}}% Based on this information, provide a brief, helpful insight about your baby's feeding frequency. Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the frequency is appropriate. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_count, historical_average, change_percentage"
  contextual_thresholds: "significant_change: 20%, alert_threshold: 40%"
  actionable_recommendations: "Monitor feeding patterns for another day to see if they stabilize, Consider checking for signs of hunger between feedings, Try adjusting the feeding schedule slightly to see if it helps"
  priority_scoring: "severity: low, urgency: low, impact: medium"

feeding_volume:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Average volume per feeding (last 24 hours): {{recent_value}}ml - Average volume per feeding (last 7 days): {{historical_average}}ml - Change: {{change_percentage}}% - Feeding type: {{feeding_type}} Based on this information, provide a brief, helpful insight about your baby's feeding volume. Focus on pattern recognition and what it might mean. Consider the baby's age and weight when determining if the volume is appropriate. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_value, historical_average, change_percentage, feeding_type"
  contextual_thresholds: "significant_change: 15%, alert_threshold: 30%"
  actionable_recommendations: "Monitor feeding volumes for another day to see if they stabilize, Consider checking for signs of fullness or hunger, For formula-fed babies, ensure the formula is prepared according to instructions"
  priority_scoring: "severity: low, urgency: low, impact: medium"

sleep_duration:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Total sleep in last 24 hours: {{recent_value}} hours - Average daily sleep (last 7 days): {{historical_average}} hours - Change: {{change_percentage}}% - Day/night distribution: {{day_night_ratio}} Based on this information, provide a brief, helpful insight about your baby's sleep duration. Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the duration is appropriate. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_value, historical_average, change_percentage, day_night_ratio"
  contextual_thresholds: "significant_change: 15%, alert_threshold: 25%"
  actionable_recommendations: "Maintain a consistent bedtime routine to support healthy sleep patterns, Consider adjusting nap schedules if nighttime sleep is affected, Ensure the sleep environment is comfortable and conducive to sleep"
  priority_scoring: "severity: medium, urgency: medium, impact: high"

sleep_pattern:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Number of sleep sessions in last 24 hours: {{recent_count}} - Average number of sleep sessions (last 7 days): {{historical_average}} - Change: {{change_percentage}}% - Longest sleep stretch: {{longest_stretch}} hours - Most common sleep location: {{common_location}} Based on this information, provide a brief, helpful insight about your baby's sleep patterns. Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the pattern is appropriate. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_count, historical_average, change_percentage, longest_stretch, common_location"
  contextual_thresholds: "significant_change: 20%, alert_threshold: 30%"
  actionable_recommendations: "Consider implementing a consistent sleep routine if not already in place, Pay attention to sleep cues to help baby fall asleep before becoming overtired, Experiment with different sleep environments to find what works best"
  priority_scoring: "severity: medium, urgency: medium, impact: high"

diaper_frequency:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Diaper changes in last 24 hours: {{recent_count}} - Average diaper changes per day (last 7 days): {{historical_average}} - Change: {{change_percentage}}% - Distribution: {{wet_count}} wet, {{dirty_count}} dirty, {{mixed_count}} mixed Based on this information, provide a brief, helpful insight about your baby's diaper patterns. Focus on pattern recognition and what it might mean. Consider the baby's age and feeding type when determining if the pattern is appropriate. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_count, historical_average, change_percentage, wet_count, dirty_count, mixed_count"
  contextual_thresholds: "significant_change: 25%, alert_threshold: 40%"
  actionable_recommendations: "Monitor hydration by tracking wet diapers, For breastfed babies, dirty diapers may decrease after 6 weeks, which is normal, For formula-fed babies, expect more consistent stool patterns"
  priority_scoring: "severity: low, urgency: low, impact: medium"

temperature_alert:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Current temperature: {{current_temp}}{{temp_unit}} - Previous temperature ({{hours_since_last}} hours ago): {{previous_temp}}{{temp_unit}} - Change: {{temp_change}}{{temp_unit}} - Normal range: {{normal_range_low}}{{temp_unit}} - {{normal_range_high}}{{temp_unit}} Based on this information, provide a brief, helpful insight about your baby's temperature trend. Focus on pattern recognition and what it might mean. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, current_temp, previous_temp, hours_since_last, temp_change, temp_unit, normal_range_low, normal_range_high"
  contextual_thresholds: "significant_change: 0.5°C or 1°F, alert_threshold: 38°C or 100.4°F"
  actionable_recommendations: "Continue monitoring temperature every few hours, Ensure baby is dressed appropriately for the environment, Keep baby hydrated, especially if temperature is elevated"
  priority_scoring: "severity: high, urgency: high, impact: high"

symptom_pattern_analysis:
  template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Baby Information: - Name: {{baby_name}} - Age: {{baby_age}} ({{baby_age_weeks}} weeks) - Current symptoms: {{current_symptoms}} - Symptom duration: {{symptom_duration}} hours/days - Related factors: {{related_factors}} Based on this information, provide a brief, helpful insight about your baby's symptoms. Focus on pattern recognition and what it might mean. Include 1-2 actionable recommendations if appropriate. Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations). Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider."
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, current_symptoms, symptom_duration, related_factors"
  contextual_thresholds: "significant_change: new symptom or worsening, alert_threshold: severe symptoms or multiple symptoms"
  actionable_recommendations: "Continue monitoring symptoms and track any changes, Ensure baby is comfortable and well-hydrated, Consider environmental factors that might be contributing to symptoms"
  priority_scoring: "severity: medium, urgency: medium, impact: high"
