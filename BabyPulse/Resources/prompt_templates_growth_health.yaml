# BabyPulse Insight Prompt Templates for Growth and Health
# Specialized templates that match analyzer insight types

# Growth Templates
weight_gain:
  template: |
    You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.
    
    Baby Information:
    - Name: {{baby_name}}
    - Age: {{baby_age}} ({{baby_age_weeks}} weeks)
    - Recent weight: {{recent_weight}}kg
    - Historical weight: {{historical_weight}}kg
    - Weight gain: {{weight_gain}}kg
    - Change: {{change_percentage}}%
    
    Based on this information, provide a brief, helpful insight about your baby's weight gain. 
    Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the growth is appropriate.
    Include 1-2 actionable recommendations if appropriate.
    Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations).
    Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider.
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_weight, historical_weight, weight_gain, change_percentage"
  contextual_thresholds: "significant_change: 10%, alert_threshold: 20%"
  actionable_recommendations: "Continue monitoring weight regularly, Consult with a healthcare provider if concerned about weight gain"
  priority_scoring: "severity: medium, urgency: medium, impact: high"

height_increase:
  template: |
    You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.
    
    Baby Information:
    - Name: {{baby_name}}
    - Age: {{baby_age}} ({{baby_age_weeks}} weeks)
    - Recent height: {{recent_height}}cm
    - Historical height: {{historical_height}}cm
    - Height increase: {{height_increase}}cm
    - Change: {{change_percentage}}%
    
    Based on this information, provide a brief, helpful insight about your baby's height increase. 
    Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the growth is appropriate.
    Include 1-2 actionable recommendations if appropriate.
    Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations).
    Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider.
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_height, historical_height, height_increase, change_percentage"
  contextual_thresholds: "significant_change: 10%, alert_threshold: 20%"
  actionable_recommendations: "Continue monitoring height regularly, Consult with a healthcare provider if concerned about height increase"
  priority_scoring: "severity: medium, urgency: medium, impact: high"

head_circumference_increase:
  template: |
    You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.
    
    Baby Information:
    - Name: {{baby_name}}
    - Age: {{baby_age}} ({{baby_age_weeks}} weeks)
    - Recent head circumference: {{recent_head_circumference}}cm
    - Historical head circumference: {{historical_head_circumference}}cm
    - Head circumference increase: {{head_circumference_increase}}cm
    - Change: {{change_percentage}}%
    
    Based on this information, provide a brief, helpful insight about your baby's head circumference increase. 
    Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the growth is appropriate.
    Include 1-2 actionable recommendations if appropriate.
    Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations).
    Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider.
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_head_circumference, historical_head_circumference, head_circumference_increase, change_percentage"
  contextual_thresholds: "significant_change: 10%, alert_threshold: 20%"
  actionable_recommendations: "Continue monitoring head circumference regularly, Consult with a healthcare provider if concerned about head growth"
  priority_scoring: "severity: medium, urgency: medium, impact: high"

# Health Templates
temperature_fluctuation:
  template: |
    You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.
    
    Baby Information:
    - Name: {{baby_name}}
    - Age: {{baby_age}} ({{baby_age_weeks}} weeks)
    - Recent temperature: {{recent_temperature}}°C
    - Historical temperature: {{historical_temperature}}°C
    - Temperature difference: {{temperature_difference}}°C
    - Change: {{change_percentage}}%
    
    Based on this information, provide a brief, helpful insight about your baby's temperature fluctuation. 
    Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the change is concerning.
    Include 1-2 actionable recommendations if appropriate.
    Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations).
    Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider.
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_temperature, historical_temperature, temperature_difference, change_percentage"
  contextual_thresholds: "significant_change: 5%, alert_threshold: 10%"
  actionable_recommendations: "Continue monitoring temperature regularly, Consult with a healthcare provider if temperature exceeds 38°C"
  priority_scoring: "severity: high, urgency: high, impact: high"

symptom_pattern:
  template: |
    You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.
    
    Baby Information:
    - Name: {{baby_name}}
    - Age: {{baby_age}} ({{baby_age_weeks}} weeks)
    - Recent symptom count: {{recent_symptom_count}}
    - Historical symptom count: {{historical_symptom_count}}
    - Symptom difference: {{symptom_difference}}
    - Change: {{change_percentage}}%
    
    Based on this information, provide a brief, helpful insight about your baby's symptom patterns. 
    Focus on pattern recognition and what it might mean. Consider the baby's age when determining if the change is concerning.
    Include 1-2 actionable recommendations if appropriate.
    Keep your response concise (2-3 sentences for the insight, 1-2 sentences for recommendations).
    Avoid medical diagnoses or alarmist language. When in doubt, suggest consulting a healthcare provider.
  dynamic_variables: "baby_name, baby_age, baby_age_weeks, recent_symptom_count, historical_symptom_count, symptom_difference, change_percentage"
  contextual_thresholds: "significant_change: 20%, alert_threshold: 40%"
  actionable_recommendations: "Continue monitoring symptoms, Consult with a healthcare provider if symptoms persist or worsen"
  priority_scoring: "severity: high, urgency: medium, impact: high"
