//
//  MigrationPlan.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

enum BabyPulseSchemaMigrationPlan: SchemaMigrationPlan {
    static var schemas: [VersionedSchema.Type] = [
        BabyPulseSchemaV1.self,
        BabyPulseSchemaV2.self
    ]

    static var stages: [MigrationStage] = [
        MigrationStage.custom(
            fromVersion: BabyPulseSchemaV1.self,
            toVersion: BabyPulseSchemaV2.self,
            willMigrate: { context in
                print("Starting migration from V1 to V2")
            },
            didMigrate: { context in
                print("Completed migration from V1 to V2")
            }
        )
    ]
}

// Original schema
enum BabyPulseSchemaV1: VersionedSchema {
    static var versionIdentifier = Schema.Version(1, 0, 0)

    static var models: [any PersistentModel.Type] = [
        Baby.self,
        FeedingEntry.self,
        DiaperEntry.self,
        SleepEntry.self,
        GrowthEntry.self,
        HealthEntry.self,
        UserPreferences.self,
        Insight.self,
        ChatThread.self,
        ChatMessage.self,
        UserAccount.self
    ]
}

// Updated schema with mandatory createdAt field
enum BabyPulseSchemaV2: VersionedSchema {
    static var versionIdentifier = Schema.Version(2, 0, 0)

    static var models: [any PersistentModel.Type] = [
        Baby.self,
        FeedingEntry.self,
        DiaperEntry.self,
        SleepEntry.self,
        GrowthEntry.self,
        HealthEntry.self,
        UserPreferences.self,
        Insight.self,
        ChatThread.self,
        ChatMessage.self,
        UserAccount.self
    ]

    static let schema = Schema(models)
}
