//
//  UserPreferencesManager.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI
import SwiftData
import Combine

/// Manager for accessing and updating user preferences throughout the app
class UserPreferencesManager: ObservableObject {
    // Singleton instance
    static let shared = UserPreferencesManager()

    // Published properties that views can observe
    @Published var unitSystem: UnitSystem = .metric
    @Published var darkModeEnabled: Bool = false
    @Published var notificationsEnabled: Bool = true

    // Model context for database access
    private var modelContext: ModelContext?

    // Cancellables for notification subscriptions
    private var cancellables = Set<AnyCancellable>()

    private init() {
        // Subscribe to unit system change notifications
        NotificationCenter.default.publisher(for: .unitSystemChanged)
            .sink { [weak self] notification in
                if let unitSystem = notification.object as? UnitSystem {
                    self?.unitSystem = unitSystem
                }
            }
            .store(in: &cancellables)
    }

    /// Initialize the manager with a model context
    /// - Parameter modelContext: The SwiftData model context
    func initialize(with modelContext: ModelContext) {
        self.modelContext = modelContext
        loadPreferences()
    }

    /// Load user preferences from the database
    private func loadPreferences() {
        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    // Update published properties on the main thread
                    await MainActor.run {
                        self.unitSystem = userPreferences.unitSystem
                        self.darkModeEnabled = userPreferences.darkModeEnabled
                        self.notificationsEnabled = userPreferences.notificationsEnabled
                    }
                }
            } catch {
                print("Error loading user preferences: \(error)")
            }
        }
    }

    /// Update the unit system preference
    /// - Parameter unitSystem: The new unit system
    func updateUnitSystem(_ unitSystem: UnitSystem) {
        guard let modelContext = modelContext else { return }

        // Update the published property immediately to provide responsive UI
        self.unitSystem = unitSystem

        // Notify observers immediately to update all views
        NotificationCenter.default.post(
            name: .unitSystemChanged,
            object: unitSystem
        )

        // Update the database
        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    // Update the model
                    await MainActor.run {
                        userPreferences.unitSystem = unitSystem
                        userPreferences.updatedAt = Date()
                    }

                    // Save the changes
                    do {
                        try modelContext.save()
                    } catch {
                        // Handle file provider errors gracefully
                        print("Warning: Could not save unit system change to database: \(error)")
                        // The UI is already updated, so the user experience is not affected
                    }
                }
            } catch {
                print("Error updating unit system: \(error)")
                // Even if there's an error, the UI is already updated
            }
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    /// Notification sent when the unit system changes
    static let unitSystemChanged = Notification.Name("unitSystemChanged")
}
