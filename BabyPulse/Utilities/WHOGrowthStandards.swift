//
//  WHOGrowthStandards.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI

/// Utility class for WHO growth standards
struct WHOGrowthStandards {

    /// Growth curve percentiles to track
    enum Percentile: Int, CaseIterable {
        case p3 = 3      // Lower bound (approximately -2 SD)
        case p50 = 50    // Median
        case p97 = 97    // Upper bound (approximately +2 SD)

        var description: String {
            switch self {
            case .p3:
                return "3rd percentile"
            case .p50:
                return "50th percentile (median)"
            case .p97:
                return "97th percentile"
            }
        }

        var color: Color {
            switch self {
            case .p3:
                return Color(hex: "FF6B6B").opacity(0.8) // Red for lower bound
            case .p50:
                return Color(hex: "4A90E2").opacity(0.8) // Blue for median
            case .p97:
                return Color(hex: "50C878").opacity(0.8) // Green for upper bound
            }
        }

        var lineStyle: StrokeStyle {
            switch self {
            case .p3:
                return StrokeStyle(lineWidth: 1.5, dash: [4, 4])
            case .p50:
                return StrokeStyle(lineWidth: 1.5, dash: [6, 3, 1, 3]) // dash-dot pattern
            case .p97:
                return StrokeStyle(lineWidth: 1.5, dash: [2, 2])
            }
        }
    }

    /// Gender of the baby
    enum Gender {
        case male
        case female
    }

    /// Get weight-for-age data for a specific gender and percentile
    /// - Parameters:
    ///   - gender: Gender of the baby
    ///   - percentile: Percentile to retrieve
    ///   - ageInMonths: Age range to retrieve (0 to specified age)
    /// - Returns: Array of (age in months, weight in kg) tuples
    static func weightForAge(gender: Gender, percentile: Percentile, maxAgeInMonths: Int) -> [(age: Int, weight: Double)] {
        // Get the appropriate data based on gender and percentile
        let data: [Double]

        switch (gender, percentile) {
        case (.male, .p3):
            data = maleWeightP3
        case (.male, .p50):
            data = maleWeightP50
        case (.male, .p97):
            data = maleWeightP97
        case (.female, .p3):
            data = femaleWeightP3
        case (.female, .p50):
            data = femaleWeightP50
        case (.female, .p97):
            data = femaleWeightP97
        }

        // Convert to age-weight tuples, limiting to the specified max age
        return data.enumerated()
            .map { (age: $0, weight: $1) }
            .filter { $0.age <= maxAgeInMonths }
    }

    /// Get length/height-for-age data for a specific gender and percentile
    /// - Parameters:
    ///   - gender: Gender of the baby
    ///   - percentile: Percentile to retrieve
    ///   - ageInMonths: Age range to retrieve (0 to specified age)
    /// - Returns: Array of (age in months, height in cm) tuples
    static func heightForAge(gender: Gender, percentile: Percentile, maxAgeInMonths: Int) -> [(age: Int, height: Double)] {
        // Get the appropriate data based on gender and percentile
        let data: [Double]

        switch (gender, percentile) {
        case (.male, .p3):
            data = maleHeightP3
        case (.male, .p50):
            data = maleHeightP50
        case (.male, .p97):
            data = maleHeightP97
        case (.female, .p3):
            data = femaleHeightP3
        case (.female, .p50):
            data = femaleHeightP50
        case (.female, .p97):
            data = femaleHeightP97
        }

        // Convert to age-height tuples, limiting to the specified max age
        return data.enumerated()
            .map { (age: $0, height: $1) }
            .filter { $0.age <= maxAgeInMonths }
    }

    /// Get head circumference-for-age data for a specific gender and percentile
    /// - Parameters:
    ///   - gender: Gender of the baby
    ///   - percentile: Percentile to retrieve
    ///   - ageInMonths: Age range to retrieve (0 to specified age)
    /// - Returns: Array of (age in months, head circumference in cm) tuples
    static func headCircumferenceForAge(gender: Gender, percentile: Percentile, maxAgeInMonths: Int) -> [(age: Int, headCircumference: Double)] {
        // Get the appropriate data based on gender and percentile
        let data: [Double]

        switch (gender, percentile) {
        case (.male, .p3):
            data = maleHeadCircumferenceP3
        case (.male, .p50):
            data = maleHeadCircumferenceP50
        case (.male, .p97):
            data = maleHeadCircumferenceP97
        case (.female, .p3):
            data = femaleHeadCircumferenceP3
        case (.female, .p50):
            data = femaleHeadCircumferenceP50
        case (.female, .p97):
            data = femaleHeadCircumferenceP97
        }

        // Convert to age-head circumference tuples, limiting to the specified max age
        return data.enumerated()
            .map { (age: $0, headCircumference: $1) }
            .filter { $0.age <= maxAgeInMonths }
    }

    // MARK: - WHO Growth Standards Data

    // Weight-for-age data (kg) for boys from 0-24 months
    private static let maleWeightP3: [Double] = [
        2.9, 3.9, 4.9, 5.7, 6.2, 6.7, 7.1, 7.4, 7.7, 8.0, 8.2, 8.4, 8.6, 8.8, 9.0, 9.2, 9.4, 9.6, 9.8, 10.0, 10.1, 10.3, 10.5, 10.7, 10.8
    ]

    private static let maleWeightP50: [Double] = [
        3.3, 4.5, 5.6, 6.4, 7.0, 7.5, 7.9, 8.3, 8.6, 8.9, 9.2, 9.4, 9.6, 9.9, 10.1, 10.3, 10.5, 10.7, 10.9, 11.1, 11.3, 11.5, 11.8, 12.0, 12.2
    ]

    private static let maleWeightP97: [Double] = [
        4.4, 5.8, 7.1, 8.0, 8.7, 9.3, 9.8, 10.3, 10.7, 11.0, 11.4, 11.7, 12.0, 12.3, 12.6, 12.9, 13.2, 13.5, 13.7, 14.0, 14.3, 14.6, 14.9, 15.2, 15.4
    ]

    // Weight-for-age data (kg) for girls from 0-24 months
    private static let femaleWeightP3: [Double] = [
        2.8, 3.6, 4.5, 5.1, 5.6, 6.1, 6.5, 6.8, 7.0, 7.3, 7.5, 7.7, 7.9, 8.1, 8.3, 8.5, 8.7, 8.9, 9.1, 9.2, 9.4, 9.6, 9.8, 10.0, 10.2
    ]

    private static let femaleWeightP50: [Double] = [
        3.2, 4.2, 5.1, 5.8, 6.4, 6.9, 7.3, 7.6, 7.9, 8.2, 8.5, 8.7, 8.9, 9.2, 9.4, 9.6, 9.8, 10.0, 10.2, 10.4, 10.6, 10.9, 11.1, 11.3, 11.5
    ]

    private static let femaleWeightP97: [Double] = [
        4.2, 5.5, 6.6, 7.5, 8.2, 8.8, 9.3, 9.8, 10.2, 10.5, 10.9, 11.2, 11.5, 11.8, 12.1, 12.4, 12.6, 12.9, 13.2, 13.5, 13.7, 14.0, 14.3, 14.6, 14.8
    ]

    // Length/height-for-age data (cm) for boys from 0-24 months
    private static let maleHeightP3: [Double] = [
        46.3, 51.1, 55.3, 58.6, 61.2, 63.3, 65.0, 66.6, 68.0, 69.4, 70.7, 72.0, 73.2, 74.4, 75.5, 76.6, 77.6, 78.6, 79.6, 80.5, 81.4, 82.3, 83.1, 83.9, 84.8
    ]

    private static let maleHeightP50: [Double] = [
        49.9, 54.7, 58.9, 62.2, 64.9, 67.0, 68.8, 70.4, 71.9, 73.3, 74.5, 75.7, 76.9, 78.0, 79.1, 80.2, 81.2, 82.3, 83.2, 84.2, 85.1, 86.0, 86.9, 87.8, 88.7
    ]

    private static let maleHeightP97: [Double] = [
        53.4, 58.3, 62.5, 65.9, 68.6, 70.8, 72.7, 74.5, 76.0, 77.5, 78.9, 80.2, 81.5, 82.7, 83.9, 85.0, 86.2, 87.3, 88.3, 89.3, 90.3, 91.3, 92.3, 93.2, 94.1
    ]

    // Length/height-for-age data (cm) for girls from 0-24 months
    private static let femaleHeightP3: [Double] = [
        45.6, 50.0, 53.7, 56.7, 59.1, 61.2, 62.9, 64.5, 66.0, 67.4, 68.7, 70.0, 71.3, 72.5, 73.7, 74.8, 76.0, 77.0, 78.1, 79.1, 80.1, 81.0, 82.0, 82.9, 83.8
    ]

    private static let femaleHeightP50: [Double] = [
        49.1, 53.7, 57.4, 60.3, 62.7, 64.8, 66.6, 68.2, 69.8, 71.3, 72.6, 74.0, 75.2, 76.5, 77.7, 78.9, 80.0, 81.1, 82.2, 83.2, 84.2, 85.2, 86.2, 87.1, 88.1
    ]

    private static let femaleHeightP97: [Double] = [
        52.7, 57.4, 61.1, 64.0, 66.4, 68.5, 70.3, 72.0, 73.5, 75.0, 76.4, 77.8, 79.2, 80.5, 81.7, 83.0, 84.2, 85.4, 86.5, 87.7, 88.8, 89.9, 90.9, 91.9, 92.9
    ]

    // Head circumference-for-age data (cm) for boys from 0-24 months
    private static let maleHeadCircumferenceP3: [Double] = [
        32.4, 35.4, 37.2, 38.6, 39.7, 40.6, 41.3, 41.9, 42.4, 42.9, 43.3, 43.7, 44.0, 44.2, 44.4, 44.7, 44.9, 45.0, 45.2, 45.3, 45.5, 45.6, 45.8, 45.9, 46.0
    ]

    private static let maleHeadCircumferenceP50: [Double] = [
        34.5, 37.3, 39.1, 40.5, 41.6, 42.6, 43.3, 44.0, 44.5, 45.0, 45.4, 45.8, 46.1, 46.3, 46.6, 46.8, 47.0, 47.2, 47.4, 47.5, 47.7, 47.8, 48.0, 48.1, 48.3
    ]

    private static let maleHeadCircumferenceP97: [Double] = [
        36.6, 39.2, 41.0, 42.4, 43.6, 44.5, 45.3, 46.0, 46.6, 47.1, 47.5, 47.9, 48.2, 48.5, 48.7, 49.0, 49.2, 49.4, 49.6, 49.7, 49.9, 50.1, 50.2, 50.4, 50.5
    ]

    // Head circumference-for-age data (cm) for girls from 0-24 months
    private static let femaleHeadCircumferenceP3: [Double] = [
        31.9, 34.6, 36.3, 37.5, 38.5, 39.3, 40.0, 40.5, 41.0, 41.4, 41.8, 42.1, 42.4, 42.7, 42.9, 43.1, 43.3, 43.5, 43.6, 43.8, 43.9, 44.1, 44.2, 44.3, 44.5
    ]

    private static let femaleHeadCircumferenceP50: [Double] = [
        33.9, 36.5, 38.3, 39.5, 40.6, 41.5, 42.2, 42.8, 43.3, 43.8, 44.2, 44.5, 44.8, 45.0, 45.3, 45.5, 45.7, 45.9, 46.1, 46.2, 46.4, 46.6, 46.7, 46.9, 47.0
    ]

    private static let femaleHeadCircumferenceP97: [Double] = [
        35.9, 38.5, 40.3, 41.6, 42.7, 43.6, 44.4, 45.1, 45.6, 46.1, 46.6, 46.9, 47.3, 47.5, 47.8, 48.0, 48.2, 48.4, 48.6, 48.8, 49.0, 49.2, 49.3, 49.5, 49.7
    ]
}
