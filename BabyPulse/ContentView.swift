//
//  ContentView.swift
//  BabyPulse
//
//  Created by <PERSON> on 4/22/25.
//

import SwiftUI
import SwiftData

#if DEBUG
import Foundation
#endif

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var userPreferences: [UserPreferences]
    @State private var onboardingCompleted = false
    @State private var showingPreferencesAfterOnboarding = false

    var body: some View {
        Group {
            if shouldShowOnboarding {
                OnboardingView(onboardingCompleted: $onboardingCompleted)
            } else {
                MainTabView()
                    .sheet(isPresented: $showingPreferencesAfterOnboarding) {
                        PostOnboardingPreferencesView(isPresented: $showingPreferencesAfterOnboarding)
                    }
            }
        }
        .onAppear {
            checkOnboardingStatus()
        }
        .onChange(of: onboardingCompleted) { _, newValue in
            if newValue {
                checkOnboardingStatus()
                // Show preferences sheet after onboarding is complete
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showingPreferencesAfterOnboarding = true
                }
            }
        }
        #if DEBUG
//        .toolbar {
//            ToolbarItemGroup(placement: .bottomBar) {
//                Spacer()
//                // Testing functions have been moved to Settings → Developer Tools
//            }
//        }
        #endif
    }

    private var shouldShowOnboarding: Bool {
        return userPreferences.isEmpty || !(userPreferences.first?.onboardingCompleted ?? false)
    }

    private func checkOnboardingStatus() {
        if userPreferences.isEmpty {
            // No preferences exist yet, onboarding should be shown
            onboardingCompleted = false
        } else {
            // Preferences exist, check if onboarding is completed
            onboardingCompleted = userPreferences.first?.onboardingCompleted ?? false
        }
    }
}

// New component for post-onboarding preferences that were moved out of the main flow
struct PostOnboardingPreferencesView: View {
    @Binding var isPresented: Bool
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @Query private var userPreferences: [UserPreferences]
    
    @State private var notificationsEnabled: Bool = true
    @State private var reminderTime: Date = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
    @State private var darkModeEnabled: Bool = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: BabyPulseLayout.spacingLG) {
                    // Welcome message
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(BabyPulseColors.primary)
                            .padding(.bottom, BabyPulseLayout.spacingSM)
                        
                        Text("You're all set!")
                            .font(BabyPulseTypography.title2())
                            .foregroundColor(BabyPulseColors.text)
                            .multilineTextAlignment(.center)
                        
                        Text("Would you like to customize your experience with a few additional settings?")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(BabyPulseColors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, BabyPulseLayout.paddingMD)
                    }
                    .padding(.vertical, BabyPulseLayout.paddingLG)
                    
                    // Preferences cards
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        // Notifications Card
                        BabyPulseCard(title: "Notifications", icon: "bell.fill", style: .elevated) {
                            VStack(spacing: BabyPulseLayout.spacingMD) {
                                Toggle(isOn: $notificationsEnabled) {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Enable Notifications")
                                            .font(BabyPulseTypography.bodyBold())
                                            .foregroundColor(BabyPulseColors.text)
                                        
                                        Text("Get reminders for important events")
                                            .font(BabyPulseTypography.footnote())
                                            .foregroundColor(BabyPulseColors.textSecondary)
                                    }
                                }
                                .toggleStyle(SwitchToggleStyle(tint: BabyPulseColors.primary))
                                
                                if notificationsEnabled {
                                    VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
                                        Text("Daily Summary Time")
                                            .font(BabyPulseTypography.footnote())
                                            .foregroundColor(BabyPulseColors.textSecondary)
                                        
                                        DatePicker("", selection: $reminderTime, displayedComponents: .hourAndMinute)
                                            .labelsHidden()
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                    .padding(.top, BabyPulseLayout.spacingSM)
                                }
                            }
                        }
                        
                        // Appearance Card
                        BabyPulseCard(title: "Appearance", icon: "paintbrush.fill", style: .elevated) {
                            Toggle(isOn: $darkModeEnabled) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Dark Mode")
                                        .font(BabyPulseTypography.bodyBold())
                                        .foregroundColor(BabyPulseColors.text)
                                    
                                    Text("Use dark theme for the app")
                                        .font(BabyPulseTypography.footnote())
                                        .foregroundColor(BabyPulseColors.textSecondary)
                                }
                            }
                            .toggleStyle(SwitchToggleStyle(tint: BabyPulseColors.primary))
                        }
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                    
                    Spacer()
                    
                    // Action buttons
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        BabyPulseButton(
                            title: "Save Preferences",
                            style: .primary,
                            size: .medium,
                            isFullWidth: true
                        ) {
                            savePreferences()
                            isPresented = false
                        }
                        
                        Button(action: {
                            isPresented = false
                        }) {
                            Text("Skip for now")
                                .font(BabyPulseTypography.body())
                                .foregroundColor(BabyPulseColors.textSecondary)
                                .frame(minHeight: BabyPulseLayout.minTouchTarget)
                        }
                        .accessibilityLabel("Skip additional settings")
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                    .padding(.bottom, BabyPulseLayout.paddingLG)
                }
            }
            .background(BabyPulseColors.background)
            .navigationBarTitle("Additional Settings", displayMode: .inline)
            .navigationBarItems(trailing: Button(action: {
                isPresented = false
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .frame(minWidth: BabyPulseLayout.minTouchTarget, minHeight: BabyPulseLayout.minTouchTarget)
            }
            .accessibilityLabel("Close settings"))
            .onAppear {
                // Load current preferences if they exist
                if let prefs = userPreferences.first {
                    notificationsEnabled = prefs.notificationsEnabled
                    if let time = prefs.reminderTime {
                        reminderTime = time
                    }
                    darkModeEnabled = prefs.darkModeEnabled
                }
            }
        }
    }
    
    private func savePreferences() {
        guard let prefs = userPreferences.first else { return }
        
        prefs.notificationsEnabled = notificationsEnabled
        prefs.reminderTime = notificationsEnabled ? reminderTime : nil
        prefs.darkModeEnabled = darkModeEnabled
        
        do {
            try modelContext.save()
        } catch {
            print("Error saving preferences: \(error)")
        }
    }
}

#Preview {
    ContentView()
        .modelContainer(for: [Baby.self, UserPreferences.self], inMemory: true)
}
