//
//  SettingsViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData
import PhotosUI
import Combine
import UserNotifications

class SettingsViewModel: ObservableObject {
    // MARK: - Properties
    @Published var babies: [Baby] = []
    @Published var selectedBabyID: UUID?
    @Published var unitSystem: UnitSystem = UserPreferencesManager.shared.unitSystem
    @Published var notificationsEnabled: Bool = true
    @Published var reminderTime: Date = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
    @Published var darkModeEnabled: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // Cancellables for notification subscriptions
    private var cancellables = Set<AnyCancellable>()

    // For baby profile editing
    @Published var editingBaby: Baby?
    @Published var showingBabyProfileEdit: Bool = false
    @Published var showingAddBabyProfile: Bool = false
    @Published var tempBabyName: String = ""
    @Published var tempBabyBirthDate: Date = Date()
    @Published var tempBabyGender: Gender = .male
    @Published var tempSelectedPhoto: PhotosPickerItem?
    @Published var tempPhotoData: Data?

    // For validation
    @Published var nameError: String?

    var modelContext: ModelContext?

    // Add a computed property to handle system appearance setting
    private var systemAppearance: Bool? = nil
    func getAppearanceSetting() -> Bool? {
        if darkModeEnabled == true {
            return true
        } else if let systemAppearance = systemAppearance {
            return nil  // System preference
        } else {
            return false
        }
    }

    // Additional notification settings
    @Published var feedingNotificationsEnabled: Bool = false
    @Published var sleepNotificationsEnabled: Bool = false
    @Published var growthNotificationsEnabled: Bool = false
    @Published var healthNotificationsEnabled: Bool = false
    @Published var insightNotificationsEnabled: Bool = false
    @Published var quietHoursEnabled: Bool = false
    @Published var quietHoursStart: Date = Calendar.current.date(from: DateComponents(hour: 22, minute: 0)) ?? Date()
    @Published var quietHoursEnd: Date = Calendar.current.date(from: DateComponents(hour: 7, minute: 0)) ?? Date()
    @Published var hasNotificationPermission: Bool = false

    // Privacy settings
    @Published var analyticsEnabled: Bool = true
    @Published var personalizationEnabled: Bool = true
    @Published var dataSharingEnabled: Bool = false
    @Published var locationEnabled: Bool = false

    // Sync and backup settings
    @Published var autoSyncEnabled: Bool = false
    @Published var syncEnabled: Bool = false
    @Published var hasPremiumAccess: Bool = false
    @Published var isExporting: Bool = false

    // MARK: - Initialization
    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext

        // Subscribe to unit system changes
        NotificationCenter.default.publisher(for: .unitSystemChanged)
            .sink { [weak self] notification in
                if let unitSystem = notification.object as? UnitSystem {
                    DispatchQueue.main.async {
                        self?.unitSystem = unitSystem
                    }
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Data Loading
    func loadData() {
        guard let modelContext = modelContext else { return }

        isLoading = true
        errorMessage = nil

        do {
            // Load babies
            let babyDescriptor = FetchDescriptor<Baby>(sortBy: [SortDescriptor(\.name)])
            babies = try modelContext.fetch(babyDescriptor)

            // Load user preferences
            let preferencesDescriptor = FetchDescriptor<UserPreferences>()
            let preferences = try modelContext.fetch(preferencesDescriptor)

            if let userPreferences = preferences.first {
                selectedBabyID = userPreferences.selectedBabyID
                unitSystem = userPreferences.unitSystem
                notificationsEnabled = userPreferences.notificationsEnabled
                reminderTime = userPreferences.reminderTime ?? Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
                darkModeEnabled = userPreferences.darkModeEnabled
                feedingNotificationsEnabled = userPreferences.feedingNotificationsEnabled
                sleepNotificationsEnabled = userPreferences.sleepNotificationsEnabled
                growthNotificationsEnabled = userPreferences.growthNotificationsEnabled
                healthNotificationsEnabled = userPreferences.healthNotificationsEnabled
                insightNotificationsEnabled = userPreferences.insightNotificationsEnabled
                quietHoursEnabled = userPreferences.quietHoursEnabled

                if let quietStart = userPreferences.quietHoursStart {
                    quietHoursStart = quietStart
                }

                if let quietEnd = userPreferences.quietHoursEnd {
                    quietHoursEnd = quietEnd
                }
            }

            // Load privacy settings
            if let preferences = try? modelContext.fetch(FetchDescriptor<UserPreferences>()).first {
                analyticsEnabled = preferences.analyticsEnabled
                personalizationEnabled = preferences.personalizationEnabled
                dataSharingEnabled = preferences.dataSharingEnabled
                locationEnabled = preferences.locationEnabled
            }
        } catch {
            errorMessage = "Failed to load settings: \(error.localizedDescription)"
            print("Error loading settings: \(error)")
        }

        isLoading = false
    }

    // MARK: - Baby Profile Management
    func selectBaby(id: UUID) {
        guard let modelContext = modelContext else { return }

        // First verify the baby exists and is valid
        if let _ = modelContext.safeFetchBaby(id: id) {
            // Update the published property on the main thread
            DispatchQueue.main.async {
                self.selectedBabyID = id
            }

            // Use a Task to handle the database update asynchronously
            Task {
                do {
                    // Use our safe extension method to update the selected baby ID
                    modelContext.updateSelectedBabyID(id)
                } catch {
                    // Update error state on the main thread
                    await MainActor.run {
                        self.errorMessage = "Failed to select baby: \(error.localizedDescription)"
                    }
                    print("Error selecting baby: \(error)")
                }
            }
        } else {
            // The baby with this ID doesn't exist or is invalid
            errorMessage = "Cannot select baby: Baby not found or invalid"
            print("Error selecting baby: Baby with ID \(id) not found or invalid")

            // Reload data to ensure UI is in sync with database
            loadData()
        }
    }

    func startEditingBaby(_ baby: Baby) {
        editingBaby = baby
        tempBabyName = baby.name
        tempBabyBirthDate = baby.birthDate
        tempBabyGender = baby.gender
        tempPhotoData = baby.photoData
        tempSelectedPhoto = nil
        showingBabyProfileEdit = true
    }

    func startAddingBaby() {
        editingBaby = nil
        tempBabyName = ""
        tempBabyBirthDate = Date()
        tempBabyGender = .male
        tempPhotoData = nil
        tempSelectedPhoto = nil
        showingAddBabyProfile = true
    }

    func saveBabyProfile() {
        guard let modelContext = modelContext else { return }

        // Validate name
        if tempBabyName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            nameError = "Please enter your baby's name"
            return
        } else {
            nameError = nil
        }

        do {
            if let editingBaby = editingBaby {
                // Update existing baby
                editingBaby.name = tempBabyName
                editingBaby.birthDate = tempBabyBirthDate
                editingBaby.gender = tempBabyGender
                if let photoData = tempPhotoData {
                    editingBaby.photoData = photoData
                }
                editingBaby.updatedAt = Date()
            } else {
                // Create new baby
                let newBaby = Baby(
                    name: tempBabyName,
                    birthDate: tempBabyBirthDate,
                    gender: tempBabyGender,
                    photoData: tempPhotoData
                )
                modelContext.insert(newBaby)

                // If this is the first baby, select it
                if babies.isEmpty {
                    selectedBabyID = newBaby.id
                    updateSelectedBabyID(newBaby.id)
                }
            }

            try modelContext.save()
            loadData() // Reload data to update the UI

            // Reset state
            showingBabyProfileEdit = false
            showingAddBabyProfile = false
            editingBaby = nil
        } catch {
            errorMessage = "Failed to save baby profile: \(error.localizedDescription)"
            print("Error saving baby profile: \(error)")
        }
    }

    func deleteBaby(_ baby: Baby) {
        guard let modelContext = modelContext else { return }

        do {
            // If deleting the selected baby, select another one if available
            if baby.id == selectedBabyID {
                // Find another baby that's not the one being deleted
                let otherBaby = babies.first(where: { $0.id != baby.id })
                if let otherBaby = otherBaby {
                    // Update our local state
                    selectedBabyID = otherBaby.id
                    // Use the safe method to update the database
                    modelContext.updateSelectedBabyID(otherBaby.id)
                } else {
                    // No other babies, clear selection
                    selectedBabyID = nil
                    modelContext.updateSelectedBabyID(nil)
                }
            }

            // Store baby ID before deletion for notification
            let deletedBabyID = baby.id

            // Delete the baby
            modelContext.delete(baby)
            try modelContext.save()

            // Post notification that baby was deleted
            NotificationCenter.default.post(
                name: .babyDeleted,
                object: deletedBabyID
            )

            // Reload data to update the UI
            // This ensures we're not holding references to deleted objects
            loadData()
        } catch {
            errorMessage = "Failed to delete baby profile: \(error.localizedDescription)"
            print("Error deleting baby profile: \(error)")
        }
    }

    // MARK: - Preferences Management
    func updateUnitSystem(_ unitSystem: UnitSystem) {
        // Update the published property on the main thread
        DispatchQueue.main.async {
            self.unitSystem = unitSystem
        }

        // Use the UserPreferencesManager to update the unit system
        // This will handle database updates and notifications
        do {
            UserPreferencesManager.shared.updateUnitSystem(unitSystem)
        } catch {
            // Handle errors gracefully - don't propagate to UI
            print("Warning: Error in SettingsViewModel.updateUnitSystem: \(error)")
            // The UI is already updated, so the user experience is not affected
        }
    }

    func updateNotificationsEnabled(_ enabled: Bool) {
        guard let modelContext = modelContext else { return }

        // Update the published property
        notificationsEnabled = enabled

        // Request notification permission if enabling
        if enabled {
            requestNotificationPermission()
        }

        // Save to database
        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        userPreferences.notificationsEnabled = enabled

                        // If disabling, disable all notification types
                        if !enabled {
                            userPreferences.feedingNotificationsEnabled = false
                            userPreferences.sleepNotificationsEnabled = false
                            userPreferences.growthNotificationsEnabled = false
                            userPreferences.healthNotificationsEnabled = false
                            userPreferences.insightNotificationsEnabled = false

                            // Update published properties
                            self.feedingNotificationsEnabled = false
                            self.sleepNotificationsEnabled = false
                            self.growthNotificationsEnabled = false
                            self.healthNotificationsEnabled = false
                            self.insightNotificationsEnabled = false
                        }

                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                } else {
                    // Create preferences if they don't exist
                    let newPreferences = UserPreferences()
                    newPreferences.notificationsEnabled = enabled
                    modelContext.insert(newPreferences)
                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to save notification settings: \(error.localizedDescription)"
                }
                print("Error saving notification settings: \(error)")
            }
        }
    }

    func updateReminderTime(_ time: Date) {
        guard let modelContext = modelContext else { return }

        // Update the published property on the main thread
        DispatchQueue.main.async {
            self.reminderTime = time
        }

        // Use a Task to handle the database update asynchronously
        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    // Update the model on the main thread
                    await MainActor.run {
                        userPreferences.reminderTime = self.notificationsEnabled ? time : nil
                        userPreferences.updatedAt = Date()
                    }

                    // Save the changes
                    try modelContext.save()
                }
            } catch {
                // Update error state on the main thread
                await MainActor.run {
                    self.errorMessage = "Failed to update reminder time: \(error.localizedDescription)"
                }
                print("Error updating reminder time: \(error)")
            }
        }
    }

    func updateDarkModeEnabled(_ value: Bool?) {
        guard let modelContext = modelContext else { return }

        do {
            // If value is nil, we're using system preference
            if value == nil {
                systemAppearance = nil
                darkModeEnabled = false
            } else {
                darkModeEnabled = value == true
                systemAppearance = nil
            }

            // Update user preferences with the new setting
            if let preferences = try modelContext.fetch(FetchDescriptor<UserPreferences>()).first {
                // Update existing preferences
                preferences.darkModeEnabled = darkModeEnabled
                preferences.updatedAt = Date()
            } else {
                // Create new preferences if none exist
                let newPreferences = UserPreferences()
                newPreferences.darkModeEnabled = darkModeEnabled
                modelContext.insert(newPreferences)
            }

            try modelContext.save()

            // Post notification to update UI throughout the app
            NotificationCenter.default.post(
                name: .darkModeChanged,
                object: value
            )
        } catch {
            errorMessage = "Failed to update appearance settings: \(error.localizedDescription)"
            print("Error updating appearance settings: \(error)")
        }
    }

    private func updateSelectedBabyID(_ id: UUID?) {
        guard let modelContext = modelContext else { return }

        // Use a Task to handle the database update asynchronously
        Task {
            do {
                // Use our safe extension method to update the selected baby ID
                modelContext.updateSelectedBabyID(id)
            } catch {
                // Update error state on the main thread
                await MainActor.run {
                    self.errorMessage = "Failed to update selected baby: \(error.localizedDescription)"
                }
                print("Error updating selected baby: \(error)")
            }
        }
    }

    // MARK: - Photo Loading
    func loadPhotoData() async {
        if let selectedPhoto = tempSelectedPhoto {
            do {
                if let data = try await selectedPhoto.loadTransferable(type: Data.self) {
                    await MainActor.run { [weak self] in
                        self?.tempPhotoData = data
                    }
                }
            } catch {
                print("Error loading photo: \(error)")
            }
        }
    }

    func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.hasNotificationPermission = (settings.authorizationStatus == .authorized ||
                                                settings.authorizationStatus == .provisional)
            }
        }
    }

    // Helper for requesting notification permission
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { success, error in
            DispatchQueue.main.async {
                self.hasNotificationPermission = success
                if let error = error {
                    self.errorMessage = "Failed to request notification permission: \(error.localizedDescription)"
                }
            }
        }
    }

    // Observe changes to specific notification settings
    func updateSpecificNotificationSetting() {
        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        // Update all notification settings
                        userPreferences.feedingNotificationsEnabled = feedingNotificationsEnabled
                        userPreferences.sleepNotificationsEnabled = sleepNotificationsEnabled
                        userPreferences.growthNotificationsEnabled = growthNotificationsEnabled
                        userPreferences.healthNotificationsEnabled = healthNotificationsEnabled
                        userPreferences.insightNotificationsEnabled = insightNotificationsEnabled
                        userPreferences.quietHoursEnabled = quietHoursEnabled
                        userPreferences.quietHoursStart = quietHoursStart
                        userPreferences.quietHoursEnd = quietHoursEnd
                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to save notification settings: \(error.localizedDescription)"
                }
                print("Error saving notification settings: \(error)")
            }
        }
    }

    // Update privacy settings
    func updatePrivacySettings() {
        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        userPreferences.analyticsEnabled = analyticsEnabled
                        userPreferences.personalizationEnabled = personalizationEnabled
                        userPreferences.dataSharingEnabled = dataSharingEnabled
                        userPreferences.locationEnabled = locationEnabled
                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                } else {
                    // Create preferences if they don't exist
                    let newPreferences = UserPreferences()
                    newPreferences.analyticsEnabled = analyticsEnabled
                    newPreferences.personalizationEnabled = personalizationEnabled
                    newPreferences.dataSharingEnabled = dataSharingEnabled
                    newPreferences.locationEnabled = locationEnabled
                    modelContext.insert(newPreferences)
                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to update privacy settings: \(error.localizedDescription)"
                }
                print("Error updating privacy settings: \(error)")
            }
        }
    }

    // Delete all data
    @MainActor
    func deleteAllData() async {
        guard let modelContext = modelContext else { return }

        do {
            // First export data if needed
            // await exportAllData()

            // Delete all babies
            let babyDescriptor = FetchDescriptor<Baby>()
            let babies = try modelContext.fetch(babyDescriptor)
            for baby in babies {
                modelContext.delete(baby)
            }

            // Delete all entries (in a real app, you would delete all entry types)
            // This is pseudo-code - you would need to delete all entry types
            // let entriesDescriptor = FetchDescriptor<BaseEntry>()
            // let entries = try modelContext.fetch(entriesDescriptor)
            // for entry in entries {
            //     modelContext.delete(entry)
            // }

            // Keep the user preferences but reset any personal settings
            if let preferences = try modelContext.fetch(FetchDescriptor<UserPreferences>()).first {
                preferences.selectedBabyID = nil
                preferences.updatedAt = Date()
            }

            try modelContext.save()

            errorMessage = nil
        } catch {
            errorMessage = "Failed to delete data: \(error.localizedDescription)"
        }
    }

    // MARK: - Sync and Backup Methods

    func updateAutoSync(_ enabled: Bool) {
        autoSyncEnabled = enabled

        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        // Update the auto sync setting in memory since the property doesn't exist in the model
                        self.autoSyncEnabled = enabled
                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to update auto sync setting: \(error.localizedDescription)"
                }
                print("Error updating auto sync: \(error)")
            }
        }
    }

    func enableBackup() {
        syncEnabled = true

        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        // Update the backup setting in memory since the property doesn't exist in the model
                        self.syncEnabled = true
                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to enable backup: \(error.localizedDescription)"
                }
                print("Error enabling backup: \(error)")
            }
        }
    }

    func disableBackup() {
        syncEnabled = false
        autoSyncEnabled = false

        guard let modelContext = modelContext else { return }

        Task {
            do {
                let preferencesDescriptor = FetchDescriptor<UserPreferences>()
                let preferences = try modelContext.fetch(preferencesDescriptor)

                if let userPreferences = preferences.first {
                    await MainActor.run {
                        // Update the settings in memory since the properties don't exist in the model
                        self.syncEnabled = false
                        self.autoSyncEnabled = false
                        userPreferences.updatedAt = Date()
                    }

                    try modelContext.save()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to disable backup: \(error.localizedDescription)"
                }
                print("Error disabling backup: \(error)")
            }
        }
    }

    // MARK: - Export Data Functionality

    func exportAllData() async -> URL? {
        guard let modelContext = modelContext else { return nil }

        await MainActor.run {
            isExporting = true
        }

        do {
            // Create export data structure
            var exportData: [String: Any] = [:]

            // Export babies
            let babyDescriptor = FetchDescriptor<Baby>()
            let babies = try modelContext.fetch(babyDescriptor)
            exportData["babies"] = babies.map { baby in
                [
                    "id": baby.id.uuidString,
                    "name": baby.name,
                    "birthDate": baby.birthDate.timeIntervalSince1970,
                    "gender": baby.gender.rawValue,
                    "createdAt": baby.createdAt.timeIntervalSince1970,
                    "updatedAt": baby.updatedAt.timeIntervalSince1970
                ]
            }

            // Export feeding entries
            let feedingDescriptor = FetchDescriptor<FeedingEntry>()
            let feedingEntries = try modelContext.fetch(feedingDescriptor)
            exportData["feedingEntries"] = feedingEntries.map { entry in
                [
                    "id": entry.id.uuidString,
                    "babyId": entry.baby?.id.uuidString ?? "",
                    "timestamp": entry.timestamp.timeIntervalSince1970,
                    "type": entry.type.rawValue,
                    "amount": entry.volume ?? 0,
                    "duration": entry.duration ?? 0,
                    "notes": entry.notes ?? ""
                ]
            }

            // Export sleep entries
            let sleepDescriptor = FetchDescriptor<SleepEntry>()
            let sleepEntries = try modelContext.fetch(sleepDescriptor)
            exportData["sleepEntries"] = sleepEntries.map { entry in
                [
                    "id": entry.id.uuidString,
                    "babyId": entry.baby?.id.uuidString ?? "",
                    "startTime": entry.timestamp.timeIntervalSince1970,
                    "endTime": entry.endTime?.timeIntervalSince1970 ?? 0,
                    "notes": entry.notes ?? ""
                ]
            }

            // Export diaper entries
            let diaperDescriptor = FetchDescriptor<DiaperEntry>()
            let diaperEntries = try modelContext.fetch(diaperDescriptor)
            exportData["diaperEntries"] = diaperEntries.map { entry in
                [
                    "id": entry.id.uuidString,
                    "babyId": entry.baby?.id.uuidString ?? "",
                    "timestamp": entry.timestamp.timeIntervalSince1970,
                    "type": entry.type.rawValue,
                    "notes": entry.notes ?? ""
                ]
            }

            // Export growth entries
            let growthDescriptor = FetchDescriptor<GrowthEntry>()
            let growthEntries = try modelContext.fetch(growthDescriptor)
            exportData["growthEntries"] = growthEntries.map { entry in
                [
                    "id": entry.id.uuidString,
                    "babyId": entry.baby?.id.uuidString ?? "",
                    "timestamp": entry.timestamp.timeIntervalSince1970,
                    "weight": entry.weight ?? 0,
                    "height": entry.height ?? 0,
                    "headCircumference": entry.headCircumference ?? 0,
                    "notes": entry.notes ?? ""
                ]
            }

            // Export health entries
            let healthDescriptor = FetchDescriptor<HealthEntry>()
            let healthEntries = try modelContext.fetch(healthDescriptor)
            exportData["healthEntries"] = healthEntries.map { entry in
                [
                    "id": entry.id.uuidString,
                    "babyId": entry.baby?.id.uuidString ?? "",
                    "timestamp": entry.timestamp.timeIntervalSince1970,
                    "type": entry.type.rawValue,
                    "temperature": entry.temperature ?? 0,
                    "notes": entry.notes ?? ""
                ]
            }

            // Convert to JSON
            let jsonData = try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)

            // Create file URL
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
            let fileName = "BabyPulse_Export_\(formatter.string(from: Date())).json"

            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let fileURL = documentsPath.appendingPathComponent(fileName)

            // Write to file
            try jsonData.write(to: fileURL)

            await MainActor.run {
                isExporting = false
            }

            return fileURL

        } catch {
            await MainActor.run {
                isExporting = false
                errorMessage = "Failed to export data: \(error.localizedDescription)"
            }
            print("Error exporting data: \(error)")
            return nil
        }
    }

    // MARK: - Individual Notification Settings Update Methods

    func updateFeedingNotifications(_ enabled: Bool) {
        feedingNotificationsEnabled = enabled
        updateSpecificNotificationSetting()
    }

    func updateSleepNotifications(_ enabled: Bool) {
        sleepNotificationsEnabled = enabled
        updateSpecificNotificationSetting()
    }

    func updateGrowthNotifications(_ enabled: Bool) {
        growthNotificationsEnabled = enabled
        updateSpecificNotificationSetting()
    }

    func updateHealthNotifications(_ enabled: Bool) {
        healthNotificationsEnabled = enabled
        updateSpecificNotificationSetting()
    }

    func updateInsightNotifications(_ enabled: Bool) {
        insightNotificationsEnabled = enabled
        updateSpecificNotificationSetting()
    }

    func updateQuietHours(_ enabled: Bool) {
        quietHoursEnabled = enabled
        updateSpecificNotificationSetting()
    }

    // MARK: - Premium Access Check

    func checkPremiumAccess() {
        // This would typically check with RevenueCat or your subscription service
        // For now, we'll set it based on some criteria
        // You should integrate this with your actual subscription service
        hasPremiumAccess = true // Placeholder - replace with actual premium check
    }
}