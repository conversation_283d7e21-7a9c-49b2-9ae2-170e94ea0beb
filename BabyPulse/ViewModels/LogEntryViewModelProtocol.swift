//
//  LogEntryViewModelProtocol.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI
import SwiftData

/// A protocol defining the common interface for all log entry view models
protocol LogEntryViewModelProtocol: ObservableObject {
    // Common properties
    var timestamp: Date { get set }
    var notes: String { get set }
    var currentBaby: Baby? { get set }
    
    // Category
    var category: LogCategory { get }
    
    // Validation and saving
    func validate() -> Bool
    func save(modelContext: ModelContext) -> Bool
    
    // Baby management
    func setCurrentBaby(_ baby: Baby)
}

/// A protocol for feeding-specific view model functionality
protocol FeedingEntryViewModelProtocol: LogEntryViewModelProtocol {
    var feedingType: FeedingEntry.FeedingType { get set }
    var feedingDuration: Int { get set }
    var feedingVolume: String { get set }
    var bottleContent: FeedingEntry.BottleContent { get set }
    var leftBreast: Bool { get set }
    var rightBreast: Bool { get set }
    var foodItem: String { get set }
    var foodAmount: String { get set }
    var reaction: String { get set }
    var feedingVolumeError: String? { get set }
    var foodItemError: String? { get set }
}

/// A protocol for diaper-specific view model functionality
protocol DiaperEntryViewModelProtocol: LogEntryViewModelProtocol {
    var diaperType: DiaperEntry.DiaperType { get set }
    var poopColor: DiaperEntry.PoopColor { get set }
    var poopConsistency: DiaperEntry.PoopConsistency { get set }
}

/// A protocol for sleep-specific view model functionality
protocol SleepEntryViewModelProtocol: LogEntryViewModelProtocol {
    var sleepStartTime: Date { get set }
    var sleepEndTime: Date { get set }
    var sleepOngoing: Bool { get set }
    var sleepLocation: SleepEntry.SleepLocation { get set }
}

/// A protocol for growth-specific view model functionality
protocol GrowthEntryViewModelProtocol: LogEntryViewModelProtocol {
    var weight: String { get set }
    var height: String { get set }
    var headCircumference: String { get set }
    var weightError: String? { get set }
    var heightError: String? { get set }
    var headCircumferenceError: String? { get set }
}

/// A protocol for health-specific view model functionality
protocol HealthEntryViewModelProtocol: LogEntryViewModelProtocol {
    var healthEntryType: HealthEntry.HealthEntryType { get set }
    var temperature: String { get set }
    var temperatureUnit: HealthEntry.TemperatureUnit { get set }
    var medicationName: String { get set }
    var medicationDosage: String { get set }
    var selectedSymptoms: [HealthEntry.Symptom] { get set }
    var vaccineName: String { get set }
    var appointmentReason: String { get set }
    var appointmentProvider: String { get set }
    var temperatureError: String? { get set }
    var medicationNameError: String? { get set }
    var vaccineNameError: String? { get set }
    var appointmentReasonError: String? { get set }
}

// Note: This file demonstrates how the LogEntryViewModel could be refactored
// using a protocol-oriented approach. The actual implementation would involve
// creating concrete classes for each entry type that conform to these protocols.
