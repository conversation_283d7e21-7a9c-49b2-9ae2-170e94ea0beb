//
//  LogsViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

class LogsViewModel: ObservableObject {
    @Published var selectedDate: Date = Date()
    @Published var logEntries: [TimelineEntry] = []
    @Published var activeFilters: Set<LogCategory> = [.feeding, .diaper, .sleep, .growth, .health]
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var showingDatePicker: Bool = false

    // Chart-related properties
    @Published var selectedTimeSpan: TimeSpan = .daily
    @Published var selectedChartCategory: LogCategory? = nil
    @Published var feedingChartData: FeedingChartData?
    @Published var sleepChartData: SleepChartData?
    @Published var diaperChartData: DiaperChartData?
    @Published var weightChartData: WeightChartData?
    @Published var heightChartData: HeightChartData?
    @Published var headCircumferenceChartData: HeadCircumferenceChartData?
    @Published var categoryPatternData: [LogCategory: PatternData] = [:]

    // Chart period navigation properties
    @Published var chartStartDate: Date = Calendar.current.date(byAdding: .day, value: -6, to: Calendar.current.startOfDay(for: Date()))!
    @Published var chartEndDate: Date = Calendar.current.startOfDay(for: Date())
    @Published var canNavigateChartForward: Bool = false

    // Formatted date string for display
    var formattedSelectedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMMM d"
        return formatter.string(from: selectedDate)
    }

    // Check if selected date is today
    var isToday: Bool {
        return Calendar.current.isDateInToday(selectedDate)
    }

    // Summary text for activity cards
    var feedingSummaryText: String {
        let feedingEntries = logEntries.filter { $0.category == .feeding }
        if feedingEntries.isEmpty {
            return ""
        }
        return "\(feedingEntries.count) feedings today"
    }

    var diaperSummaryText: String {
        let diaperEntries = logEntries.filter { $0.category == .diaper }
        if diaperEntries.isEmpty {
            return ""
        }
        return "\(diaperEntries.count) changes today"
    }

    var sleepSummaryText: String {
        let sleepEntries = logEntries.filter { $0.category == .sleep }
        if sleepEntries.isEmpty {
            return ""
        }

        // Calculate total sleep duration
        let totalMinutes = sleepEntries.compactMap { $0.duration }.reduce(0, +)
        if totalMinutes == 0 {
            return "\(sleepEntries.count) sleep periods"
        }

        let hours = totalMinutes / 60
        let minutes = totalMinutes % 60

        if hours > 0 {
            return "\(hours)h \(minutes)m total"
        } else {
            return "\(minutes)m total"
        }
    }

    // Helper method to get pattern data for a specific category
    func patternData(for category: LogCategory) -> PatternData? {
        return categoryPatternData[category]
    }

    // Check if all chart data is empty
    var isAllChartDataEmpty: Bool {
        let feedingEmpty = feedingChartData?.dataPoints.isEmpty ?? true
        let sleepEmpty = sleepChartData?.dataPoints.isEmpty ?? true
        let diaperEmpty = diaperChartData?.dataPoints.isEmpty ?? true
        let weightEmpty = weightChartData?.dataPoints.isEmpty ?? true
        let heightEmpty = heightChartData?.dataPoints.isEmpty ?? true
        let headCircumferenceEmpty = headCircumferenceChartData?.dataPoints.isEmpty ?? true

        return feedingEmpty && sleepEmpty && diaperEmpty && weightEmpty && heightEmpty && headCircumferenceEmpty
    }

    // Computed property to organize entries by hour for timeline view
    var timeSlots: [(Int, [TimelineEntry])] {
        let entriesByHour = Dictionary(grouping: logEntries) { entry in
            Calendar.current.component(.hour, from: entry.timestamp)
        }

        return entriesByHour.sorted { $0.key > $1.key }
    }

    // Navigation direction enum
    enum NavigationDirection {
        case forward
        case backward
    }

    // Navigate date forward or backward
    func navigateDate(direction: NavigationDirection) {
        switch direction {
        case .forward:
            loadNextDay()
        case .backward:
            loadPreviousDay()
        }
    }

    // Edit an entry
    func editEntry(_ entry: TimelineEntry) {
        // Implementation will depend on your app's navigation and editing flow
        print("Edit entry: \(entry.id)")
    }

    private var modelContext: ModelContext?
    private let calendar = Calendar.current

    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
        loadEntriesForSelectedDate()
    }

    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        loadEntriesForSelectedDate()
    }

    func setTimeSpan(_ timeSpan: TimeSpan) {
        // Only take action if the selected time span has changed
        guard selectedTimeSpan != timeSpan else { return }

        // Update the selected time span
        selectedTimeSpan = timeSpan

        // Set loading state
        isLoading = true

        if timeSpan.isChartView {
            // Reset chart period to current when switching to a chart view
            resetChartPeriodToCurrent()

            // Need to ensure we load chart data with a slight delay to allow UI to update
            DispatchQueue.main.async {
                self.loadDataForCharts()
            }
        } else {
            // When switching to daily view, load the daily entries
            loadEntriesForSelectedDate()
        }
    }

    func loadEntriesForSelectedDate() {
        guard let modelContext = modelContext else {
            errorMessage = "Model context not available"
            return
        }

        isLoading = true
        errorMessage = nil

        // Get start and end of the selected date
        let startOfDay = calendar.startOfDay(for: selectedDate)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!

        // Create a local reference to the model context to avoid capturing self.modelContext
        let context = modelContext

        Task {
            await MainActor.run {
                do {
                    var entries: [TimelineEntry] = []

                    // Fetch feeding entries if filter is active
                    if activeFilters.contains(.feeding) {
                        let feedingDescriptor = FetchDescriptor<FeedingEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let feedings = try context.fetch(feedingDescriptor)

                        entries.append(contentsOf: feedings.map { feeding in
                            // Enhanced TimelineEntry with additional properties
                            TimelineEntry(
                                id: feeding.id,
                                timestamp: feeding.timestamp,
                                category: .feeding,
                                title: getTitleForFeedingType(feeding.type),
                                subtitle: feeding.subtitle,
                                details: feeding.details,
                                originalEntry: feeding,
                                notes: feeding.notes,
                                duration: feeding.type == .breastfeeding ? feeding.duration : nil,
                                tags: []
                            )
                        })
                    }

                    // Fetch diaper entries if filter is active
                    if activeFilters.contains(.diaper) {
                        let diaperDescriptor = FetchDescriptor<DiaperEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let diapers = try context.fetch(diaperDescriptor)

                        entries.append(contentsOf: diapers.map { diaper in
                            TimelineEntry(
                                id: diaper.id,
                                timestamp: diaper.timestamp,
                                category: .diaper,
                                title: diaper.type.description,
                                subtitle: diaper.subtitle,
                                details: diaper.details,
                                originalEntry: diaper,
                                notes: diaper.notes,
                                tags: []
                            )
                        })
                    }

                    // Fetch sleep entries if filter is active
                    if activeFilters.contains(.sleep) {
                        let sleepDescriptor = FetchDescriptor<SleepEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let sleeps = try context.fetch(sleepDescriptor)

                        entries.append(contentsOf: sleeps.map { sleep in
                            // Calculate sleep duration if endTime is available
                            let duration: Int? = sleep.endTime != nil ?
                                Int(sleep.endTime!.timeIntervalSince(sleep.timestamp) / 60) : nil

                            return TimelineEntry(
                                id: sleep.id,
                                timestamp: sleep.timestamp,
                                category: .sleep,
                                title: sleep.endTime == nil ? "Ongoing Sleep" : "Sleep",
                                subtitle: sleep.subtitle,
                                details: sleep.details,
                                originalEntry: sleep,
                                notes: sleep.notes,
                                duration: duration,
                                tags: sleep.location != nil ? [sleep.location!.description] : []
                            )
                        })
                    }

                    // Fetch growth entries if filter is active
                    if activeFilters.contains(.growth) {
                        let growthDescriptor = FetchDescriptor<GrowthEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let growthEntries = try context.fetch(growthDescriptor)

                        entries.append(contentsOf: growthEntries.map { growth in
                            var tags: [String] = []
                            if let weight = growth.weight { tags.append("Weight") }
                            if let height = growth.height { tags.append("Height") }
                            if let headCircumference = growth.headCircumference { tags.append("Head Circumference") }

                            return TimelineEntry(
                                id: growth.id,
                                timestamp: growth.timestamp,
                                category: .growth,
                                title: "Growth Measurement",
                                subtitle: growth.subtitle,
                                details: growth.details,
                                originalEntry: growth,
                                notes: growth.notes,
                                tags: tags
                            )
                        })
                    }

                    // Fetch health entries if filter is active
                    if activeFilters.contains(.health) {
                        let healthDescriptor = FetchDescriptor<HealthEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let healthEntries = try context.fetch(healthDescriptor)

                        entries.append(contentsOf: healthEntries.map { health in
                            var tags: [String] = []
                            if case .medication = health.type, let medicationName = health.medicationName {
                                tags.append(medicationName)
                            }

                            return TimelineEntry(
                                id: health.id,
                                timestamp: health.timestamp,
                                category: .health,
                                title: health.type.description,
                                subtitle: health.subtitle,
                                details: health.details,
                                originalEntry: health,
                                notes: health.notes,
                                tags: tags
                            )
                        })
                    }

                    // Fetch general activities after health entries
                    if activeFilters.contains(.general) { // Ensure we only fetch if filter is active
                        print("LOGSVIEWMODEL: Fetching GeneralActivityEntry items because .general is in activeFilters: \(activeFilters.map { $0.rawValue })") // DEBUG
                        let generalActivityDescriptor = FetchDescriptor<GeneralActivityEntry>(
                            predicate: #Predicate { entry in
                                entry.timestamp >= startOfDay && entry.timestamp < endOfDay
                            },
                            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
                        )
                        let generalActivities = try context.fetch(generalActivityDescriptor)
                        print("LOGSVIEWMODEL: Fetched \(generalActivities.count) GeneralActivityEntry items.") // DEBUG

                        entries.append(contentsOf: generalActivities.map { activity in
                            print("LOGSVIEWMODEL: Mapping GeneralActivityEntry to TimelineEntry: \(activity.activityType.rawValue) - \(activity.customActivityName ?? "N/A")") // DEBUG
                            // Generate title, subtitle, and details for general activity
                            let title = activity.activityType == .other && activity.customActivityName != nil ?
                                activity.customActivityName! : activity.activityType.rawValue

                            let subtitle = activity.durationMinutes != nil ?
                                "\(activity.durationMinutes!) minutes" : ""

                            let details = ""  // No specific details for general activities

                            // Create tags array if needed
                            let tags: [String] = activity.activityType == .other && activity.customActivityName != nil ?
                                [activity.customActivityName!] : [activity.activityType.rawValue]

                            return TimelineEntry(
                                id: activity.id,
                                timestamp: activity.timestamp,
                                category: .general,
                                title: title,
                                subtitle: subtitle,
                                details: details,
                                originalEntry: activity,
                                notes: activity.notes,
                                duration: activity.durationMinutes,
                                tags: tags
                            )
                        })
                    } else {
                        print("LOGSVIEWMODEL: .general filter NOT active. Skipping fetch of GeneralActivityEntry.") // DEBUG
                    }

                    // Sort all entries by timestamp (newest first)
                    DispatchQueue.main.async {
                        self.logEntries = entries.sorted(by: { $0.timestamp > $1.timestamp })
                        print("LOGSVIEWMODEL: logEntries updated. Total count: \(self.logEntries.count). Contains general: \(self.logEntries.contains(where: { $0.category == .general }))") // DEBUG
                        self.isLoading = false
                    }
                } catch {
                    self.errorMessage = "Failed to load entries: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    func loadPreviousDay() {
        selectedDate = calendar.date(byAdding: .day, value: -1, to: selectedDate) ?? selectedDate
        loadEntriesForSelectedDate()
    }

    func loadNextDay() {
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: selectedDate) ?? selectedDate
        if tomorrow <= Date() {
            selectedDate = tomorrow
            loadEntriesForSelectedDate()
        }
    }

    func toggleFilter(_ category: LogCategory) {
        if activeFilters.contains(category) {
            activeFilters.remove(category)
        } else {
            activeFilters.insert(category)
        }
        loadEntriesForSelectedDate()
    }

    func deleteEntry(_ entry: TimelineEntry) {
        guard let modelContext = modelContext else { return }

        if let feedingEntry = entry.originalEntry as? FeedingEntry {
            modelContext.delete(feedingEntry)
        } else if let diaperEntry = entry.originalEntry as? DiaperEntry {
            modelContext.delete(diaperEntry)
        } else if let sleepEntry = entry.originalEntry as? SleepEntry {
            modelContext.delete(sleepEntry)
        } else if let growthEntry = entry.originalEntry as? GrowthEntry {
            modelContext.delete(growthEntry)
        } else if let healthEntry = entry.originalEntry as? HealthEntry {
            modelContext.delete(healthEntry)
        } else if let generalActivity = entry.originalEntry as? GeneralActivityEntry {
            modelContext.delete(generalActivity)
        }

        do {
            try modelContext.save()
            loadEntriesForSelectedDate()
        } catch {
            errorMessage = "Failed to delete entry: \(error.localizedDescription)"
        }
    }

    // MARK: - Chart Data Loading

    func loadDataForCharts() {
        isLoading = true

        // Use chartStartDate and chartEndDate instead of calculating them
        Task {
            await loadFeedingChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadSleepChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadDiaperChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadWeightChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadHeightChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadHeadCircumferenceChartData(startDate: chartStartDate, endDate: chartEndDate)
            await loadPatternData(startDate: chartStartDate, endDate: chartEndDate)

            await MainActor.run {
                isLoading = false
            }
        }
    }

    private func loadFeedingChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        let descriptor = FetchDescriptor<FeedingEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        do {
            let feedings = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Group by day
            let calendar = Calendar.current
            var dailyTotals: [Date: Double] = [:]
            var dailyEntries: [Date: [FeedingEntry]] = [:]

            for feeding in feedings {
                let day = calendar.startOfDay(for: feeding.timestamp)

                // Add to daily entries
                if dailyEntries[day] == nil {
                    dailyEntries[day] = []
                }
                dailyEntries[day]?.append(feeding)

                // Calculate volume for bottle feedings
                if feeding.type == .bottleFeeding, let volume = feeding.volume {
                    dailyTotals[day, default: 0] += volume
                }
            }

            // Create data points
            var dataPoints: [ChartDataPoint] = []
            var currentDate = startDate

            while currentDate < endDate {
                let day = calendar.startOfDay(for: currentDate)
                let value = dailyTotals[day] ?? 0

                dataPoints.append(ChartDataPoint(
                    date: day,
                    value: value,
                    category: .feeding
                ))

                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)
            let minimum = values.isEmpty ? 0 : values.min() ?? 0
            let maximum = values.isEmpty ? 0 : values.max() ?? 0

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.feedingChartData = FeedingChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    average: average,
                    minimum: minimum,
                    maximum: maximum,
                    percentChange: percentChange
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load feeding data: \(error.localizedDescription)"
            }
        }
    }

    private func loadSleepChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        let descriptor = FetchDescriptor<SleepEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        do {
            let sleepEntries = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Group by day
            let calendar = Calendar.current
            var dailyTotals: [Date: Double] = [:]
            var dailyEntries: [Date: [SleepEntry]] = [:]

            for sleep in sleepEntries {
                let day = calendar.startOfDay(for: sleep.timestamp)

                // Add to daily entries
                if dailyEntries[day] == nil {
                    dailyEntries[day] = []
                }
                dailyEntries[day]?.append(sleep)

                // Calculate sleep duration in hours
                if let duration = sleep.calculatedDuration {
                    dailyTotals[day, default: 0] += Double(duration) / 60.0 // Convert minutes to hours
                }
            }

            // Create data points
            var dataPoints: [ChartDataPoint] = []
            var currentDate = startDate

            while currentDate < endDate {
                let day = calendar.startOfDay(for: currentDate)
                let value = dailyTotals[day] ?? 0

                dataPoints.append(ChartDataPoint(
                    date: day,
                    value: value,
                    category: .sleep
                ))

                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)
            let minimum = values.isEmpty ? 0 : values.min() ?? 0
            let maximum = values.isEmpty ? 0 : values.max() ?? 0

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.sleepChartData = SleepChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    average: average,
                    minimum: minimum,
                    maximum: maximum,
                    percentChange: percentChange
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load sleep data: \(error.localizedDescription)"
            }
        }
    }

    private func loadDiaperChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        let descriptor = FetchDescriptor<DiaperEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        do {
            let diaperEntries = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Group by day
            let calendar = Calendar.current
            var dailyTotals: [Date: Int] = [:]
            var dailyWet: [Date: Int] = [:]
            var dailyDirty: [Date: Int] = [:]
            var dailyMixed: [Date: Int] = [:]
            var dailyEntries: [Date: [DiaperEntry]] = [:]

            for diaper in diaperEntries {
                let day = calendar.startOfDay(for: diaper.timestamp)

                // Add to daily entries
                if dailyEntries[day] == nil {
                    dailyEntries[day] = []
                }
                dailyEntries[day]?.append(diaper)

                // Count by type
                dailyTotals[day, default: 0] += 1

                switch diaper.type {
                case .wet:
                    dailyWet[day, default: 0] += 1
                case .dirty:
                    dailyDirty[day, default: 0] += 1
                case .mixed:
                    dailyMixed[day, default: 0] += 1
                }
            }

            // Create data points
            var dataPoints: [ChartDataPoint] = []
            var currentDate = startDate

            while currentDate < endDate {
                let day = calendar.startOfDay(for: currentDate)
                let value = Double(dailyTotals[day] ?? 0)

                dataPoints.append(ChartDataPoint(
                    date: day,
                    value: value,
                    category: .diaper
                ))

                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)

            // Calculate total counts
            let wetCount = dailyWet.values.reduce(0, +)
            let dirtyCount = dailyDirty.values.reduce(0, +)
            let mixedCount = dailyMixed.values.reduce(0, +)

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.diaperChartData = DiaperChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    wetCount: wetCount,
                    dirtyCount: dirtyCount,
                    mixedCount: mixedCount,
                    percentChange: percentChange
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load diaper data: \(error.localizedDescription)"
            }
        }
    }

    private func loadWeightChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        // For growth metrics, we always want to show the full curve from beginning to current date
        // So we ignore the startDate parameter and fetch all data up to endDate
        let descriptor = FetchDescriptor<GrowthEntry>(
            predicate: #Predicate { entry in
                entry.timestamp < endDate && entry.weight != nil
            },
            sortBy: [SortDescriptor(\.timestamp)]
        )

        do {
            let growthEntries = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Create data points directly from growth entries
            var dataPoints: [ChartDataPoint] = []

            for entry in growthEntries {
                if let weight = entry.weight {
                    dataPoints.append(ChartDataPoint(
                        date: Calendar.current.startOfDay(for: entry.timestamp),
                        value: weight,
                        category: .growth
                    ))
                }
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)
            let minimum = values.isEmpty ? 0 : values.min() ?? 0
            let maximum = values.isEmpty ? 0 : values.max() ?? 0

            // Calculate daily weight gain average
            var dailyGainAverage: Double? = nil
            if values.count >= 2 {
                let firstWeight = values.first!
                let lastWeight = values.last!
                let daysDifference = Calendar.current.dateComponents([.day], from: dataPoints.first!.date, to: dataPoints.last!.date).day ?? 1

                if daysDifference > 0 {
                    dailyGainAverage = (lastWeight - firstWeight) / Double(daysDifference)
                }
            }

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.weightChartData = WeightChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    average: average,
                    minimum: minimum,
                    maximum: maximum,
                    percentChange: percentChange,
                    dailyGainAverage: dailyGainAverage
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load weight data: \(error.localizedDescription)"
            }
        }
    }

    private func loadHeightChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        // For growth metrics, we always want to show the full curve from beginning to current date
        // So we ignore the startDate parameter and fetch all data up to endDate
        let descriptor = FetchDescriptor<GrowthEntry>(
            predicate: #Predicate { entry in
                entry.timestamp < endDate && entry.height != nil
            },
            sortBy: [SortDescriptor(\.timestamp)]
        )

        do {
            let growthEntries = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Create data points directly from growth entries
            var dataPoints: [ChartDataPoint] = []

            for entry in growthEntries {
                if let height = entry.height {
                    dataPoints.append(ChartDataPoint(
                        date: Calendar.current.startOfDay(for: entry.timestamp),
                        value: height,
                        category: .growth
                    ))
                }
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)
            let minimum = values.isEmpty ? 0 : values.min() ?? 0
            let maximum = values.isEmpty ? 0 : values.max() ?? 0

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.heightChartData = HeightChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    average: average,
                    minimum: minimum,
                    maximum: maximum,
                    percentChange: percentChange
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load height data: \(error.localizedDescription)"
            }
        }
    }

    private func loadHeadCircumferenceChartData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        // For growth metrics, we always want to show the full curve from beginning to current date
        // So we ignore the startDate parameter and fetch all data up to endDate
        let descriptor = FetchDescriptor<GrowthEntry>(
            predicate: #Predicate { entry in
                entry.timestamp < endDate && entry.headCircumference != nil
            },
            sortBy: [SortDescriptor(\.timestamp)]
        )

        do {
            let growthEntries = try await MainActor.run { try modelContext.fetch(descriptor) }

            // Create data points directly from growth entries
            var dataPoints: [ChartDataPoint] = []

            for entry in growthEntries {
                if let headCircumference = entry.headCircumference {
                    dataPoints.append(ChartDataPoint(
                        date: Calendar.current.startOfDay(for: entry.timestamp),
                        value: headCircumference,
                        category: .growth
                    ))
                }
            }

            // Calculate statistics
            let values = dataPoints.map { $0.value }.filter { $0 > 0 } // Only consider non-zero values
            let average = values.isEmpty ? 0 : values.reduce(0, +) / Double(values.count)
            let minimum = values.isEmpty ? 0 : values.min() ?? 0
            let maximum = values.isEmpty ? 0 : values.max() ?? 0

            // Calculate percent change (if applicable)
            var percentChange: Double? = nil
            if values.count >= 2 {
                let firstHalf = Array(values.prefix(values.count / 2))
                let secondHalf = Array(values.suffix(values.count / 2))

                let firstHalfAvg = firstHalf.isEmpty ? 0 : firstHalf.reduce(0, +) / Double(firstHalf.count)
                let secondHalfAvg = secondHalf.isEmpty ? 0 : secondHalf.reduce(0, +) / Double(secondHalf.count)

                if firstHalfAvg > 0 {
                    percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                }
            }

            await MainActor.run {
                self.headCircumferenceChartData = HeadCircumferenceChartData(
                    timeSpan: selectedTimeSpan,
                    startDate: startDate,
                    endDate: endDate,
                    dataPoints: dataPoints,
                    average: average,
                    minimum: minimum,
                    maximum: maximum,
                    percentChange: percentChange
                )
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load head circumference data: \(error.localizedDescription)"
            }
        }
    }

    private func loadPatternData(startDate: Date, endDate: Date) async {
        guard let modelContext = modelContext else { return }

        // Fetch all entries within the date range
        let feedingDescriptor = FetchDescriptor<FeedingEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        let sleepDescriptor = FetchDescriptor<SleepEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        let diaperDescriptor = FetchDescriptor<DiaperEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= startDate && entry.timestamp < endDate
            }
        )

        do {
            // Fetch all entries
            let feedings = try await MainActor.run { try modelContext.fetch(feedingDescriptor) }
            let sleepEntries = try await MainActor.run { try modelContext.fetch(sleepDescriptor) }
            let diaperEntries = try await MainActor.run { try modelContext.fetch(diaperDescriptor) }

            // Create category-specific pattern activities
            var feedingActivities: [PatternData.PatternActivity] = []
            var sleepActivities: [PatternData.PatternActivity] = []
            var diaperActivities: [PatternData.PatternActivity] = []

            // Add feeding activities
            for feeding in feedings {
                let day = Calendar.current.startOfDay(for: feeding.timestamp)

                // Estimate duration based on feeding type
                let duration: TimeInterval?
                if feeding.type == .breastfeeding, let mins = feeding.duration {
                    duration = TimeInterval(mins * 60) // Convert minutes to seconds
                } else if feeding.type == .bottleFeeding {
                    duration = 15 * 60 // Assume 15 minutes for bottle feeding
                } else {
                    duration = 20 * 60 // Assume 20 minutes for solid food
                }

                let endTime = duration != nil ? feeding.timestamp.addingTimeInterval(duration!) : nil

                feedingActivities.append(PatternData.PatternActivity(
                    date: day,
                    startTime: feeding.timestamp,
                    endTime: endTime,
                    category: .feeding,
                    duration: duration
                ))
            }

            // Add sleep activities
            for sleep in sleepEntries {
                let day = Calendar.current.startOfDay(for: sleep.timestamp)

                sleepActivities.append(PatternData.PatternActivity(
                    date: day,
                    startTime: sleep.timestamp,
                    endTime: sleep.endTime,
                    category: .sleep,
                    duration: sleep.endTime != nil ? sleep.endTime!.timeIntervalSince(sleep.timestamp) : nil
                ))
            }

            // Add diaper activities
            for diaper in diaperEntries {
                let day = Calendar.current.startOfDay(for: diaper.timestamp)

                // Assume diaper changes take 5 minutes
                let duration: TimeInterval = 5 * 60
                let endTime = diaper.timestamp.addingTimeInterval(duration)

                diaperActivities.append(PatternData.PatternActivity(
                    date: day,
                    startTime: diaper.timestamp,
                    endTime: endTime,
                    category: .diaper,
                    duration: duration
                ))
            }

            await MainActor.run {
                var newCategoryPatternData: [LogCategory: PatternData] = [:]

                if !feedingActivities.isEmpty {
                    newCategoryPatternData[.feeding] = PatternData(
                        timeSpan: selectedTimeSpan,
                        startDate: startDate,
                        endDate: endDate,
                        category: .feeding,
                        activities: feedingActivities
                    )
                }

                if !sleepActivities.isEmpty {
                    newCategoryPatternData[.sleep] = PatternData(
                        timeSpan: selectedTimeSpan,
                        startDate: startDate,
                        endDate: endDate,
                        category: .sleep,
                        activities: sleepActivities
                    )
                }

                if !diaperActivities.isEmpty {
                    newCategoryPatternData[.diaper] = PatternData(
                        timeSpan: selectedTimeSpan,
                        startDate: startDate,
                        endDate: endDate,
                        category: .diaper,
                        activities: diaperActivities
                    )
                }

                self.categoryPatternData = newCategoryPatternData
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load pattern data: \(error.localizedDescription)"
            }
        }
    }

    // Reset chart period to the most recent period
    func resetChartPeriodToCurrent() {
        let today = Date()
        chartEndDate = Calendar.current.startOfDay(for: today)

        switch selectedTimeSpan {
        case .weekly:
            chartStartDate = Calendar.current.date(byAdding: .day, value: -6, to: chartEndDate)!
        case .biweekly:
            chartStartDate = Calendar.current.date(byAdding: .day, value: -13, to: chartEndDate)!
        case .monthly:
            chartStartDate = Calendar.current.date(byAdding: .month, value: -1, to: chartEndDate)!
            chartStartDate = Calendar.current.date(byAdding: .day, value: 1, to: chartStartDate)!
        default:
            chartStartDate = Calendar.current.date(byAdding: .day, value: -6, to: chartEndDate)!
        }

        // Cannot navigate forward past current date
        canNavigateChartForward = false
    }

    // Navigate to previous chart period
    func navigateChartPrevious() {
        let calendar = Calendar.current

        // Store current end date as it becomes the new start date for forward navigation
        let previousEndDate = chartEndDate

        switch selectedTimeSpan {
        case .weekly:
            chartEndDate = calendar.date(byAdding: .day, value: -7, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .day, value: -7, to: chartStartDate)!
        case .biweekly:
            chartEndDate = calendar.date(byAdding: .day, value: -14, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .day, value: -14, to: chartStartDate)!
        case .monthly:
            chartEndDate = calendar.date(byAdding: .month, value: -1, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .month, value: -1, to: chartStartDate)!
        default:
            // For daily view, handled by existing navigation
            return
        }

        // After navigating back, we can always navigate forward
        canNavigateChartForward = true

        loadDataForCharts()
    }

    // Navigate to next chart period
    func navigateChartNext() {
        if !canNavigateChartForward {
            return
        }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        switch selectedTimeSpan {
        case .weekly:
            chartEndDate = calendar.date(byAdding: .day, value: 7, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .day, value: 7, to: chartStartDate)!
        case .biweekly:
            chartEndDate = calendar.date(byAdding: .day, value: 14, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .day, value: 14, to: chartStartDate)!
        case .monthly:
            chartEndDate = calendar.date(byAdding: .month, value: 1, to: chartEndDate)!
            chartStartDate = calendar.date(byAdding: .month, value: 1, to: chartStartDate)!
        default:
            // For daily view, handled by existing navigation
            return
        }

        // Check if we've reached current date
        if chartEndDate >= today {
            chartEndDate = today

            // Adjust start date based on timespan
            switch selectedTimeSpan {
            case .weekly:
                chartStartDate = calendar.date(byAdding: .day, value: -6, to: chartEndDate)!
            case .biweekly:
                chartStartDate = calendar.date(byAdding: .day, value: -13, to: chartEndDate)!
            case .monthly:
                chartStartDate = calendar.date(byAdding: .month, value: -1, to: chartEndDate)!
                chartStartDate = calendar.date(byAdding: .day, value: 1, to: chartStartDate)!
            default:
                break
            }

            // Cannot navigate forward past current date
            canNavigateChartForward = false
        }

        loadDataForCharts()
    }
}

// MARK: - Entry Extensions

extension FeedingEntry {
    var displayTitle: String {
        switch type {
        case .breastfeeding:
            return "Breastfeeding"
        case .bottleFeeding:
            return "Bottle Feeding"
        case .solidFood:
            return "Solid Food"
        }
    }

    var subtitle: String {
        switch type {
        case .breastfeeding:
            var parts: [String] = []
            if leftBreast == true { parts.append("Left") }
            if rightBreast == true { parts.append("Right") }
            if let duration = duration { parts.append("\(duration) min") }
            return parts.joined(separator: " • ")

        case .bottleFeeding:
            var parts: [String] = []
            if let volume = volume { parts.append("\(Int(volume)) mL") }
            if let content = content { parts.append(getDescriptionForBottleContent(content)) }
            return parts.joined(separator: " • ")

        case .solidFood:
            var parts: [String] = []
            if let foodItem = foodItem { parts.append(foodItem) }
            if let foodAmount = foodAmount { parts.append(foodAmount) }
            return parts.joined(separator: " • ")
        }
    }

    var details: String {
        return notes ?? ""
    }
}

extension DiaperEntry {
    var subtitle: String {
        var parts: [String] = []

        switch type {
        case .wet:
            parts.append("Wet diaper")
        case .dirty:
            parts.append("Dirty diaper")
            if let color = poopColor { parts.append(color.description) }
            if let consistency = poopConsistency { parts.append(consistency.description) }
        case .mixed:
            parts.append("Mixed diaper")
            if let color = poopColor { parts.append(color.description) }
            if let consistency = poopConsistency { parts.append(consistency.description) }
        }

        return parts.joined(separator: " • ")
    }

    var details: String {
        return notes ?? ""
    }
}

extension SleepEntry {
    var subtitle: String {
        var parts: [String] = []

        if let endTime = endTime {
            let duration = Int(endTime.timeIntervalSince(timestamp) / 60)
            if duration < 60 {
                parts.append("\(duration) min")
            } else {
                let hours = duration / 60
                let minutes = duration % 60
                parts.append("\(hours) hr \(minutes) min")
            }
        } else {
            parts.append("Ongoing")
        }

        if let location = location {
            parts.append(location.description)
        }

        return parts.joined(separator: " • ")
    }

    var details: String {
        return notes ?? ""
    }
}

// Helper functions for feeding types and bottle content
func getTitleForFeedingType(_ type: FeedingEntry.FeedingType) -> String {
    switch type {
    case .breastfeeding:
        return "Breastfeeding"
    case .bottleFeeding:
        return "Bottle Feeding"
    case .solidFood:
        return "Solid Food"
    }
}

func getDescriptionForBottleContent(_ content: FeedingEntry.BottleContent) -> String {
    switch content {
    case .formula:
        return "Formula"
    case .expressedBreastMilk:
        return "Breast Milk"
    case .mixed:
        return "Mixed"
    }
}

// MARK: - Growth Entry Extension

extension GrowthEntry {
    var subtitle: String {
        let unitSystem = UserPreferencesManager.shared.unitSystem
        var parts: [String] = []

        if let weight = weight {
            parts.append(UnitConverter.formatWeight(weight, unitSystem: unitSystem))
        }

        if let height = height {
            parts.append(UnitConverter.formatHeight(height, unitSystem: unitSystem))
        }

        if let headCircumference = headCircumference {
            parts.append("HC: " + UnitConverter.formatHeadCircumference(headCircumference, unitSystem: unitSystem))
        }

        return parts.joined(separator: " • ")
    }

    var details: String {
        return notes ?? ""
    }
}

// MARK: - Health Entry Extension

extension HealthEntry {
    var subtitle: String {
        let unitSystem = UserPreferencesManager.shared.unitSystem
        var parts: [String] = []

        switch type {
        case .temperature:
            if let temperature = temperature, let unit = temperatureUnit {
                parts.append(UnitConverter.formatTemperature(temperature, unit: unit, convertTo: unitSystem))
            }

        case .medication:
            if let medicationName = medicationName {
                parts.append(medicationName)
            }
            if let medicationDosage = medicationDosage {
                parts.append(medicationDosage)
            }

        case .symptom:
            let symptoms = getSymptoms()
            if !symptoms.isEmpty {
                parts.append(symptoms.prefix(2).map { $0.description }.joined(separator: ", "))
                if symptoms.count > 2 {
                    parts.append("+\(symptoms.count - 2) more")
                }
            }

        case .vaccination:
            if let vaccineName = vaccineName {
                parts.append(vaccineName)
            }

        case .appointment:
            if let appointmentReason = appointmentReason {
                parts.append(appointmentReason)
            }
            if let appointmentProvider = appointmentProvider {
                parts.append(appointmentProvider)
            }
        }

        return parts.joined(separator: " • ")
    }

    var details: String {
        return notes ?? ""
    }
}
