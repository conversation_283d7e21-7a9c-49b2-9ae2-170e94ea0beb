//
//  OnboardingViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI
import SwiftData
import PhotosUI
import Combine

#if DEBUG
import struct BabyPulse.TestingUtilities
#endif

enum OnboardingStep: Int, CaseIterable {
    case welcome = 0
    case babyProfile = 1
    // Removed whatToExpect step as it's less important
}

class OnboardingViewModel: ObservableObject {
    @Published var currentStep: OnboardingStep = .welcome
    @Published var babyName: String = ""
    @Published var birthDate: Date = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
    @Published var gender: Gender = .male
    @Published var selectedPhoto: PhotosPickerItem?
    @Published var photoData: Data?
    @Published var unitSystem: UnitSystem = .metric
    // Moved these preferences to be set later in the app
    @Published var notificationsEnabled: Bool = true
    @Published var reminderTime: Date = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
    @Published var darkModeEnabled: Bool = false
    // Demo mode property kept for backward compatibility but not used
    @Published var demoModeEnabled: Bool = false
    
    // For calculations
    @Published var babyAge: String = ""
    
    // Validation
    @Published var nameError: String?
    
    // Sample avatar options
    let avatarOptions = [
        "baby.avatar.1",
        "baby.avatar.2",
        "baby.avatar.3",
        "baby.avatar.4"
    ]
    @Published var selectedAvatarIndex: Int? = nil

    var canMoveToNextStep: Bool {
        switch currentStep {
        case .welcome:
            return true
        case .babyProfile:
            return validateBabyProfile()
        }
    }

    func moveToNextStep() {
        if canMoveToNextStep {
            if let nextStep = OnboardingStep(rawValue: currentStep.rawValue + 1) {
                currentStep = nextStep
            } else {
                // If we're at the last step and try to go forward, complete onboarding
                finishOnboarding?()
            }
        }
    }

    func moveToPreviousStep() {
        if let previousStep = OnboardingStep(rawValue: currentStep.rawValue - 1) {
            currentStep = previousStep
        }
    }

    var finishOnboarding: (() -> Void)?

    func validateBabyProfile() -> Bool {
        // Reset errors
        nameError = nil

        // Validate name
        if babyName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            nameError = "Please enter your baby's name"
            return false
        }

        return true
    }
    
    func updateBabyAge() {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year, .month, .day], from: birthDate, to: Date())
        
        if let years = ageComponents.year, let months = ageComponents.month, let days = ageComponents.day {
            if years > 0 {
                babyAge = "\(years) year\(years == 1 ? "" : "s"), \(months) month\(months == 1 ? "" : "s")"
            } else if months > 0 {
                babyAge = "\(months) month\(months == 1 ? "" : "s"), \(days) day\(days == 1 ? "" : "s")"
            } else {
                babyAge = "\(days) day\(days == 1 ? "" : "s")"
            }
        }
    }

    func loadPhotoData() async {
        if let selectedPhoto = selectedPhoto {
            do {
                if let data = try await selectedPhoto.loadTransferable(type: Data.self) {
                    // Capture weak self to avoid Sendable warning
                    await MainActor.run { [weak self] in
                        self?.photoData = data
                        self?.selectedAvatarIndex = nil
                    }
                }
            } catch {
                print("Error loading photo: \(error)")
            }
        }
    }
    
    func selectAvatar(index: Int) {
        selectedAvatarIndex = index
        photoData = nil
        selectedPhoto = nil
    }

    func completeOnboarding(modelContext: ModelContext) {
        // Create and save baby profile
        let baby = createBabyProfile()
        modelContext.insert(baby)

        // Create and save user preferences
        let preferences = createUserPreferences(babyID: baby.id)
        modelContext.insert(preferences)

        // Save changes
        do {
            try modelContext.save()
        } catch {
            print("Error saving onboarding data: \(error)")
        }
    }

    private func createBabyProfile() -> Baby {
        // If avatar is selected, use that instead of photo data
        var finalPhotoData = photoData
        if let avatarIndex = selectedAvatarIndex {
            if let avatarImage = UIImage(named: avatarOptions[avatarIndex]) {
                finalPhotoData = avatarImage.jpegData(compressionQuality: 0.8)
            }
        }
        
        return Baby(
            name: babyName,
            birthDate: birthDate,
            gender: gender,
            photoData: finalPhotoData
        )
    }

    private func createUserPreferences(babyID: UUID) -> UserPreferences {
        let preferences = UserPreferences()
        preferences.selectedBabyID = babyID
        preferences.unitSystem = unitSystem
        preferences.notificationsEnabled = notificationsEnabled
        preferences.reminderTime = notificationsEnabled ? reminderTime : nil
        preferences.darkModeEnabled = darkModeEnabled
        preferences.onboardingCompleted = true
        return preferences
    }
}
