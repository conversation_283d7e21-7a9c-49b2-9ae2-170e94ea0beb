import Foundation
import SwiftUI
import Combine
import SwiftData

class SyncViewModel: ObservableObject {
    // MARK: - Properties

    // Services
    private let syncManager = SyncManager.shared
    private let supabaseService = SupabaseService.shared
    private let revenueCatService = RevenueCatService.shared

    // Sync state
    @Published var isSyncing = false
    @Published var isOnline = true
    @Published var lastSyncDate: Date?
    @Published var syncEnabled = false
    @Published var autoSyncEnabled = false
    @Published var syncInterval: TimeInterval = 3600 // Default to 1 hour

    // Error state
    @Published var errorMessage: String?

    // Premium access
    @Published var hasPremiumAccess = false

    // Sync status
    @Published var syncStatus: String = "Ready to sync"
    @Published var syncProgress: Double? = nil

    // Data usage metrics
    @Published var profilesCount: Int = 0
    @Published var feedingsCount: Int = 0
    @Published var diapersCount: Int = 0
    @Published var sleepCount: Int = 0
    @Published var healthCount: Int = 0
    @Published var profilesLastSync: Date? = nil
    @Published var feedingsLastSync: Date? = nil
    @Published var diapersLastSync: Date? = nil
    @Published var sleepLastSync: Date? = nil
    @Published var healthLastSync: Date? = nil
    @Published var totalSyncSize: String = "0 KB"

    private var connectivityTimer: Timer?
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init() {
        setupConnectivityCheck()

        // Subscribe to sync and premium status changes
        NotificationCenter.default.publisher(for: .syncStatusChanged)
            .sink { [weak self] notification in
                self?.loadSyncInfo()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: .premiumStatusChanged)
            .sink { [weak self] notification in
                self?.checkPremiumStatus()
            }
            .store(in: &cancellables)

        // Initial state load
        loadSyncInfo()
        checkPremiumStatus()
    }

    deinit {
        connectivityTimer?.invalidate()
    }

    // MARK: - Methods

    /// Load sync settings from UserDefaults
    private func loadSettings() {
        autoSyncEnabled = UserDefaults.standard.bool(forKey: "autoSyncEnabled")
        syncInterval = UserDefaults.standard.double(forKey: "syncInterval")

        if syncInterval == 0 {
            syncInterval = 3600 // Default to 1 hour
        }

        if let lastSyncDateTimestamp = UserDefaults.standard.object(forKey: "lastSyncDate") as? Double {
            lastSyncDate = Date(timeIntervalSince1970: lastSyncDateTimestamp)
        }
    }

    /// Save sync settings to UserDefaults
    private func saveSettings() {
        UserDefaults.standard.set(autoSyncEnabled, forKey: "autoSyncEnabled")
        UserDefaults.standard.set(syncInterval, forKey: "syncInterval")

        if let lastSyncDate = lastSyncDate {
            UserDefaults.standard.set(lastSyncDate.timeIntervalSince1970, forKey: "lastSyncDate")
        }
    }

    /// Update auto sync setting
    func updateAutoSync(_ enabled: Bool) {
        autoSyncEnabled = enabled
        saveSettings()
    }

    /// Update sync interval
    func updateSyncInterval(_ interval: TimeInterval) {
        syncInterval = interval
        saveSettings()
    }

    /// Trigger manual sync
    @MainActor
    func syncNow() async -> Bool {
        guard isOnline else {
            errorMessage = "Cannot sync while offline. Please check your internet connection."
            return false
        }

        isSyncing = true
        syncStatus = "Starting sync..."
        syncProgress = 0.0

        do {
            // Simulate a sync operation with progress
            try await simulateSync()

            // Update last sync date
            lastSyncDate = Date()
            saveSettings()

            // Load updated metrics
            loadSyncMetrics()

            syncStatus = "Sync completed"
            isSyncing = false
            return true
        } catch {
            errorMessage = "Sync failed: \(error.localizedDescription)"
            syncStatus = "Sync failed"
            isSyncing = false
            return false
        }
    }

    /// Format last sync date for display
    func formattedLastSyncDate() -> String {
        guard let lastSyncDate = lastSyncDate else {
            return "Never"
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: lastSyncDate)
    }

    func getTimeSinceLastSync() -> String {
        guard let lastSyncDate = lastSyncDate else {
            return "No previous sync"
        }

        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastSyncDate, relativeTo: Date())
    }

    // MARK: - Sync Operations
    @MainActor
    func forceRefresh() async -> Bool {
        guard isOnline else {
            errorMessage = "Cannot perform force refresh while offline. Please check your internet connection."
            return false
        }

        isSyncing = true
        syncStatus = "Starting force refresh..."
        syncProgress = 0.0

        do {
            // Simulate a longer force refresh operation with progress
            try await simulateForceRefresh()

            // Update last sync date
            lastSyncDate = Date()
            saveSettings()

            // Load updated metrics
            loadSyncMetrics()

            syncStatus = "Force refresh completed"
            isSyncing = false
            return true
        } catch {
            errorMessage = "Force refresh failed: \(error.localizedDescription)"
            syncStatus = "Force refresh failed"
            isSyncing = false
            return false
        }
    }

    @MainActor
    func clearSyncLog() async -> Bool {
        isSyncing = true
        syncStatus = "Clearing sync logs..."

        do {
            // Simulate clearing logs
            try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds

            // In a real app, you would clear the sync logs from the database

            syncStatus = "Sync logs cleared"
            isSyncing = false
            return true
        } catch {
            errorMessage = "Failed to clear sync logs: \(error.localizedDescription)"
            syncStatus = "Failed to clear logs"
            isSyncing = false
            return false
        }
    }

    // MARK: - Helper Functions
    private func setupConnectivityCheck() {
        // Check connectivity status every 10 seconds
        // In a real app, you would use NWPathMonitor for real connectivity checking
        connectivityTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
            // Simple simulation - 95% chance of being online
            self?.isOnline = Double.random(in: 0...1) < 0.95
        }

        // Initial check
        isOnline = true
    }

    private func checkPremiumStatus() {
        // In a real app, this would check with RevenueCat or another subscription service
        // For now, we'll simulate having premium access
        hasPremiumAccess = true
    }

    /// Load sync information including settings and metrics
    func loadSyncInfo() {
        // Load sync settings
        loadSettings()

        // Check if sync is enabled on the server
        syncEnabled = supabaseService.isSyncEnabled

        // Load sync metrics
        loadSyncMetrics()
    }

    private func loadSyncMetrics() {
        // In a real app, these would be loaded from your database
        // For simulation, we'll use random values
        profilesCount = Int.random(in: 1...5)
        feedingsCount = Int.random(in: 30...150)
        diapersCount = Int.random(in: 25...120)
        sleepCount = Int.random(in: 20...100)
        healthCount = Int.random(in: 5...30)

        // Last sync dates
        if let lastSync = lastSyncDate {
            profilesLastSync = lastSync.addingTimeInterval(Double.random(in: -3600...0))
            feedingsLastSync = lastSync.addingTimeInterval(Double.random(in: -3600...0))
            diapersLastSync = lastSync.addingTimeInterval(Double.random(in: -3600...0))
            sleepLastSync = lastSync.addingTimeInterval(Double.random(in: -3600...0))
            healthLastSync = lastSync.addingTimeInterval(Double.random(in: -3600...0))
        }

        // Calculate total sync size (this would be calculated from actual data in a real app)
        let totalItems = profilesCount + feedingsCount + diapersCount + sleepCount + healthCount
        let estimatedSizeBytes = totalItems * 2048 // Assume average 2KB per item

        if estimatedSizeBytes < 1024 {
            totalSyncSize = "\(estimatedSizeBytes) B"
        } else if estimatedSizeBytes < 1024 * 1024 {
            totalSyncSize = String(format: "%.1f KB", Double(estimatedSizeBytes) / 1024.0)
        } else {
            totalSyncSize = String(format: "%.1f MB", Double(estimatedSizeBytes) / (1024.0 * 1024.0))
        }
    }

    // Simulation functions
    private func simulateSync() async throws {
        // Simulate a sync operation with progress reporting
        let totalSteps = 10
        for step in 1...totalSteps {
            // Check if network is still available
            if !isOnline {
                throw NSError(domain: "SyncError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Network connection lost during sync"])
            }

            // Update progress
            syncProgress = Double(step) / Double(totalSteps)

            // Update status message based on current step
            switch step {
            case 1:
                syncStatus = "Connecting to server..."
            case 2:
                syncStatus = "Authenticating..."
            case 3:
                syncStatus = "Preparing data..."
            case 4:
                syncStatus = "Syncing profiles..."
            case 5:
                syncStatus = "Syncing feedings..."
            case 6:
                syncStatus = "Syncing diapers..."
            case 7:
                syncStatus = "Syncing sleep records..."
            case 8:
                syncStatus = "Syncing health records..."
            case 9:
                syncStatus = "Finalizing sync..."
            case 10:
                syncStatus = "Completing sync..."
            default:
                syncStatus = "Syncing data..."
            }

            // Simulate network delay
            try await Task.sleep(nanoseconds: UInt64.random(in: 300_000_000...800_000_000))
        }
    }

    private func simulateForceRefresh() async throws {
        // Simulate a force refresh operation with progress reporting
        let totalSteps = 15
        for step in 1...totalSteps {
            // Check if network is still available
            if !isOnline {
                throw NSError(domain: "SyncError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Network connection lost during force refresh"])
            }

            // Update progress
            syncProgress = Double(step) / Double(totalSteps)

            // Update status message based on current step
            switch step {
            case 1:
                syncStatus = "Connecting to server..."
            case 2:
                syncStatus = "Authenticating..."
            case 3:
                syncStatus = "Backing up local data..."
            case 4:
                syncStatus = "Preparing to reset..."
            case 5:
                syncStatus = "Clearing local database..."
            case 6:
                syncStatus = "Downloading profiles..."
            case 7:
                syncStatus = "Downloading feeding data..."
            case 8:
                syncStatus = "Downloading diaper records..."
            case 9:
                syncStatus = "Downloading sleep records..."
            case 10:
                syncStatus = "Downloading health records..."
            case 11:
                syncStatus = "Downloading settings..."
            case 12:
                syncStatus = "Processing downloaded data..."
            case 13:
                syncStatus = "Rebuilding local database..."
            case 14:
                syncStatus = "Verifying data integrity..."
            case 15:
                syncStatus = "Finalizing force refresh..."
            default:
                syncStatus = "Refreshing data..."
            }

            // Simulate network delay (longer for force refresh)
            try await Task.sleep(nanoseconds: UInt64.random(in: 400_000_000...1_000_000_000))
        }
    }
}

// MARK: - Notification extensions
extension Notification.Name {
    static let syncStatusChanged = Notification.Name("syncStatusChanged")
    static let premiumStatusChanged = Notification.Name("premiumStatusChanged")
    static let darkModeChanged = Notification.Name("darkModeChanged")
}

