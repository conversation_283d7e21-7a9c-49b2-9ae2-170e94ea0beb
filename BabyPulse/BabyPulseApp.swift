//
//  BabyPulseApp.swift
//  BabyPulse
//
//  Created by <PERSON> on 4/22/25.
//

import SwiftUI
import SwiftData

@main
struct BabyPulseApp: App {
    // Initialize services
    private let supabaseService = SupabaseService.shared
    private let syncManager = SyncManager.shared
    private let revenueCatService = RevenueCatService.shared
    private let userPreferencesManager = UserPreferencesManager.shared

    // Insight generation manager
    private let insightGenerationManager = InsightGenerationManager.shared

    var sharedModelContainer: ModelContainer = {
        // Create a schema with all models
        let schema = Schema([
            Baby.self,
            UserPreferences.self,
            FeedingEntry.self,
            DiaperEntry.self,
            SleepEntry.self,
            GrowthEntry.self,
            HealthEntry.self,
            ChatMessage.self,
            ChatThread.self,
            UserAccount.self,
            Insight.self
        ])

        do {
            // Use persistent database for development
            print("Using persistent database for development")

            // Create a URL for the database file
            let url = URL.applicationSupportDirectory.appending(path: "default.store")

            // For development, we'll keep the database file to persist data
            // This helps with testing features like charts that need historical data
            // try? FileManager.default.removeItem(at: url)

            // Create a new configuration with persistent storage
            let modelConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: false,
                allowsSave: true
            )

            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            print("Error creating persistent ModelContainer: \(error)")

            // If all else fails, use in-memory storage as a last resort
            print("Falling back to in-memory storage")

            let inMemoryConfig = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )

            do {
                return try ModelContainer(for: schema, configurations: [inMemoryConfig])
            } catch {
                fatalError("Could not create ModelContainer even with in-memory storage: \(error)")
            }
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.light) // Start with light mode by default
                .environmentObject(supabaseService) // Provide Supabase service to the view hierarchy
                .environmentObject(syncManager) // Provide Sync manager to the view hierarchy
                .environmentObject(revenueCatService) // Provide RevenueCat service to the view hierarchy
                .environmentObject(userPreferencesManager) // Provide user preferences to the view hierarchy
                .unitSystem(userPreferencesManager.unitSystem) // Set the unit system environment value
                .task {
                    // Configure status bar appearance
                    await configureAppAppearance()
                }
                .onAppear {
                    // Initialize the model context for services
                    let modelContext = sharedModelContainer.mainContext

                    // Initialize the user preferences manager
                    userPreferencesManager.initialize(with: modelContext)

                    // Initialize the insight generation manager
                    insightGenerationManager.initialize(with: modelContext)

                    // Select a baby if none is selected
                    Task {
                        do {
                            // Check if we have a selected baby
                            if modelContext.fetchSelectedBaby() == nil {
                                // No selected baby, check if we have any babies
                                let fetchDescriptor = FetchDescriptor<Baby>()
                                let babies = try modelContext.fetch(fetchDescriptor)

                                if let firstBaby = babies.first {
                                    // Select the first baby
                                    modelContext.updateSelectedBabyID(firstBaby.id)
                                }
                            }
                        } catch {
                            print("Error fetching babies on app launch: \(error.localizedDescription)")
                        }
                    }
                }
        }
        .modelContainer(sharedModelContainer)
    }
    
    // MARK: - App Appearance Configuration
    @MainActor
    private func configureAppAppearance() async {
        // Configure navigation bar appearance
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithTransparentBackground()
        navBarAppearance.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        navBarAppearance.titleTextAttributes = [
            .foregroundColor: UIColor(BabyPulseColors.text),
            .font: UIFont.systemFont(ofSize: 20, weight: .bold)
        ]
        navBarAppearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor(BabyPulseColors.text),
            .font: UIFont.systemFont(ofSize: 34, weight: .bold)
        ]
        
        UINavigationBar.appearance().standardAppearance = navBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
        UINavigationBar.appearance().compactAppearance = navBarAppearance
        
        // Configure status bar style
        UIApplication.shared.statusBarStyle = .default
        
        // Configure tab bar appearance for custom implementation
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithTransparentBackground()
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }
}
