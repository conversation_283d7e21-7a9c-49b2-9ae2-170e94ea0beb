//
//  PromptTemplateManager.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import Yams

/// Manages prompt templates for insight generation
class PromptTemplateManager {
    /// Singleton instance
    static let shared = PromptTemplateManager()

    /// Cache of loaded templates
    private var templates: [String: PromptTemplate]?

    /// Private initializer for singleton
    private init() {}

    /// Load prompt templates from YAML configuration
    func loadTemplates() -> [String: PromptTemplate]? {
        // Return cached templates if available
        if let templates = templates {
            return templates
        }

        var allTemplates: [String: PromptTemplate] = [:]
        var templatesLoaded = false

        // Try to get the URL for the growth and health templates file from Resources directory
        if let fileURL = Bundle.main.url(forResource: "prompt_templates_growth_health", withExtension: "yaml", subdirectory: "Resources") {
            if let loadedTemplates = loadTemplatesFromURL(fileURL) {
                allTemplates.merge(loadedTemplates) { (_, new) in new }
                templatesLoaded = true
                print("Loaded growth and health templates")
            }
        }

        // Try to get the URL for the simplified prompt templates file from Resources directory
        if let fileURL = Bundle.main.url(forResource: "prompt_templates_simplified", withExtension: "yaml", subdirectory: "Resources") {
            if let loadedTemplates = loadTemplatesFromURL(fileURL) {
                allTemplates.merge(loadedTemplates) { (_, new) in new }
                templatesLoaded = true
                print("Loaded simplified templates")
            }
        }

        // Try to get the URL for the prompt templates file from Resources directory
        if let fileURL = Bundle.main.url(forResource: "prompt_templates", withExtension: "yaml", subdirectory: "Resources") {
            if let loadedTemplates = loadTemplatesFromURL(fileURL) {
                allTemplates.merge(loadedTemplates) { (_, new) in new }
                templatesLoaded = true
                print("Loaded standard templates from Resources")
            }
        }

        // Try to get the URL from Config directory as fallback
        if let fileURL = Bundle.main.url(forResource: "prompt_templates", withExtension: "yaml", subdirectory: "Config") {
            if let loadedTemplates = loadTemplatesFromURL(fileURL) {
                allTemplates.merge(loadedTemplates) { (_, new) in new }
                templatesLoaded = true
                print("Loaded templates from Config")
            }
        }

        // Try to get the URL from the main bundle as a last resort
        if let fileURL = Bundle.main.url(forResource: "prompt_templates", withExtension: "yaml") {
            if let loadedTemplates = loadTemplatesFromURL(fileURL) {
                allTemplates.merge(loadedTemplates) { (_, new) in new }
                templatesLoaded = true
                print("Loaded templates from main bundle")
            }
        }

        if templatesLoaded {
            // Cache the combined templates
            self.templates = allTemplates
            return allTemplates
        }

        print("Prompt templates file not found in any location, using default templates")
        return createDefaultTemplates()
    }

    /// Load templates from a URL
    private func loadTemplatesFromURL(_ fileURL: URL) -> [String: PromptTemplate]? {
        do {
            // Read the YAML file
            let yamlData = try String(contentsOf: fileURL, encoding: .utf8)

            // Parse YAML using Yams
            let decoder = YAMLDecoder()

            // Try to decode as a string-based dictionary
            do {
                let rawYaml = try decoder.decode([String: [String: String]].self, from: yamlData)

                // Convert raw templates to PromptTemplate objects
                var templates: [String: PromptTemplate] = [:]

                for (key, value) in rawYaml {
                    // Skip base_template or other non-template entries
                    if key == "base_template" {
                        continue
                    }

                    // Require at least the template string
                    guard let templateString = value["template"] else {
                        print("Skipping template \(key): missing template string")
                        continue
                    }

                    // Get optional fields with defaults
                    let variablesString = value["dynamic_variables"] ?? "baby_name, baby_age, baby_age_weeks"
                    let thresholdsString = value["contextual_thresholds"] ?? "significant_change: 20%, alert_threshold: 40%"
                    let recommendationsString = value["actionable_recommendations"] ?? "Monitor patterns, Consult with a healthcare provider if concerned"
                    let priorityString = value["priority_scoring"] ?? "severity: medium, urgency: medium, impact: medium"

                    // Parse string values into appropriate types
                    let variables = variablesString.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                    let thresholds = parseStringToDictionary(thresholdsString)
                    let recommendations = recommendationsString.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                    let priority = parseStringToDictionary(priorityString)

                    let promptTemplate = PromptTemplate(
                        name: key,
                        template: templateString,
                        dynamicVariables: variables,
                        contextualThresholds: thresholds,
                        actionableRecommendations: recommendations,
                        priorityScoring: priority
                    )

                    templates[key] = promptTemplate
                    print("Loaded template: \(key)")
                }

                return templates
            } catch {
                print("Error parsing YAML as [String: [String: String]]: \(error.localizedDescription)")
                // Fall back to default templates
                return createDefaultTemplates()
            }
        } catch {
            print("Error loading prompt templates from \(fileURL.path): \(error.localizedDescription)")
            print("Error details: \(error)")
            // Fall back to default templates
            return createDefaultTemplates()
        }
    }

    /// Create default templates when YAML parsing fails
    private func createDefaultTemplates() -> [String: PromptTemplate] {
        print("Creating default templates as fallback")
        var templates: [String: PromptTemplate] = [:]

        // Create a default feeding template
        let feedingTemplate = PromptTemplate(
            name: "feeding_frequency",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the feeding data, provide a brief insight about the baby's feeding patterns.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_count", "historical_average", "change_percentage"],
            contextualThresholds: ["significant_change": "20%", "alert_threshold": "40%"],
            actionableRecommendations: ["Monitor feeding patterns", "Consult with a healthcare provider if concerned"],
            priorityScoring: ["severity": "low", "urgency": "low", "impact": "medium"]
        )
        templates["feeding_frequency"] = feedingTemplate

        // Create a default sleep template
        let sleepTemplate = PromptTemplate(
            name: "sleep_duration",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the sleep data, provide a brief insight about the baby's sleep patterns.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_value", "historical_average", "change_percentage"],
            contextualThresholds: ["significant_change": "15%", "alert_threshold": "25%"],
            actionableRecommendations: ["Maintain a consistent bedtime routine", "Consult with a healthcare provider if concerned"],
            priorityScoring: ["severity": "medium", "urgency": "medium", "impact": "high"]
        )
        templates["sleep_duration"] = sleepTemplate

        // Create a default diaper template
        let diaperTemplate = PromptTemplate(
            name: "diaper_frequency",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the diaper data, provide a brief insight about the baby's diaper patterns.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_count", "historical_average", "change_percentage"],
            contextualThresholds: ["significant_change": "25%", "alert_threshold": "40%"],
            actionableRecommendations: ["Monitor hydration by tracking wet diapers", "Consult with a healthcare provider if concerned"],
            priorityScoring: ["severity": "low", "urgency": "low", "impact": "medium"]
        )
        templates["diaper_frequency"] = diaperTemplate

        // Create default growth templates
        let weightGainTemplate = PromptTemplate(
            name: "weight_gain",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the weight data, provide a brief insight about the baby's weight gain pattern.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_weight", "historical_weight", "weight_gain", "change_percentage"],
            contextualThresholds: ["significant_change": "10%", "alert_threshold": "20%"],
            actionableRecommendations: ["Continue monitoring weight regularly", "Consult with a healthcare provider if concerned about weight gain"],
            priorityScoring: ["severity": "medium", "urgency": "medium", "impact": "high"]
        )
        templates["weight_gain"] = weightGainTemplate

        let heightIncreaseTemplate = PromptTemplate(
            name: "height_increase",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the height data, provide a brief insight about the baby's height increase pattern.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_height", "historical_height", "height_increase", "change_percentage"],
            contextualThresholds: ["significant_change": "10%", "alert_threshold": "20%"],
            actionableRecommendations: ["Continue monitoring height regularly", "Consult with a healthcare provider if concerned about height increase"],
            priorityScoring: ["severity": "medium", "urgency": "medium", "impact": "high"]
        )
        templates["height_increase"] = heightIncreaseTemplate

        let headCircumferenceTemplate = PromptTemplate(
            name: "head_circumference_increase",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the head circumference data, provide a brief insight about the baby's head growth pattern.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_head_circumference", "historical_head_circumference", "head_circumference_increase", "change_percentage"],
            contextualThresholds: ["significant_change": "10%", "alert_threshold": "20%"],
            actionableRecommendations: ["Continue monitoring head circumference regularly", "Consult with a healthcare provider if concerned about head growth"],
            priorityScoring: ["severity": "medium", "urgency": "medium", "impact": "high"]
        )
        templates["head_circumference_increase"] = headCircumferenceTemplate

        // Create default health templates
        let temperatureTemplate = PromptTemplate(
            name: "temperature_fluctuation",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the temperature data, provide a brief insight about the baby's temperature fluctuation.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_temperature", "historical_temperature", "temperature_difference", "change_percentage"],
            contextualThresholds: ["significant_change": "5%", "alert_threshold": "10%"],
            actionableRecommendations: ["Continue monitoring temperature regularly", "Consult with a healthcare provider if temperature exceeds 38°C"],
            priorityScoring: ["severity": "high", "urgency": "high", "impact": "high"]
        )
        templates["temperature_fluctuation"] = temperatureTemplate

        let symptomTemplate = PromptTemplate(
            name: "symptom_pattern",
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the symptom data, provide a brief insight about the baby's symptom patterns.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks", "recent_symptom_count", "historical_symptom_count", "symptom_difference", "change_percentage"],
            contextualThresholds: ["significant_change": "20%", "alert_threshold": "40%"],
            actionableRecommendations: ["Continue monitoring symptoms", "Consult with a healthcare provider if symptoms persist or worsen"],
            priorityScoring: ["severity": "high", "urgency": "medium", "impact": "high"]
        )
        templates["symptom_pattern"] = symptomTemplate

        return templates
    }

    /// Get a specific template by name
    func getTemplate(named templateName: String) -> PromptTemplate? {
        guard let templates = loadTemplates() else {
            // This should never happen now that we have default templates
            return createDefaultTemplate(for: templateName)
        }

        // Return the requested template if it exists
        if let template = templates[templateName] {
            return template
        }

        // If the requested template doesn't exist, create a default one
        return createDefaultTemplate(for: templateName)
    }

    /// Create a default template for a specific template name
    func createDefaultTemplate(for templateName: String) -> PromptTemplate {
        print("Creating default template for \(templateName)")

        // Create a generic template based on the name
        return PromptTemplate(
            name: templateName,
            template: "You are an assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice. Based on the available data, provide a brief insight that might be helpful.",
            dynamicVariables: ["baby_name", "baby_age", "baby_age_weeks"],
            contextualThresholds: ["significant_change": "20%", "alert_threshold": "40%"],
            actionableRecommendations: ["Monitor patterns", "Consult with a healthcare provider if concerned"],
            priorityScoring: ["severity": "medium", "urgency": "medium", "impact": "medium"]
        )
    }

    /// Format a template with variables
    func formatTemplate(_ template: PromptTemplate, with variables: [String: String]) -> String {
        var formattedText = template.template

        for (key, value) in variables {
            let placeholder = "{{\(key)}}"
            formattedText = formattedText.replacingOccurrences(of: placeholder, with: value)
        }

        return formattedText
    }

    /// Check if a metric exceeds the threshold defined in a template
    func exceedsThreshold(metric: Double, thresholdKey: String, in template: PromptTemplate) -> Bool {
        guard let thresholdString = template.contextualThresholds[thresholdKey],
              let thresholdValue = Double(thresholdString.replacingOccurrences(of: "%", with: "")) else {
            return false
        }

        return metric >= thresholdValue
    }

    /// Parse a string representation to a dictionary
    private func parseStringToDictionary(_ string: String) -> [String: String] {
        var result: [String: String] = [:]

        // Split by comma for key-value pairs
        let pairs = string.components(separatedBy: ",")

        for pair in pairs {
            // Split by colon for key and value
            let components = pair.components(separatedBy: ":")
            if components.count == 2 {
                let key = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let value = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                result[key] = value
            }
        }

        return result
    }
}
