//
//  EnhancedDataAnalyzerProtocol.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Enhanced protocol for data analyzers with advanced analysis capabilities
protocol EnhancedDataAnalyzerProtocol: DataAnalyzerProtocol {
    /// Analyze data for a specific timeframe
    /// - Parameters:
    ///   - timeframe: The timeframe to analyze
    ///   - baby: The baby whose data is being analyzed
    ///   - modelContext: The SwiftData model context
    /// - Returns: Array of analysis results
    func analyzeTimeframe(_ timeframe: AnalysisTimeframe, for baby: Baby, modelContext: ModelContext) -> [AnalysisResult]

    /// Correlate data with another category
    /// - Parameters:
    ///   - category: The category to correlate with
    ///   - baby: The baby whose data is being analyzed
    ///   - modelContext: The SwiftData model context
    /// - Returns: Array of correlation analyses
    func correlateWith(category: Insight.InsightCategory, for baby: Baby, modelContext: ModelContext) -> [CorrelationAnalysis]

    /// Detect patterns in entries
    /// - Parameter entries: The entries to analyze
    /// - Returns: Array of pattern results
    func detectPatterns(in entries: [EntryType]) -> [PatternResult]

    /// Detect cyclical patterns in entries
    /// - Parameter entries: The entries to analyze
    /// - Returns: Array of cyclical patterns
    func detectCyclicalPatterns(in entries: [EntryType]) -> [CyclicalPattern]

    /// Analyze trends in entries
    /// - Parameter entries: The entries to analyze
    /// - Returns: Array of trend analyses
    func analyzeTrends(in entries: [EntryType]) -> [TrendAnalysis]

    /// Detect anomalies in recent entries compared to historical data
    /// - Parameters:
    ///   - recentEntries: Recent entries to check for anomalies
    ///   - historicalEntries: Historical entries for baseline comparison
    /// - Returns: Array of anomaly detections
    func detectAnomalies(recentEntries: [EntryType], historicalEntries: [EntryType]) -> [AnomalyDetection]

    /// Generate predictions based on analysis
    /// - Parameters:
    ///   - baby: The baby whose data is being analyzed
    ///   - modelContext: The SwiftData model context
    /// - Returns: Array of predictions
    func generatePredictions(for baby: Baby, modelContext: ModelContext) -> [Prediction]

    /// Apply developmental context to analysis results
    /// - Parameters:
    ///   - results: The analysis results to enhance
    ///   - baby: The baby whose data is being analyzed
    ///   - modelContext: The SwiftData model context
    /// - Returns: Enhanced analysis results
    func applyDevelopmentalContext(to results: [AnalysisResult], baby: Baby, modelContext: ModelContext) -> [AnalysisResult]

    /// Perform enhanced analysis with all capabilities
    /// - Parameters:
    ///   - baby: The baby whose data is being analyzed
    ///   - timeframe: The timeframe to analyze
    ///   - modelContext: The SwiftData model context
    /// - Returns: Enhanced analysis results
    func performEnhancedAnalysis(for baby: Baby, timeframe: AnalysisTimeframe, modelContext: ModelContext) -> [EnhancedAnalysisResult]
}

/// Default implementations for EnhancedDataAnalyzerProtocol
extension EnhancedDataAnalyzerProtocol {
    func analyzeTimeframe(_ timeframe: AnalysisTimeframe, for baby: Baby, modelContext: ModelContext) -> [AnalysisResult] {
        // Default implementation uses the timeframe's equivalent TimeRange
        let timeRange = timeframe.asTimeRange
        let entries = fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)

        // For historical comparison, use a longer timeframe
        var historicalTimeRange: TimeRange
        switch timeframe {
        case .intraday:
            historicalTimeRange = .last7Days
        case .shortTerm:
            historicalTimeRange = .last7Days
        case .weekly:
            historicalTimeRange = .last30Days
        case .biweekly, .monthly:
            // For longer timeframes, compare with 3x the period
            let endDate = Date()
            let startDate = Calendar.current.date(byAdding: .day, value: -90, to: endDate)!
            historicalTimeRange = .custom(DateInterval(start: startDate, end: endDate))
        }

        let historicalEntries = fetchEntries(for: baby, inTimeRange: historicalTimeRange, modelContext: modelContext)

        return analyzeData(recentEntries: entries, historicalEntries: historicalEntries, baby: baby)
    }

    func correlateWith(category: Insight.InsightCategory, for baby: Baby, modelContext: ModelContext) -> [CorrelationAnalysis] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func detectPatterns(in entries: [EntryType]) -> [PatternResult] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func detectCyclicalPatterns(in entries: [EntryType]) -> [CyclicalPattern] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func analyzeTrends(in entries: [EntryType]) -> [TrendAnalysis] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func detectAnomalies(recentEntries: [EntryType], historicalEntries: [EntryType]) -> [AnomalyDetection] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func generatePredictions(for baby: Baby, modelContext: ModelContext) -> [Prediction] {
        // Default implementation returns empty array
        // This should be implemented by specific analyzers
        return []
    }

    func applyDevelopmentalContext(to results: [AnalysisResult], baby: Baby, modelContext: ModelContext) -> [AnalysisResult] {
        // Default implementation returns the original results
        // This should be implemented by specific analyzers
        return results
    }

    func performEnhancedAnalysis(for baby: Baby, timeframe: AnalysisTimeframe, modelContext: ModelContext) -> [EnhancedAnalysisResult] {
        // Get base analysis results
        let baseResults = analyzeTimeframe(timeframe, for: baby, modelContext: modelContext)

        // Get entries for pattern detection
        let entries = fetchEntries(for: baby, inTimeRange: timeframe.asTimeRange, modelContext: modelContext)

        // Get historical entries for anomaly detection
        let historicalTimeRange = TimeRange.last30Days
        let historicalEntries = fetchEntries(for: baby, inTimeRange: historicalTimeRange, modelContext: modelContext)

        var enhancedResults: [EnhancedAnalysisResult] = []

        for baseResult in baseResults {
            // Detect patterns
            let patterns = detectPatterns(in: entries)

            // Detect cyclical patterns
            let cyclicalPatterns = detectCyclicalPatterns(in: entries)

            // Analyze trends
            let trends = analyzeTrends(in: entries)

            // Detect anomalies
            let anomalies = detectAnomalies(recentEntries: entries, historicalEntries: historicalEntries)

            // Get correlations
            let correlations = correlateWith(category: baseResult.category, for: baby, modelContext: modelContext)

            // Generate predictions
            let predictions = generatePredictions(for: baby, modelContext: modelContext)

            // Create developmental context using the service
            let developmentalService = DevelopmentalDataService.shared
            developmentalService.setModelContext(modelContext)
            let developmentalContext = developmentalService.createDevelopmentalContext(for: baby)

            // Check for milestone proximity
            let milestoneProximity = developmentalService.detectMilestoneProximity(
                for: baby,
                category: baseResult.category
            )

            // Create enhanced result
            let enhancedResult = EnhancedAnalysisResult(
                baseResult: baseResult,
                timeframe: timeframe,
                patternResults: patterns,
                cyclicalPatterns: cyclicalPatterns,
                trendAnalyses: trends,
                anomalyDetections: anomalies,
                correlations: correlations,
                developmentalContext: developmentalContext,
                milestoneProximity: milestoneProximity.map {
                    (milestone: $0.milestone.name, proximity: $0.proximity, indicators: $0.indicators)
                },
                predictions: predictions
            )

            enhancedResults.append(enhancedResult)
        }

        return enhancedResults
    }
}
