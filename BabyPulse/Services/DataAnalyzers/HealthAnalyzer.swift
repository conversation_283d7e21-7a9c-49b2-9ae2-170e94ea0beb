//
//  HealthAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Analyzer for health entries
class HealthAnalyzer: DataAnalyzerProtocol {
    typealias EntryType = HealthEntry

    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [HealthEntry] {
        // Create a basic fetch descriptor without predicates
        var fetchDescriptor = FetchDescriptor<HealthEntry>()
        fetchDescriptor.sortBy = [SortDescriptor(\HealthEntry.timestamp, order: .reverse)]
        
        do {
            // Fetch all entries
            let allEntries = try modelContext.fetch(fetchDescriptor)
            
            // Then filter manually
            let startDate = timeRange.startDate
            
            return allEntries.filter { entry in
                guard let entryBaby = entry.baby else { return false }
                return entryBaby.id == baby.id && entry.timestamp >= startDate
            }
        } catch {
            print("Error fetching health entries: \(error.localizedDescription)")
            return []
        }
    }

    func analyzeData(recentEntries: [HealthEntry], historicalEntries: [HealthEntry], baby: Baby) -> [AnalysisResult] {
        var results: [AnalysisResult] = []

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Calculate baby's age
        let babyAge = calculateBabyAge(baby: baby)

        // Analyze temperature fluctuations
        if let temperatureResult = analyzeTemperatureFluctuations(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(temperatureResult)
        }

        // Analyze symptom patterns
        if let symptomResult = analyzeSymptomPatterns(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(symptomResult)
        }

        return results
    }

    // MARK: - Private Analysis Methods

    private func analyzeTemperatureFluctuations(recentEntries: [HealthEntry], historicalEntries: [HealthEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Get most recent temperature
        guard let recentTemperature = recentEntries.first?.temperature else {
            return nil
        }

        // Get historical temperature
        guard let historicalTemperature = historicalEntries.first?.temperature else {
            return nil
        }

        // Calculate temperature difference
        let temperatureDifference = recentTemperature - historicalTemperature

        // Calculate deviation percentage
        let deviationPercentage = abs(temperatureDifference / historicalTemperature) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 5 // 5% change in temperature

        // Determine pattern type
        let patternType = temperatureDifference > 0 ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_temperature": String(format: "%.1f", recentTemperature),
            "historical_temperature": String(format: "%.1f", historicalTemperature),
            "temperature_difference": String(format: "%.1f", temperatureDifference),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalTemperature,
            recentValue: recentTemperature,
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 10 // 10% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "temperature_fluctuation",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .health,
            title: "Temperature \(patternType.capitalized)",
            metricString: "\(String(format: "%.1f", temperatureDifference))°C",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeSymptomPatterns(recentEntries: [HealthEntry], historicalEntries: [HealthEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Get recent symptoms
        guard let recentSymptoms = recentEntries.first?.getSymptoms() else {
            return nil
        }

        // Get historical symptoms
        guard let historicalSymptoms = historicalEntries.first?.getSymptoms() else {
            return nil
        }

        // Calculate symptom count
        let recentSymptomCount = recentSymptoms.count
        let historicalSymptomCount = historicalSymptoms.count

        // Calculate symptom difference
        let symptomDifference = recentSymptomCount - historicalSymptomCount

        // Calculate deviation percentage
        let deviationPercentage = abs(Double(symptomDifference) / Double(historicalSymptomCount)) * 100

        // Determine if this exceeds threshold for generating an insight
         let exceedsThreshold = deviationPercentage >= 20 // 20% change in symptoms

        // Determine pattern type
        let patternType = symptomDifference > 0 ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_symptom_count": String(recentSymptomCount),
            "historical_symptom_count": String(historicalSymptomCount),
            "symptom_difference": String(symptomDifference),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: Double(historicalSymptomCount),
            recentValue: Double(recentSymptomCount),
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 30 // 30% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "symptom_pattern",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .health,
            title: "Symptom Pattern \(patternType.capitalized)",
            metricString: "\(symptomDifference) symptoms",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    // MARK: - Helper Methods

    private func calculateBabyAge(baby: Baby) -> (months: Int, weeks: Int) {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month, .day], from: baby.birthDate, to: Date())

        let months = ageComponents.month ?? 0
        let days = ageComponents.day ?? 0
        let weeks = (months * 30 + days) / 7 // Approximate weeks

        return (months, weeks)
    }
}
