//
//  FeedingAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Analyzer for feeding entries
class FeedingAnalyzer: DataAnalyzerProtocol {
    typealias EntryType = FeedingEntry

    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [FeedingEntry] {
        // Create a basic fetch descriptor without predicates
        var fetchDescriptor = FetchDescriptor<FeedingEntry>()
        fetchDescriptor.sortBy = [SortDescriptor(\FeedingEntry.timestamp, order: .reverse)]
        
        do {
            // Fetch all entries
            let allEntries = try modelContext.fetch(fetchDescriptor)
            
            // Then filter manually
            let startDate = timeRange.startDate
            
            return allEntries.filter { entry in
                guard let entryBaby = entry.baby else { return false }
                return entryBaby.id == baby.id && entry.timestamp >= startDate
            }
        } catch {
            print("Error fetching feeding entries: \(error.localizedDescription)")
            return []
        }
    }

    func analyzeData(recentEntries: [FeedingEntry], historicalEntries: [FeedingEntry], baby: Baby) -> [AnalysisResult] {
        var results: [AnalysisResult] = []

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Calculate baby's age
        let babyAge = calculateBabyAge(baby: baby)

        // Analyze feeding frequency
        if let frequencyResult = analyzeFrequency(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(frequencyResult)
        }

        // Analyze feeding volume (for bottle feedings)
        if let volumeResult = analyzeVolume(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(volumeResult)
        }

        // Analyze feeding duration (for breastfeeding)
        if let durationResult = analyzeDuration(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(durationResult)
        }

        // Analyze feeding type distribution
        if let typeDistributionResult = analyzeTypeDistribution(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(typeDistributionResult)
        }

        return results
    }

    // MARK: - Private Analysis Methods

    private func analyzeFrequency(recentEntries: [FeedingEntry], historicalEntries: [FeedingEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Count feedings in the last 24 hours
        let recentCount = recentEntries.count

        // Calculate average daily feedings from historical data (last 7 days)
        let daysInPeriod = min(7, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
        let historicalAverage = Double(historicalEntries.count) / Double(max(1, daysInPeriod))

        // Calculate deviation
        let deviationPercentage = abs((Double(recentCount) - historicalAverage) / historicalAverage) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 20 // 20% change in feeding frequency

        // Determine pattern type
        let patternType = recentCount > Int(historicalAverage) ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_count": "\(recentCount)",
            "historical_average": String(format: "%.1f", historicalAverage),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalAverage,
            recentValue: Double(recentCount),
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 40 // 40% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "feeding_frequency",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .feeding,
            title: "Feeding Frequency \(patternType.capitalized)",
            metricString: "\(recentCount) feedings in 24 hours",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeVolume(recentEntries: [FeedingEntry], historicalEntries: [FeedingEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Filter for bottle feedings with volume data
        let recentBottleFeedings = recentEntries.filter { $0.type == .bottleFeeding && $0.volume != nil }
        let historicalBottleFeedings = historicalEntries.filter { $0.type == .bottleFeeding && $0.volume != nil }

        // Only proceed if we have enough data
        guard !recentBottleFeedings.isEmpty, !historicalBottleFeedings.isEmpty else {
            return nil
        }

        // Calculate average volume per feeding
        let recentVolumes = recentBottleFeedings.compactMap { $0.volume }
        let historicalVolumes = historicalBottleFeedings.compactMap { $0.volume }

        let recentAverage = recentVolumes.reduce(0, +) / Double(recentVolumes.count)
        let historicalAverage = historicalVolumes.reduce(0, +) / Double(historicalVolumes.count)

        // Calculate deviation
        let deviationPercentage = abs((recentAverage - historicalAverage) / historicalAverage) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 15 // 15% change in feeding volume

        // Determine pattern type
        let patternType = recentAverage > historicalAverage ? "increased" : "decreased"

        // Get feeding type
        let feedingType = recentBottleFeedings.first?.content?.rawValue ?? "formula"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_value": String(format: "%.0f", recentAverage),
            "historical_average": String(format: "%.0f", historicalAverage),
            "change_percentage": String(format: "%.0f", deviationPercentage),
            "feeding_type": feedingType
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalAverage,
            recentValue: recentAverage,
            additionalMetrics: ["feeding_type": feedingType]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 30 // 30% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "feeding_volume",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .feeding,
            title: "Feeding Volume \(patternType.capitalized)",
            metricString: "\(String(format: "%.0f", recentAverage))ml per feeding",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeDuration(recentEntries: [FeedingEntry], historicalEntries: [FeedingEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Filter for breastfeedings with duration data
        let recentBreastFeedings = recentEntries.filter { $0.type == .breastfeeding && $0.duration != nil }
        let historicalBreastFeedings = historicalEntries.filter { $0.type == .breastfeeding && $0.duration != nil }

        // Only proceed if we have enough data
        guard !recentBreastFeedings.isEmpty, !historicalBreastFeedings.isEmpty else {
            return nil
        }

        // Calculate average duration per feeding
        let recentDurations = recentBreastFeedings.compactMap { $0.duration }
        let historicalDurations = historicalBreastFeedings.compactMap { $0.duration }

        let recentAverage = Double(recentDurations.reduce(0, +)) / Double(recentDurations.count)
        let historicalAverage = Double(historicalDurations.reduce(0, +)) / Double(historicalDurations.count)

        // Calculate deviation
        let deviationPercentage = abs((recentAverage - historicalAverage) / historicalAverage) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 20 // 20% change in feeding duration

        // Determine pattern type
        let patternType = recentAverage > historicalAverage ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_value": String(format: "%.0f", recentAverage),
            "historical_average": String(format: "%.0f", historicalAverage),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalAverage,
            recentValue: recentAverage,
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 40 // 40% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "feeding_duration",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .feeding,
            title: "Breastfeeding Duration \(patternType.capitalized)",
            metricString: "\(String(format: "%.0f", recentAverage)) minutes per feeding",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeTypeDistribution(recentEntries: [FeedingEntry], historicalEntries: [FeedingEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Only proceed if we have enough data
        guard recentEntries.count >= 3 else {
            return nil
        }

        // Count feeding types
        let recentBreastCount = recentEntries.filter { $0.type == .breastfeeding }.count
        let recentBottleCount = recentEntries.filter { $0.type == .bottleFeeding }.count
        let recentSolidCount = recentEntries.filter { $0.type == .solidFood }.count

        let totalRecentCount = recentEntries.count

        // Calculate percentages
        let breastPercentage = (Double(recentBreastCount) / Double(totalRecentCount)) * 100
        let bottlePercentage = (Double(recentBottleCount) / Double(totalRecentCount)) * 100
        let solidPercentage = (Double(recentSolidCount) / Double(totalRecentCount)) * 100

        // Determine primary feeding type
        let primaryType: String
        if recentBreastCount >= recentBottleCount && recentBreastCount >= recentSolidCount {
            primaryType = "breastfeeding"
        } else if recentBottleCount >= recentBreastCount && recentBottleCount >= recentSolidCount {
            primaryType = "bottle feeding"
        } else {
            primaryType = "solid food"
        }

        // Prepare distribution string
        let distributionString = String(format: "%.0f%% breast, %.0f%% bottle, %.0f%% solid", breastPercentage, bottlePercentage, solidPercentage)

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "breast_percentage": String(format: "%.0f", breastPercentage),
            "bottle_percentage": String(format: "%.0f", bottlePercentage),
            "solid_percentage": String(format: "%.0f", solidPercentage),
            "primary_type": primaryType,
            "feeding_count": "\(totalRecentCount)"
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: 0, // Not applicable for distribution
            patternType: "distribution",
            historicalAverage: 0, // Not applicable for distribution
            recentValue: 0, // Not applicable for distribution
            additionalMetrics: [
                "breast_percentage": breastPercentage,
                "bottle_percentage": bottlePercentage,
                "solid_percentage": solidPercentage,
                "primary_type": primaryType
            ]
        )

        // Always generate this insight if we have enough data
        return AnalysisResult(
            insightType: "feeding_type_distribution",
            metrics: metrics,
            exceedsThreshold: true,
            templateVariables: templateVariables,
            category: .feeding,
            title: "Feeding Type Distribution",
            metricString: distributionString,
            needsAttention: false,
            confidence: 90
        )
    }

    // MARK: - Helper Methods

    private func calculateBabyAge(baby: Baby) -> (months: Int, weeks: Int) {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month, .day], from: baby.birthDate, to: Date())

        let months = ageComponents.month ?? 0
        let days = ageComponents.day ?? 0
        let weeks = (months * 30 + days) / 7 // Approximate weeks

        return (months, weeks)
    }
}
