//
//  EnhancedSleepAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Enhanced analyzer for sleep entries with advanced analysis capabilities
class EnhancedSleepAnalyzer: SleepAnalyzer, EnhancedDataAnalyzerProtocol {
    // MARK: - Pattern Detection

    func detectPatterns(in entries: [SleepEntry]) -> [PatternResult] {
        var patterns: [PatternResult] = []

        // Detect day/night reversal
        let dayNightRatio = PatternDetectionUtilities.calculateDayNightRatio(entries: entries)
        if dayNightRatio > 1.5 {
            patterns.append(PatternResult(
                type: "day_night_reversal",
                confidence: min(100, Int(dayNightRatio * 50)),
                description: "More sleep during day than night",
                metrics: ["day_night_ratio": dayNightRatio]
            ))
        }

        // Detect sleep consolidation
        let consolidationScore = PatternDetectionUtilities.calculateSleepConsolidation(entries: entries)
        patterns.append(PatternResult(
            type: "sleep_consolidation",
            confidence: consolidationScore,
            description: consolidationScore > 70 ? "Sleep becoming more consolidated" : "Sleep still fragmented",
            metrics: ["consolidation_score": Double(consolidationScore)]
        ))

        // Detect sleep associations
        if let associations = PatternDetectionUtilities.detectSleepAssociations(entries: entries) {
            patterns.append(PatternResult(
                type: "sleep_associations",
                confidence: associations.confidence,
                description: "Sleep associated with \(associations.method)",
                metrics: ["primary_association": associations.method]
            ))
        }

        return patterns
    }

    // MARK: - Cyclical Pattern Detection

    func detectCyclicalPatterns(in entries: [SleepEntry]) -> [CyclicalPattern] {
        guard entries.count >= 10 else { return [] }

        var cyclicalPatterns: [CyclicalPattern] = []

        // Extract timestamps and durations for analysis
        let timestamps = entries.map { $0.timestamp }
        let durations = entries.compactMap { entry -> Double? in
            if let duration = entry.duration {
                return Double(duration)
            }
            return nil
        }

        // Detect cyclical patterns in sleep duration
        if let durationPattern = PatternDetectionUtilities.detectCyclicalPattern(
            timestamps: timestamps,
            values: durations,
            category: .sleep,
            metric: "total_duration"
        ) {
            cyclicalPatterns.append(durationPattern)
        }

        // Extract timestamps and start times (hour of day) for nap timing analysis
        let startTimeHours = entries.map { entry -> Double in
            let hour = Calendar.current.component(.hour, from: entry.timestamp)
            let minute = Calendar.current.component(.minute, from: entry.timestamp)
            return Double(hour) + Double(minute) / 60.0
        }

        // Detect cyclical patterns in sleep timing
        if let timingPattern = PatternDetectionUtilities.detectCyclicalPattern(
            timestamps: timestamps,
            values: startTimeHours,
            category: .sleep,
            metric: "start_time"
        ) {
            cyclicalPatterns.append(timingPattern)
        }

        return cyclicalPatterns
    }

    // MARK: - Trend Analysis

    func analyzeTrends(in entries: [SleepEntry]) -> [TrendAnalysis] {
        guard entries.count >= 5 else { return [] }

        var trends: [TrendAnalysis] = []

        // Extract timestamps and durations for analysis
        let timestamps = entries.map { $0.timestamp }
        let durations = entries.compactMap { entry -> Double? in
            if let duration = entry.duration {
                return Double(duration)
            }
            return nil
        }

        // Analyze trends in sleep duration
        if let durationTrend = PatternDetectionUtilities.detectTrend(
            timestamps: timestamps,
            values: durations,
            category: .sleep,
            metric: "total_duration"
        ) {
            trends.append(durationTrend)
        }

        // Calculate and analyze longest sleep stretches
        var longestStretches: [(timestamp: Date, value: Double)] = []

        // Group entries by day
        let calendar = Calendar.current
        var entriesByDay: [Date: [SleepEntry]] = [:]

        for entry in entries {
            let day = calendar.startOfDay(for: entry.timestamp)
            entriesByDay[day, default: []].append(entry)
        }

        // Find longest stretch for each day
        for (day, dayEntries) in entriesByDay {
            let longestStretch = dayEntries.compactMap { $0.duration }.max() ?? 0
            longestStretches.append((timestamp: day, value: Double(longestStretch)))
        }

        // Only analyze if we have enough data points
        if longestStretches.count >= 3 {
            let stretchTimestamps = longestStretches.map { $0.timestamp }
            let stretchValues = longestStretches.map { $0.value }

            if let stretchTrend = PatternDetectionUtilities.detectTrend(
                timestamps: stretchTimestamps,
                values: stretchValues,
                category: .sleep,
                metric: "longest_stretch"
            ) {
                trends.append(stretchTrend)
            }
        }

        return trends
    }

    // MARK: - Anomaly Detection

    func detectAnomalies(recentEntries: [SleepEntry], historicalEntries: [SleepEntry]) -> [AnomalyDetection] {
        guard !recentEntries.isEmpty, historicalEntries.count >= 5 else { return [] }

        var anomalies: [AnomalyDetection] = []

        // Calculate total sleep duration for recent entries (last 24 hours)
        let recentTotalDuration = recentEntries.reduce(0.0) { total, entry in
            if let duration = entry.duration {
                return total + Double(duration)
            }
            return total
        }

        // Calculate historical daily sleep durations
        let calendar = Calendar.current
        var dailyDurations: [Double] = []
        var entriesByDay: [Date: [SleepEntry]] = [:]

        for entry in historicalEntries {
            let day = calendar.startOfDay(for: entry.timestamp)
            entriesByDay[day, default: []].append(entry)
        }

        for (_, dayEntries) in entriesByDay {
            let dailyDuration = dayEntries.reduce(0.0) { total, entry in
                if let duration = entry.duration {
                    return total + Double(duration)
                }
                return total
            }
            dailyDurations.append(dailyDuration)
        }

        // Detect anomalies in total sleep duration
        if let durationAnomaly = PatternDetectionUtilities.detectAnomaly(
            value: recentTotalDuration,
            historicalValues: dailyDurations,
            category: .sleep,
            metric: "total_duration"
        ) {
            anomalies.append(durationAnomaly)
        }

        // Calculate longest sleep stretch for recent entries
        let recentLongestStretch = recentEntries.compactMap { $0.duration }.max() ?? 0

        // Calculate historical longest sleep stretches
        var historicalLongestStretches: [Double] = []

        for (_, dayEntries) in entriesByDay {
            let longestStretch = dayEntries.compactMap { $0.duration }.max() ?? 0
            historicalLongestStretches.append(Double(longestStretch))
        }

        // Detect anomalies in longest sleep stretch
        if let stretchAnomaly = PatternDetectionUtilities.detectAnomaly(
            value: Double(recentLongestStretch),
            historicalValues: historicalLongestStretches,
            category: .sleep,
            metric: "longest_stretch"
        ) {
            anomalies.append(stretchAnomaly)
        }

        return anomalies
    }

    // MARK: - Cross-category Correlation

    func correlateWith(category: Insight.InsightCategory, for baby: Baby, modelContext: ModelContext) -> [CorrelationAnalysis] {
        // Use the correlation service to analyze correlations
        let correlationService = CorrelationAnalysisService(modelContext: modelContext)
        return correlationService.analyzeCorrelation(
            primaryCategory: .sleep,
            secondaryCategory: category,
            for: baby,
            timeframe: .biweekly
        )
    }

    // MARK: - Predictions

    func generatePredictions(for baby: Baby, modelContext: ModelContext) -> [Prediction] {
        var predictions: [Prediction] = []

        // Get recent sleep entries
        let recentEntries = fetchEntries(
            for: baby,
            inTimeRange: .last7Days,
            modelContext: modelContext
        )

        guard recentEntries.count >= 5 else { return [] }

        // Predict next sleep time based on patterns
        if let nextSleepPrediction = predictNextSleep(entries: recentEntries) {
            predictions.append(nextSleepPrediction)
        }

        return predictions
    }

    // MARK: - Developmental Context

    func applyDevelopmentalContext(to results: [AnalysisResult], baby: Baby, modelContext: ModelContext) -> [AnalysisResult] {
        let developmentalService = DevelopmentalDataService.shared
        developmentalService.setModelContext(modelContext)

        // Get developmental context
        let context = developmentalService.createDevelopmentalContext(for: baby)

        // Apply context to results
        return results.map { result in
            var templateVariables = result.templateVariables

            // Add developmental context variables
            for (key, value) in context.templateVariables {
                templateVariables[key] = value
            }

            // Check for milestone proximity
            let milestoneProximity = developmentalService.detectMilestoneProximity(
                for: baby,
                category: .sleep
            )

            if let (milestone, proximity, indicators) = milestoneProximity.first {
                templateVariables["milestone_name"] = milestone.name
                templateVariables["milestone_proximity"] = String(format: "%.0f%%", proximity * 100)

                if !indicators.isEmpty {
                    templateVariables["milestone_indicators"] = indicators.joined(separator: ", ")
                }
            }

            // Create new result with enhanced variables
            return AnalysisResult(
                insightType: result.insightType,
                metrics: result.metrics,
                exceedsThreshold: result.exceedsThreshold,
                templateVariables: templateVariables,
                category: result.category,
                title: result.title,
                metricString: result.metricString,
                needsAttention: result.needsAttention,
                confidence: result.confidence
            )
        }
    }

    // MARK: - Helper Methods

    /// Predict the next sleep time based on patterns
    /// - Parameter entries: Recent sleep entries
    /// - Returns: Prediction for next sleep time, or nil if not enough data
    private func predictNextSleep(entries: [SleepEntry]) -> Prediction? {
        guard entries.count >= 5 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Group entries by day
        let calendar = Calendar.current
        var entriesByDay: [Date: [SleepEntry]] = [:]

        for entry in sortedEntries {
            let day = calendar.startOfDay(for: entry.timestamp)
            entriesByDay[day, default: []].append(entry)
        }

        // Calculate average number of sleeps per day
        let averageSleepsPerDay = Double(entries.count) / Double(entriesByDay.count)

        // Calculate average time between sleeps
        var timeIntervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            let interval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)

            // Only consider intervals less than 12 hours (to avoid overnight gaps)
            if interval < 12 * 3600 {
                timeIntervals.append(interval)
            }
        }

        // If we don't have enough intervals, return nil
        guard !timeIntervals.isEmpty else { return nil }

        let averageInterval = timeIntervals.reduce(0, +) / Double(timeIntervals.count)

        // Get the most recent sleep entry
        guard let lastSleep = sortedEntries.last else { return nil }

        // Calculate the predicted next sleep time
        let predictedTime = lastSleep.timestamp.addingTimeInterval(averageInterval)

        // Only predict if it's in the future
        guard predictedTime > Date() else { return nil }

        // Create a time window for the prediction (±30 minutes)
        let startTime = predictedTime.addingTimeInterval(-30 * 60)
        let endTime = predictedTime.addingTimeInterval(30 * 60)

        // Calculate confidence based on consistency of intervals
        let stdDev = calculateStandardDeviation(timeIntervals)
        let coefficientOfVariation = stdDev / averageInterval

        // Higher consistency = higher confidence
        let confidence = min(90, max(50, Int(100 - coefficientOfVariation * 100)))

        return Prediction(
            type: "next_sleep",
            description: "Next sleep session likely around \(formatTime(predictedTime))",
            confidence: confidence,
            timeframe: DateInterval(start: startTime, end: endTime),
            predictedValue: averageInterval / 60, // Convert to minutes
            confidenceInterval: (averageInterval - stdDev) / 60...(averageInterval + stdDev) / 60,
            forecastModel: "time_series_analysis",
            errorMetrics: ["coefficient_of_variation": coefficientOfVariation]
        )
    }

    /// Calculate standard deviation of a set of values
    /// - Parameter values: Array of values
    /// - Returns: Standard deviation
    private func calculateStandardDeviation(_ values: [TimeInterval]) -> Double {
        let count = Double(values.count)
        let mean = values.reduce(0, +) / count
        let variance = values.reduce(0.0) { sum, value in
            let diff = value - mean
            return sum + (diff * diff)
        } / count

        return sqrt(variance)
    }

    /// Format a date as a time string
    /// - Parameter date: The date to format
    /// - Returns: Formatted time string
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}
