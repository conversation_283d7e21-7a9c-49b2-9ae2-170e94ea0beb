//
//  DiaperAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Analyzer for diaper entries
class DiaperAnalyzer: DataAnalyzerProtocol {
    typealias EntryType = DiaperEntry

    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [DiaperEntry] {
        // Create a basic fetch descriptor without predicates
        var fetchDescriptor = FetchDescriptor<DiaperEntry>()
        fetchDescriptor.sortBy = [SortDescriptor(\DiaperEntry.timestamp, order: .reverse)]
        
        do {
            // Fetch all entries
            let allEntries = try modelContext.fetch(fetchDescriptor)
            
            // Then filter manually
            let startDate = timeRange.startDate
            
            return allEntries.filter { entry in
                guard let entryBaby = entry.baby else { return false }
                return entryBaby.id == baby.id && entry.timestamp >= startDate
            }
        } catch {
            print("Error fetching diaper entries: \(error.localizedDescription)")
            return []
        }
    }

    func analyzeData(recentEntries: [DiaperEntry], historicalEntries: [DiaperEntry], baby: Baby) -> [AnalysisResult] {
        var results: [AnalysisResult] = []

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Calculate baby's age
        let babyAge = calculateBabyAge(baby: baby)

        // Analyze diaper frequency
        if let frequencyResult = analyzeFrequency(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(frequencyResult)
        }

        // Analyze stool characteristics
        if let characteristicsResult = analyzeStoolCharacteristics(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(characteristicsResult)
        }

        return results
    }

    // MARK: - Private Analysis Methods

    private func analyzeFrequency(recentEntries: [DiaperEntry], historicalEntries: [DiaperEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Count diaper changes in the last 24 hours
        let recentCount = recentEntries.count

        // Count by type
        let wetCount = recentEntries.filter { $0.type == .wet }.count
        let dirtyCount = recentEntries.filter { $0.type == .dirty }.count
        let mixedCount = recentEntries.filter { $0.type == .mixed }.count

        // Calculate average daily diaper changes from historical data
        let daysInPeriod = min(7, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
        let historicalAverage = Double(historicalEntries.count) / Double(max(1, daysInPeriod))

        // Calculate deviation
        let deviationPercentage = abs((Double(recentCount) - historicalAverage) / historicalAverage) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 25 // 25% change in diaper frequency

        // Determine pattern type
        let patternType = recentCount > Int(historicalAverage) ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_count": "\(recentCount)",
            "historical_average": String(format: "%.1f", historicalAverage),
            "change_percentage": String(format: "%.0f", deviationPercentage),
            "wet_count": "\(wetCount)",
            "dirty_count": "\(dirtyCount)",
            "mixed_count": "\(mixedCount)"
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalAverage,
            recentValue: Double(recentCount),
            additionalMetrics: [
                "wet_count": wetCount,
                "dirty_count": dirtyCount,
                "mixed_count": mixedCount
            ]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 40 // 40% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "diaper_frequency",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .diaper,
            title: "Diaper Frequency \(patternType.capitalized)",
            metricString: "\(recentCount) changes in 24 hours",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeStoolCharacteristics(recentEntries: [DiaperEntry], historicalEntries: [DiaperEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Filter for dirty or mixed diapers
        let recentStoolDiapers = recentEntries.filter { $0.type == .dirty || $0.type == .mixed }

        // Only proceed if we have stool diapers
        guard !recentStoolDiapers.isEmpty else {
            return nil
        }

        // Get most recent stool characteristics
        guard let mostRecentStool = recentStoolDiapers.max(by: { $0.timestamp < $1.timestamp }),
              let stoolColor = mostRecentStool.poopColor,
              let stoolConsistency = mostRecentStool.poopConsistency else {
            return nil
        }

        // Determine if there's a change in stool characteristics
        let previousStools = historicalEntries.filter { $0.type == .dirty || $0.type == .mixed }
            .filter { $0.timestamp < mostRecentStool.timestamp }
            .sorted(by: { $0.timestamp > $1.timestamp })
            .prefix(3)

        var consistencyChange = "no change"
        if let previousStool = previousStools.first,
           let previousConsistency = previousStool.poopConsistency,
           previousConsistency != stoolConsistency {
            consistencyChange = "changed from \(previousConsistency.description) to \(stoolConsistency.description)"
        }

        // Determine feeding type (for context)
        let feedingType = determineFeedingType(for: baby)

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "stool_color": stoolColor.description,
            "stool_consistency": stoolConsistency.description,
            "consistency_change": consistencyChange,
            "feeding_type": feedingType
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: 0, // Not applicable for characteristics
            patternType: "characteristics",
            historicalAverage: 0, // Not applicable for characteristics
            recentValue: 0, // Not applicable for characteristics
            additionalMetrics: [
                "stool_color": stoolColor.rawValue,
                "stool_consistency": stoolConsistency.rawValue,
                "consistency_change": consistencyChange,
                "feeding_type": feedingType
            ]
        )

        // Determine if this needs attention based on color
        let needsAttention = stoolColor == .red || stoolColor == .white || stoolColor == .black

        // Always generate this insight if we have stool data
        return AnalysisResult(
            insightType: "diaper_characteristics",
            metrics: metrics,
            exceedsThreshold: true,
            templateVariables: templateVariables,
            category: .diaper,
            title: "Stool Characteristics",
            metricString: "\(stoolColor.description), \(stoolConsistency.description)",
            needsAttention: needsAttention,
            confidence: 85
        )
    }

    // MARK: - Helper Methods

    private func determineFeedingType(for baby: Baby) -> String {
        // This would ideally analyze recent feeding entries to determine the primary feeding type
        // For now, return a generic value
        return "mixed"
    }

    private func calculateBabyAge(baby: Baby) -> (months: Int, weeks: Int) {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month, .day], from: baby.birthDate, to: Date())

        let months = ageComponents.month ?? 0
        let days = ageComponents.day ?? 0
        let weeks = (months * 30 + days) / 7 // Approximate weeks

        return (months, weeks)
    }
}
