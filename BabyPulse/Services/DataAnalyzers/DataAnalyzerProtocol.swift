//
//  DataAnalyzerProtocol.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Protocol for data analyzers that process different types of entries
protocol DataAnalyzerProtocol {
    associatedtype EntryType

    /// Analyze recent entries and compare with historical data
    /// - Parameters:
    ///   - recentEntries: Entries from the last 24 hours
    ///   - historicalEntries: Entries from the last 7 days
    ///   - baby: The baby whose data is being analyzed
    /// - Returns: Array of analysis results
    func analyzeData(recentEntries: [EntryType], historicalEntries: [EntryType], baby: Baby) -> [AnalysisResult]

    /// Fetch entries for a specific baby within a time range
    /// - Parameters:
    ///   - baby: The baby whose entries to fetch
    ///   - timeRange: The time range to fetch entries for
    ///   - modelContext: The SwiftData model context
    /// - Returns: Array of entries
    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [EntryType]
}

/// Represents the result of data analysis
struct AnalysisResult {
    /// The type of insight this analysis could generate
    let insightType: String

    /// The metrics calculated from the analysis
    let metrics: AnalysisMetrics

    /// Whether this result exceeds thresholds for generating an insight
    let exceedsThreshold: Bool

    /// Variables to be used in the prompt template
    let templateVariables: [String: String]

    /// The category of the insight
    let category: Insight.InsightCategory

    /// The title for the insight
    let title: String

    /// The metric string for the insight
    let metricString: String

    /// Whether this insight needs attention
    let needsAttention: Bool

    /// The confidence level (0-100)
    let confidence: Int
}

/// Metrics calculated from data analysis
struct AnalysisMetrics {
    /// The percentage deviation from historical average
    let deviationPercentage: Double

    /// The type of pattern or regression detected
    let patternType: String

    /// The historical average value
    let historicalAverage: Double

    /// The recent value
    let recentValue: Double

    /// Additional metrics specific to the entry type
    let additionalMetrics: [String: Any]
}


