//
//  GrowthAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Analyzer for growth entries
class GrowthAnalyzer: DataAnalyzerProtocol {
    typealias EntryType = GrowthEntry

    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [GrowthEntry] {
        // Create a basic fetch descriptor without predicates
        var fetchDescriptor = FetchDescriptor<GrowthEntry>()
        fetchDescriptor.sortBy = [SortDescriptor(\GrowthEntry.timestamp, order: .reverse)]
        
        do {
            // Fetch all entries
            let allEntries = try modelContext.fetch(fetchDescriptor)
            
            // Then filter manually
            let startDate = timeRange.startDate
            
            return allEntries.filter { entry in
                guard let entryBaby = entry.baby else { return false }
                return entryBaby.id == baby.id && entry.timestamp >= startDate
            }
        } catch {
            print("Error fetching growth entries: \(error.localizedDescription)")
            return []
        }
    }

    func analyzeData(recentEntries: [GrowthEntry], historicalEntries: [GrowthEntry], baby: Baby) -> [AnalysisResult] {
        var results: [AnalysisResult] = []

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Calculate baby's age
        let babyAge = calculateBabyAge(baby: baby)

        // Analyze weight gain
        if let weightResult = analyzeWeightGain(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(weightResult)
        }

        // Analyze height increase
        if let heightResult = analyzeHeightIncrease(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(heightResult)
        }

        // Analyze head circumference increase
        if let headCircumferenceResult = analyzeHeadCircumferenceIncrease(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(headCircumferenceResult)
        }

        return results
    }

    // MARK: - Private Analysis Methods

    private func analyzeWeightGain(recentEntries: [GrowthEntry], historicalEntries: [GrowthEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Get most recent weight
        guard let recentWeight = recentEntries.first?.weight else {
            return nil
        }

        // Get historical weight
        guard let historicalWeight = historicalEntries.first?.weight else {
            return nil
        }

        // Calculate weight gain
        let weightGain = recentWeight - historicalWeight

        // Calculate deviation percentage
        let deviationPercentage = abs(weightGain / historicalWeight) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 10 // 10% change in weight

        // Determine pattern type
        let patternType = weightGain > 0 ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_weight": String(format: "%.1f", recentWeight),
            "historical_weight": String(format: "%.1f", historicalWeight),
            "weight_gain": String(format: "%.1f", weightGain),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalWeight,
            recentValue: recentWeight,
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 20 // 20% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "weight_gain",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .growth,
            title: "Weight Gain \(patternType.capitalized)",
            metricString: "\(String(format: "%.1f", weightGain)) kg",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeHeightIncrease(recentEntries: [GrowthEntry], historicalEntries: [GrowthEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Get most recent height
        guard let recentHeight = recentEntries.first?.height else {
            return nil
        }

        // Get historical height
        guard let historicalHeight = historicalEntries.first?.height else {
            return nil
        }

        // Calculate height increase
        let heightIncrease = recentHeight - historicalHeight

        // Calculate deviation percentage
        let deviationPercentage = abs(heightIncrease / historicalHeight) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 10 // 10% change in height

        // Determine pattern type
        let patternType = heightIncrease > 0 ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_height": String(format: "%.1f", recentHeight),
            "historical_height": String(format: "%.1f", historicalHeight),
            "height_increase": String(format: "%.1f", heightIncrease),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalHeight,
            recentValue: recentHeight,
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 20 // 20% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "height_increase",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .growth,
            title: "Height Increase \(patternType.capitalized)",
            metricString: "\(String(format: "%.1f", heightIncrease)) cm",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeHeadCircumferenceIncrease(recentEntries: [GrowthEntry], historicalEntries: [GrowthEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Get most recent head circumference
        guard let recentHeadCircumference = recentEntries.first?.headCircumference else {
            return nil
        }

        // Get historical head circumference
        guard let historicalHeadCircumference = historicalEntries.first?.headCircumference else {
            return nil
        }

        // Calculate head circumference increase
        let headCircumferenceIncrease = recentHeadCircumference - historicalHeadCircumference

        // Calculate deviation percentage
        let deviationPercentage = abs(headCircumferenceIncrease / historicalHeadCircumference) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 10 // 10% change in head circumference

        // Determine pattern type
        let patternType = headCircumferenceIncrease > 0 ? "increased" : "decreased"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_head_circumference": String(format: "%.1f", recentHeadCircumference),
            "historical_head_circumference": String(format: "%.1f", historicalHeadCircumference),
            "head_circumference_increase": String(format: "%.1f", headCircumferenceIncrease),
            "change_percentage": String(format: "%.0f", deviationPercentage)
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalHeadCircumference,
            recentValue: recentHeadCircumference,
            additionalMetrics: [:]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 20 // 20% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "head_circumference_increase",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .growth,
            title: "Head Circumference Increase \(patternType.capitalized)",
            metricString: "\(String(format: "%.1f", headCircumferenceIncrease)) cm",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    // MARK: - Helper Methods

    private func calculateBabyAge(baby: Baby) -> (months: Int, weeks: Int) {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month, .day], from: baby.birthDate, to: Date())

        let months = ageComponents.month ?? 0
        let days = ageComponents.day ?? 0
        let weeks = (months * 30 + days) / 7 // Approximate weeks

        return (months, weeks)
    }
}
