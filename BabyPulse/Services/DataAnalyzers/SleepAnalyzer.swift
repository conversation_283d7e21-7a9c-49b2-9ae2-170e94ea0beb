//
//  SleepAnalyzer.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Analyzer for sleep entries
class SleepAnalyzer: DataAnalyzerProtocol {
    typealias EntryType = SleepEntry

    func fetchEntries(for baby: Baby, inTimeRange timeRange: TimeRange, modelContext: ModelContext) -> [SleepEntry] {
        // Create a basic fetch descriptor without predicates
        var fetchDescriptor = FetchDescriptor<SleepEntry>()
        fetchDescriptor.sortBy = [SortDescriptor(\SleepEntry.timestamp, order: .reverse)]
        
        do {
            // Fetch all entries
            let allEntries = try modelContext.fetch(fetchDescriptor)
            
            // Then filter manually
            let startDate = timeRange.startDate
            
            return allEntries.filter { entry in
                guard let entryBaby = entry.baby else { return false }
                return entryBaby.id == baby.id && entry.timestamp >= startDate
            }
        } catch {
            print("Error fetching sleep entries: \(error.localizedDescription)")
            return []
        }
    }

    func analyzeData(recentEntries: [SleepEntry], historicalEntries: [SleepEntry], baby: Baby) -> [AnalysisResult] {
        var results: [AnalysisResult] = []

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Calculate baby's age
        let babyAge = calculateBabyAge(baby: baby)

        // Analyze total sleep duration
        if let durationResult = analyzeTotalDuration(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(durationResult)
        }

        // Analyze sleep patterns (number of sessions, longest stretch)
        if let patternResult = analyzeSleepPattern(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(patternResult)
        }

        // Analyze day/night distribution
        if let dayNightResult = analyzeDayNightDistribution(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby, babyAge: babyAge) {
            results.append(dayNightResult)
        }

        return results
    }

    // MARK: - Private Analysis Methods

    private func analyzeTotalDuration(recentEntries: [SleepEntry], historicalEntries: [SleepEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Calculate total sleep duration in the last 24 hours
        let recentDuration = calculateTotalSleepDuration(entries: recentEntries)

        // Calculate average daily sleep duration from historical data
        let daysInPeriod = min(7, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
        let totalHistoricalDuration = calculateTotalSleepDuration(entries: historicalEntries)
        let historicalAverage = totalHistoricalDuration / Double(max(1, daysInPeriod))

        // Convert to hours for display
        let recentHours = recentDuration / 60.0
        let historicalHours = historicalAverage / 60.0

        // Calculate deviation
        let deviationPercentage = abs((recentHours - historicalHours) / historicalHours) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 15 // 15% change in sleep duration

        // Determine pattern type
        let patternType = recentHours > historicalHours ? "increased" : "decreased"

        // Calculate day/night ratio
        let dayNightRatio = calculateDayNightRatio(entries: recentEntries)

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_value": String(format: "%.1f", recentHours),
            "historical_average": String(format: "%.1f", historicalHours),
            "change_percentage": String(format: "%.0f", deviationPercentage),
            "day_night_ratio": dayNightRatio
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalHours,
            recentValue: recentHours,
            additionalMetrics: ["day_night_ratio": dayNightRatio]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 25 // 25% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "sleep_duration",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .sleep,
            title: "Sleep Duration \(patternType.capitalized)",
            metricString: "\(String(format: "%.1f", recentHours)) hours in 24 hours",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeSleepPattern(recentEntries: [SleepEntry], historicalEntries: [SleepEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Count sleep sessions in the last 24 hours
        let recentCount = recentEntries.count

        // Calculate average daily sleep sessions from historical data
        let daysInPeriod = min(7, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
        let historicalAverage = Double(historicalEntries.count) / Double(max(1, daysInPeriod))

        // Calculate deviation
        let deviationPercentage = abs((Double(recentCount) - historicalAverage) / historicalAverage) * 100

        // Calculate longest sleep stretch
        let longestStretch = calculateLongestSleepStretch(entries: recentEntries)

        // Determine most common sleep location
        let commonLocation = findMostCommonSleepLocation(entries: recentEntries)

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 20 // 20% change in sleep sessions

        // Determine pattern type
        let patternType = recentCount > Int(historicalAverage) ? "fragmented" : "consolidated"

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "recent_count": "\(recentCount)",
            "historical_average": String(format: "%.1f", historicalAverage),
            "change_percentage": String(format: "%.0f", deviationPercentage),
            "longest_stretch": String(format: "%.1f", longestStretch),
            "common_location": commonLocation
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: historicalAverage,
            recentValue: Double(recentCount),
            additionalMetrics: [
                "longest_stretch": longestStretch,
                "common_location": commonLocation
            ]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 30 // 30% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "sleep_pattern",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .sleep,
            title: "Sleep Pattern \(patternType.capitalized)",
            metricString: "\(recentCount) sleep sessions, \(String(format: "%.1f", longestStretch))h longest",
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    private func analyzeDayNightDistribution(recentEntries: [SleepEntry], historicalEntries: [SleepEntry], baby: Baby, babyAge: (months: Int, weeks: Int)) -> AnalysisResult? {
        // Only proceed if we have enough data
        guard recentEntries.count >= 2 else {
            return nil
        }

        // Calculate day/night sleep distribution
        let (dayMinutes, nightMinutes) = calculateDayNightSleepMinutes(entries: recentEntries)

        // Calculate historical day/night distribution
        let (histDayMinutes, histNightMinutes) = calculateDayNightSleepMinutes(entries: historicalEntries)

        // Convert to hours for display
        let dayHours = dayMinutes / 60.0
        let nightHours = nightMinutes / 60.0
        let totalHours = (dayMinutes + nightMinutes) / 60.0

        let histDayHours = histDayMinutes / Double(max(1, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count))
        let histNightHours = histNightMinutes / Double(max(1, Set(historicalEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count))

        // Calculate percentages
        let dayPercentage = (dayMinutes / (dayMinutes + nightMinutes)) * 100
        let nightPercentage = (nightMinutes / (dayMinutes + nightMinutes)) * 100

        let histDayPercentage = (histDayMinutes / (histDayMinutes + histNightMinutes)) * 100
        let histNightPercentage = (histNightMinutes / (histDayMinutes + histNightMinutes)) * 100

        // Calculate deviation in night sleep percentage
        let deviationPercentage = abs((nightPercentage - histNightPercentage) / histNightPercentage) * 100

        // Determine if this exceeds threshold for generating an insight
        let exceedsThreshold = deviationPercentage >= 15 // 15% change in day/night distribution

        // Determine pattern type
        let patternType = nightPercentage > histNightPercentage ? "improved" : "shifted"

        // Prepare day/night ratio string
        let dayNightRatio = String(format: "%.0f%% day, %.0f%% night", dayPercentage, nightPercentage)

        // Prepare template variables
        let templateVariables: [String: String] = [
            "baby_name": baby.name,
            "baby_age": "\(babyAge.months) months",
            "baby_age_weeks": "\(babyAge.weeks)",
            "day_hours": String(format: "%.1f", dayHours),
            "night_hours": String(format: "%.1f", nightHours),
            "total_hours": String(format: "%.1f", totalHours),
            "day_percentage": String(format: "%.0f", dayPercentage),
            "night_percentage": String(format: "%.0f", nightPercentage),
            "historical_day_percentage": String(format: "%.0f", histDayPercentage),
            "historical_night_percentage": String(format: "%.0f", histNightPercentage),
            "change_percentage": String(format: "%.0f", deviationPercentage),
            "day_night_ratio": dayNightRatio
        ]

        // Create analysis metrics
        let metrics = AnalysisMetrics(
            deviationPercentage: deviationPercentage,
            patternType: patternType,
            historicalAverage: histNightPercentage,
            recentValue: nightPercentage,
            additionalMetrics: [
                "day_hours": dayHours,
                "night_hours": nightHours,
                "day_percentage": dayPercentage,
                "night_percentage": nightPercentage
            ]
        )

        // Determine if this needs attention
        let needsAttention = deviationPercentage >= 25 // 25% is a significant change

        // Calculate confidence level
        let confidence = max(50, 100 - Int(deviationPercentage / 2))

        return AnalysisResult(
            insightType: "sleep_day_night_distribution",
            metrics: metrics,
            exceedsThreshold: exceedsThreshold,
            templateVariables: templateVariables,
            category: .sleep,
            title: "Day/Night Sleep Distribution",
            metricString: dayNightRatio,
            needsAttention: needsAttention,
            confidence: confidence
        )
    }

    // MARK: - Helper Methods

    private func calculateTotalSleepDuration(entries: [SleepEntry]) -> Double {
        var totalMinutes = 0.0

        for entry in entries {
            if let duration = entry.calculatedDuration {
                totalMinutes += Double(duration)
            }
        }

        return totalMinutes
    }

    private func calculateDayNightRatio(entries: [SleepEntry]) -> String {
        let (dayMinutes, nightMinutes) = calculateDayNightSleepMinutes(entries: entries)

        let dayPercentage = (dayMinutes / (dayMinutes + nightMinutes)) * 100
        let nightPercentage = (nightMinutes / (dayMinutes + nightMinutes)) * 100

        return String(format: "%.0f%% day, %.0f%% night", dayPercentage, nightPercentage)
    }

    private func calculateDayNightSleepMinutes(entries: [SleepEntry]) -> (dayMinutes: Double, nightMinutes: Double) {
        var dayMinutes = 0.0
        var nightMinutes = 0.0

        let calendar = Calendar.current

        for entry in entries {
            guard let duration = entry.calculatedDuration else { continue }

            let hour = calendar.component(.hour, from: entry.timestamp)

            // Consider 7 AM to 7 PM as day, and 7 PM to 7 AM as night
            if hour >= 7 && hour < 19 {
                dayMinutes += Double(duration)
            } else {
                nightMinutes += Double(duration)
            }
        }

        return (dayMinutes, nightMinutes)
    }

    private func calculateLongestSleepStretch(entries: [SleepEntry]) -> Double {
        var longestDuration = 0.0

        for entry in entries {
            if let duration = entry.calculatedDuration {
                let durationHours = Double(duration) / 60.0
                longestDuration = max(longestDuration, durationHours)
            }
        }

        return longestDuration
    }

    private func findMostCommonSleepLocation(entries: [SleepEntry]) -> String {
        var locationCounts: [SleepEntry.SleepLocation?: Int] = [:]

        for entry in entries {
            if let location = entry.location {
                locationCounts[location, default: 0] += 1
            }
        }

        let mostCommonLocation = locationCounts.max(by: { $0.value < $1.value })?.key
        return mostCommonLocation?.description ?? "Unknown"
    }

    private func calculateBabyAge(baby: Baby) -> (months: Int, weeks: Int) {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month, .day], from: baby.birthDate, to: Date())

        let months = ageComponents.month ?? 0
        let days = ageComponents.day ?? 0
        let weeks = (months * 30 + days) / 7 // Approximate weeks

        return (months, weeks)
    }
}
