//
//  EventPredictionEngine.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Engine for predicting events based on patterns and historical data
class EventPredictionEngine {
    // MARK: - Properties

    private let modelContext: ModelContext
    private let predictionStore: PredictionStore
    private let feedbackService: PredictionFeedbackService

    // MARK: - Initialization

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.predictionStore = PredictionStore(modelContext: modelContext)
        self.feedbackService = PredictionFeedbackService(modelContext: modelContext)
    }

    // MARK: - Public Methods

    /// Generate predictions for a baby
    /// - Parameter baby: The baby to generate predictions for
    /// - Returns: Array of event predictions
    func generatePredictions(for baby: Baby) -> [EventPrediction] {
        var predictions: [EventPrediction] = []

        // Generate sleep predictions
        let sleepPredictions = predictSleepEvents(for: baby)
        predictions.append(contentsOf: sleepPredictions)

        // Generate feeding predictions
        let feedingPredictions = predictFeedingEvents(for: baby)
        predictions.append(contentsOf: feedingPredictions)

        // Generate diaper predictions
        let diaperPredictions = predictDiaperEvents(for: baby)
        predictions.append(contentsOf: diaperPredictions)

        // Generate developmental predictions
        let developmentalPredictions = predictDevelopmentalEvents(for: baby)
        predictions.append(contentsOf: developmentalPredictions)

        // Save predictions to the store
        for prediction in predictions {
            predictionStore.savePrediction(prediction.toPrediction(), for: baby)
        }

        return predictions
    }

    /// Check if a predicted event has occurred
    /// - Parameters:
    ///   - predictionId: The ID of the prediction
    ///   - entry: The entry to check against
    /// - Returns: Whether the prediction was correct
    func checkPrediction(predictionId: UUID, against entry: EntryProtocol) -> Bool {
        guard let prediction = predictionStore.getPrediction(id: predictionId) else {
            return false
        }

        // Check if the entry timestamp falls within the prediction timeframe
        let isWithinTimeframe = prediction.timeframe.contains(entry.timestamp)

        // Check if the entry type matches the prediction type
        let matchesType = entryMatchesPredictionType(entry, prediction: prediction)

        let wasCorrect = isWithinTimeframe && matchesType

        // Update prediction correctness
        predictionStore.updatePredictionCorrectness(id: predictionId, wasCorrect: wasCorrect)

        // Record feedback
        let feedback = PredictionFeedback(
            predictionId: predictionId,
            predictionType: prediction.type,
            predictionTimestamp: prediction.timeframe.start,
            predictedValue: prediction.predictedValue,
            actualValue: getEntryValue(entry),
            wasCorrect: wasCorrect
        )

        feedbackService.recordFeedback(feedback)

        return wasCorrect
    }

    /// Get the accuracy rate for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Accuracy rate (0.0-1.0)
    func getAccuracyRate(for predictionType: String) -> Double {
        return feedbackService.calculateAccuracyRate(for: predictionType)
    }

    // MARK: - Private Methods

    /// Predict sleep events for a baby
    /// - Parameter baby: The baby to predict for
    /// - Returns: Array of sleep event predictions
    private func predictSleepEvents(for baby: Baby) -> [EventPrediction] {
        var predictions: [EventPrediction] = []

        // Get recent sleep entries
        let sleepEntries = fetchRecentEntries(for: baby, category: .sleep) as? [SleepEntry] ?? []

        guard sleepEntries.count >= 5 else {
            return []
        }

        // Predict next sleep time
        if let nextSleepPrediction = predictNextSleep(entries: sleepEntries) {
            predictions.append(nextSleepPrediction)
        }

        return predictions
    }

    /// Predict feeding events for a baby
    /// - Parameter baby: The baby to predict for
    /// - Returns: Array of feeding event predictions
    private func predictFeedingEvents(for baby: Baby) -> [EventPrediction] {
        var predictions: [EventPrediction] = []

        // Get recent feeding entries
        let feedingEntries = fetchRecentEntries(for: baby, category: .feeding) as? [FeedingEntry] ?? []

        guard feedingEntries.count >= 5 else {
            return []
        }

        // Predict next feeding time
        if let nextFeedingPrediction = predictNextFeeding(entries: feedingEntries) {
            predictions.append(nextFeedingPrediction)
        }

        return predictions
    }

    /// Predict diaper events for a baby
    /// - Parameter baby: The baby to predict for
    /// - Returns: Array of diaper event predictions
    private func predictDiaperEvents(for baby: Baby) -> [EventPrediction] {
        var predictions: [EventPrediction] = []

        // Get recent diaper entries
        let diaperEntries = fetchRecentEntries(for: baby, category: .diaper) as? [DiaperEntry] ?? []

        guard diaperEntries.count >= 5 else {
            return []
        }

        // Predict next diaper change
        if let nextDiaperPrediction = predictNextDiaper(entries: diaperEntries) {
            predictions.append(nextDiaperPrediction)
        }

        return predictions
    }

    /// Predict developmental events for a baby
    /// - Parameter baby: The baby to predict for
    /// - Returns: Array of developmental event predictions
    private func predictDevelopmentalEvents(for baby: Baby) -> [EventPrediction] {
        var predictions: [EventPrediction] = []

        // Get developmental service
        let developmentalService = DevelopmentalDataService.shared
        developmentalService.setModelContext(modelContext)

        // Get baby's age in weeks
        let ageInWeeks = calculateAgeInWeeks(baby: baby)

        // Get upcoming milestones
        let upcomingMilestones = developmentalService.getUpcomingMilestones(for: ageInWeeks, lookAheadWeeks: 8)

        // Get potential phases
        let potentialPhases = developmentalService.getPotentialPhases(for: ageInWeeks)

        // Predict milestone events
        for milestone in upcomingMilestones {
            if let milestonePrediction = predictMilestone(milestone: milestone, baby: baby) {
                predictions.append(milestonePrediction)
            }
        }

        // Predict phase events
        for phase in potentialPhases {
            if let phasePrediction = predictPhase(phase: phase, baby: baby) {
                predictions.append(phasePrediction)
            }
        }

        return predictions
    }

    /// Fetch recent entries for a baby
    /// - Parameters:
    ///   - baby: The baby to fetch entries for
    ///   - category: The category of entries to fetch
    /// - Returns: Array of entries
    private func fetchRecentEntries(for baby: Baby, category: Insight.InsightCategory) -> [EntryProtocol] {
        switch category {
        case .sleep:
            let babyId = baby.id
            var descriptor = FetchDescriptor<SleepEntry>(
                predicate: #Predicate {
                    $0.baby != nil && $0.baby!.id == babyId
                },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = 20

            do {
                return try modelContext.fetch(descriptor)
            } catch {
                print("Error fetching sleep entries: \(error)")
                return []
            }

        case .feeding:
            let babyId = baby.id
            var descriptor = FetchDescriptor<FeedingEntry>(
                predicate: #Predicate {
                    $0.baby != nil && $0.baby!.id == babyId
                },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = 20

            do {
                return try modelContext.fetch(descriptor)
            } catch {
                print("Error fetching feeding entries: \(error)")
                return []
            }

        case .diaper:
            let babyId = baby.id
            var descriptor = FetchDescriptor<DiaperEntry>(
                predicate: #Predicate {
                    $0.baby != nil && $0.baby!.id == babyId
                },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = 20

            do {
                return try modelContext.fetch(descriptor)
            } catch {
                print("Error fetching diaper entries: \(error)")
                return []
            }

        default:
            return []
        }
    }

    /// Predict the next sleep time
    /// - Parameter entries: Recent sleep entries
    /// - Returns: Event prediction for next sleep, or nil if not enough data
    private func predictNextSleep(entries: [SleepEntry]) -> EventPrediction? {
        guard entries.count >= 5 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Calculate average time between sleeps
        var timeIntervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            let interval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)

            // Only consider intervals less than 12 hours (to avoid overnight gaps)
            if interval < 12 * 3600 {
                timeIntervals.append(interval)
            }
        }

        // If we don't have enough intervals, return nil
        guard !timeIntervals.isEmpty else { return nil }

        let averageInterval = timeIntervals.reduce(0, +) / Double(timeIntervals.count)

        // Get the most recent sleep entry
        guard let lastSleep = sortedEntries.last else { return nil }

        // Calculate the predicted next sleep time
        let predictedTime = lastSleep.timestamp.addingTimeInterval(averageInterval)

        // Only predict if it's in the future
        guard predictedTime > Date() else { return nil }

        // Create a time window for the prediction (±30 minutes)
        let startTime = predictedTime.addingTimeInterval(-30 * 60)
        let endTime = predictedTime.addingTimeInterval(30 * 60)

        // Calculate confidence based on consistency of intervals
        let stdDev = calculateStandardDeviation(timeIntervals)
        let coefficientOfVariation = stdDev / averageInterval

        // Higher consistency = higher probability
        let probability = min(0.9, max(0.5, 1.0 - coefficientOfVariation))

        // Format time for description
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short

        return EventPrediction(
            category: .sleep,
            eventType: "next_sleep",
            timeframe: DateInterval(start: startTime, end: endTime),
            probability: probability,
            evidence: ["Based on \(timeIntervals.count) recent sleep intervals"],
            predictionMethod: "Pattern Analysis",
            description: "Next sleep session likely around \(formatter.string(from: predictedTime))"
        )
    }

    /// Predict the next feeding time
    /// - Parameter entries: Recent feeding entries
    /// - Returns: Event prediction for next feeding, or nil if not enough data
    private func predictNextFeeding(entries: [FeedingEntry]) -> EventPrediction? {
        guard entries.count >= 5 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Calculate average time between feedings
        var timeIntervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            let interval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)
            timeIntervals.append(interval)
        }

        let averageInterval = timeIntervals.reduce(0, +) / Double(timeIntervals.count)

        // Get the most recent feeding entry
        guard let lastFeeding = sortedEntries.last else { return nil }

        // Calculate the predicted next feeding time
        let predictedTime = lastFeeding.timestamp.addingTimeInterval(averageInterval)

        // Only predict if it's in the future
        guard predictedTime > Date() else { return nil }

        // Create a time window for the prediction (±20 minutes)
        let startTime = predictedTime.addingTimeInterval(-20 * 60)
        let endTime = predictedTime.addingTimeInterval(20 * 60)

        // Calculate confidence based on consistency of intervals
        let stdDev = calculateStandardDeviation(timeIntervals)
        let coefficientOfVariation = stdDev / averageInterval

        // Higher consistency = higher probability
        let probability = min(0.9, max(0.5, 1.0 - coefficientOfVariation))

        // Format time for description
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short

        return EventPrediction(
            category: .feeding,
            eventType: "next_feeding",
            timeframe: DateInterval(start: startTime, end: endTime),
            probability: probability,
            evidence: ["Based on \(timeIntervals.count) recent feeding intervals"],
            predictionMethod: "Pattern Analysis",
            description: "Next feeding likely around \(formatter.string(from: predictedTime))"
        )
    }

    /// Predict the next diaper change
    /// - Parameter entries: Recent diaper entries
    /// - Returns: Event prediction for next diaper change, or nil if not enough data
    private func predictNextDiaper(entries: [DiaperEntry]) -> EventPrediction? {
        guard entries.count >= 5 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Calculate average time between diaper changes
        var timeIntervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            let interval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)
            timeIntervals.append(interval)
        }

        let averageInterval = timeIntervals.reduce(0, +) / Double(timeIntervals.count)

        // Get the most recent diaper entry
        guard let lastDiaper = sortedEntries.last else { return nil }

        // Calculate the predicted next diaper change time
        let predictedTime = lastDiaper.timestamp.addingTimeInterval(averageInterval)

        // Only predict if it's in the future
        guard predictedTime > Date() else { return nil }

        // Create a time window for the prediction (±30 minutes)
        let startTime = predictedTime.addingTimeInterval(-30 * 60)
        let endTime = predictedTime.addingTimeInterval(30 * 60)

        // Calculate confidence based on consistency of intervals
        let stdDev = calculateStandardDeviation(timeIntervals)
        let coefficientOfVariation = stdDev / averageInterval

        // Higher consistency = higher probability
        let probability = min(0.9, max(0.5, 1.0 - coefficientOfVariation))

        // Format time for description
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short

        return EventPrediction(
            category: .diaper,
            eventType: "next_diaper",
            timeframe: DateInterval(start: startTime, end: endTime),
            probability: probability,
            evidence: ["Based on \(timeIntervals.count) recent diaper change intervals"],
            predictionMethod: "Pattern Analysis",
            description: "Next diaper change likely around \(formatter.string(from: predictedTime))"
        )
    }

    /// Predict a developmental milestone
    /// - Parameters:
    ///   - milestone: The milestone to predict
    ///   - baby: The baby to predict for
    /// - Returns: Event prediction for the milestone, or nil if not applicable
    private func predictMilestone(milestone: DevelopmentalMilestone, baby: Baby) -> EventPrediction? {
        let ageInWeeks = calculateAgeInWeeks(baby: baby)

        // Check if the milestone is upcoming
        guard milestone.isUpcoming(for: ageInWeeks) else { return nil }

        // Get estimated time until milestone
        guard let weeksUntil = milestone.estimatedTimeUntil(ageInWeeks: ageInWeeks) else { return nil }

        // Calculate timeframe
        let now = Date()
        let startTime = now.addingTimeInterval(Double(weeksUntil - 1) * 7 * 24 * 3600) // 1 week before estimated time
        let endTime = now.addingTimeInterval(Double(weeksUntil + 1) * 7 * 24 * 3600)   // 1 week after estimated time

        // Calculate probability based on proximity
        let proximity = 1.0 - Double(weeksUntil) / 8.0 // Higher proximity = higher probability
        let probability = min(0.8, max(0.5, proximity))

        // Create evidence list
        var evidence: [String] = [
            "Typical age range: \(milestone.typicalAgeWeeks.lowerBound)-\(milestone.typicalAgeWeeks.upperBound) weeks",
            "Current age: \(ageInWeeks) weeks"
        ]

        // Add common patterns as evidence
        for (_, description) in milestone.commonPatterns {
            evidence.append(description)
        }

        // Format dates for description
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return EventPrediction(
            category: .development,
            eventType: "milestone_\(milestone.name.lowercased().replacingOccurrences(of: " ", with: "_"))",
            timeframe: DateInterval(start: startTime, end: endTime),
            probability: probability,
            evidence: evidence,
            predictionMethod: "Developmental Analysis",
            description: "Milestone '\(milestone.name)' likely between \(formatter.string(from: startTime)) and \(formatter.string(from: endTime))"
        )
    }

    /// Predict a developmental phase
    /// - Parameters:
    ///   - phase: The phase to predict
    ///   - baby: The baby to predict for
    /// - Returns: Event prediction for the phase, or nil if not applicable
    private func predictPhase(phase: DevelopmentalPhase, baby: Baby) -> EventPrediction? {
        let ageInWeeks = calculateAgeInWeeks(baby: baby)

        // Check if the phase is potentially active
        guard phase.isPotentiallyActive(for: ageInWeeks) else { return nil }

        // Get the most relevant age range
        guard let relevantRange = phase.mostRelevantAgeRange(for: ageInWeeks) else { return nil }

        // Calculate timeframe
        let now = Date()
        let calendar = Calendar.current

        // If we're already in the range, start from now
        let startTime: Date
        if relevantRange.contains(ageInWeeks) {
            startTime = now
        } else {
            // Calculate weeks until the start of the range
            let weeksUntil = relevantRange.lowerBound - ageInWeeks
            startTime = calendar.date(byAdding: .day, value: weeksUntil * 7, to: now)!
        }

        // Calculate end time based on phase duration
        let endTime = calendar.date(byAdding: .day, value: phase.duration.upperBound, to: startTime)!

        // Calculate probability based on proximity and historical accuracy
        let proximity = 1.0 - Double(max(0, relevantRange.lowerBound - ageInWeeks)) / 4.0
        let probability = min(0.8, max(0.5, proximity))

        // Create evidence list
        var evidence: [String] = [
            "Typical age range: \(relevantRange.lowerBound)-\(relevantRange.upperBound) weeks",
            "Current age: \(ageInWeeks) weeks",
            "Sleep impact: \(phase.sleepImpact)",
            "Feeding impact: \(phase.feedingImpact)"
        ]

        // Add behavioral signs as evidence
        for sign in phase.behavioralSigns {
            evidence.append("Sign: \(sign)")
        }

        // Format dates for description
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return EventPrediction(
            category: .development,
            eventType: "phase_\(phase.name.lowercased().replacingOccurrences(of: " ", with: "_"))",
            timeframe: DateInterval(start: startTime, end: endTime),
            probability: probability,
            evidence: evidence,
            predictionMethod: "Developmental Analysis",
            description: "Developmental phase '\(phase.name)' likely between \(formatter.string(from: startTime)) and \(formatter.string(from: endTime))"
        )
    }

    /// Calculate standard deviation of a set of values
    /// - Parameter values: Array of values
    /// - Returns: Standard deviation
    private func calculateStandardDeviation(_ values: [TimeInterval]) -> Double {
        let count = Double(values.count)
        let mean = values.reduce(0, +) / count
        let variance = values.reduce(0.0) { sum, value in
            let diff = value - mean
            return sum + (diff * diff)
        } / count

        return sqrt(variance)
    }

    /// Calculate a baby's age in weeks
    /// - Parameter baby: The baby to calculate age for
    /// - Returns: Age in weeks
    private func calculateAgeInWeeks(baby: Baby) -> Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.day], from: baby.birthDate, to: Date())

        guard let days = ageComponents.day else { return 0 }
        return days / 7
    }

    /// Check if an entry matches a prediction type
    /// - Parameters:
    ///   - entry: The entry to check
    ///   - prediction: The prediction to check against
    /// - Returns: Whether the entry matches the prediction type
    private func entryMatchesPredictionType(_ entry: EntryProtocol, prediction: Prediction) -> Bool {
        switch entry {
        case is SleepEntry:
            return prediction.type.contains("sleep")
        case is FeedingEntry:
            return prediction.type.contains("feeding")
        case is DiaperEntry:
            return prediction.type.contains("diaper")
        default:
            return false
        }
    }

    /// Get a numeric value from an entry for comparison
    /// - Parameter entry: The entry to get a value from
    /// - Returns: A numeric value, or nil if not applicable
    private func getEntryValue(_ entry: EntryProtocol) -> Double? {
        switch entry {
        case let sleepEntry as SleepEntry:
            return Double(sleepEntry.duration ?? 0)
        case let feedingEntry as FeedingEntry:
            return feedingEntry.volume
        default:
            return nil
        }
    }
}
