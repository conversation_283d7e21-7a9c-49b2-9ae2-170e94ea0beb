import Foundation
import StoreKit

/// Service for managing in-app purchases and subscriptions
class RevenueCatService: ObservableObject {
    /// Shared instance of the service
    static let shared = RevenueCatService()

    /// Current subscription status
    @Published var subscriptionStatus: SubscriptionStatus = .free

    /// Available products
    @Published var products: [Product] = []

    /// Loading state
    @Published var isLoading = false

    /// Error message
    @Published var errorMessage: String? = nil

    /// Subscription status
    enum SubscriptionStatus: String, Codable {
        case free
        case trial
        case premium
        case expired
    }

    /// Private initializer for singleton
    private init() {
        // Load products
        Task {
            await loadProducts()
        }

        // Check subscription status
        Task {
            await checkSubscriptionStatus()
        }
    }

    /// Load available products
    @MainActor
    func loadProducts() async {
        isLoading = true
        errorMessage = nil

        do {
            // Use StoreKit 2 to load products
            let storeProducts = try await Product.products(for: Set(RevenueCatConfig.ProductIDs.all))
            self.products = storeProducts
            isLoading = false
        } catch {
            errorMessage = "Failed to load products: \(error.localizedDescription)"
            isLoading = false
            print("Error loading products: \(error)")
        }
    }

    /// Check subscription status
    @MainActor
    func checkSubscriptionStatus() async {
        isLoading = true
        errorMessage = nil

        // TODO: Implement with RevenueCat SDK
        // For now, default to free
        subscriptionStatus = .free
        isLoading = false
    }

    /// Purchase a product
    @MainActor
    func purchase(productId: String) async -> Bool {
        isLoading = true
        errorMessage = nil

        guard let product = products.first(where: { $0.id == productId }) else {
            errorMessage = "Product not found"
            isLoading = false
            return false
        }

        do {
            // Use StoreKit 2 to purchase product
            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                // Handle successful purchase
                let transaction = try checkVerified(verification)

                // Update subscription status
                if productId == RevenueCatConfig.ProductIDs.monthly || productId == RevenueCatConfig.ProductIDs.annual {
                    subscriptionStatus = .premium
                } else if productId == RevenueCatConfig.ProductIDs.lifetime {
                    subscriptionStatus = .premium
                }

                // Finish the transaction
                await transaction.finish()

                isLoading = false
                return true

            case .userCancelled:
                errorMessage = "Purchase cancelled"
                isLoading = false
                return false

            case .pending:
                errorMessage = "Purchase pending"
                isLoading = false
                return false

            default:
                errorMessage = "Purchase failed"
                isLoading = false
                return false
            }
        } catch {
            errorMessage = "Purchase failed: \(error.localizedDescription)"
            isLoading = false
            print("Error purchasing product: \(error)")
            return false
        }
    }

    /// Restore purchases
    @MainActor
    func restorePurchases() async -> Bool {
        isLoading = true
        errorMessage = nil

        do {
            // Use StoreKit 2 to restore purchases
            for await result in Transaction.currentEntitlements {
                if let transaction = try? checkVerified(result) {
                    // Check if this transaction gives premium access
                    if RevenueCatConfig.ProductIDs.all.contains(transaction.productID) {
                        subscriptionStatus = .premium
                    }
                }
            }

            isLoading = false
            return true
        } catch {
            errorMessage = "Restore failed: \(error.localizedDescription)"
            isLoading = false
            print("Error restoring purchases: \(error)")
            return false
        }
    }

    /// Check if user has premium access
    func hasPremiumAccess() -> Bool {
        return subscriptionStatus == .premium || subscriptionStatus == .trial
    }

    /// Helper function to verify a transaction
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    /// Store errors
    enum StoreError: Error {
        case failedVerification
    }
}
