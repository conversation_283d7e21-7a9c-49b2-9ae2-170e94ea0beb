//
//  DevelopmentalDataService.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Service for accessing developmental data
class DevelopmentalDataService {
    // Singleton instance
    static let shared = DevelopmentalDataService()

    // MARK: - Properties

    /// Developmental norms database
    private var developmentalNorms: [DevelopmentalNorm] = []

    /// Developmental milestones database
    private var developmentalMilestones: [DevelopmentalMilestone] = []

    /// Developmental phases database
    private var developmentalPhases: [DevelopmentalPhase] = []

    /// Model context for accessing baby data
    private var modelContext: ModelContext?

    // MARK: - Initialization

    private init() {
        loadDevelopmentalData()
    }

    /// Set the model context
    /// - Parameter modelContext: The SwiftData model context
    func setModelContext(_ modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    // MARK: - Public Methods

    /// Get developmental norms for a specific category and age
    /// - Parameters:
    ///   - category: The category to get norms for
    ///   - ageInWeeks: The baby's age in weeks
    /// - Returns: Array of applicable developmental norms
    func getNorms(for category: Insight.InsightCategory, ageInWeeks: Int) -> [DevelopmentalNorm] {
        return developmentalNorms.filter {
            $0.category == category && $0.isApplicable(for: ageInWeeks)
        }
    }

    /// Get upcoming milestones for a baby
    /// - Parameters:
    ///   - ageInWeeks: The baby's age in weeks
    ///   - lookAheadWeeks: How many weeks ahead to consider
    /// - Returns: Array of upcoming milestones
    func getUpcomingMilestones(for ageInWeeks: Int, lookAheadWeeks: Int = 4) -> [DevelopmentalMilestone] {
        return developmentalMilestones.filter {
            $0.isUpcoming(for: ageInWeeks, lookAheadWeeks: lookAheadWeeks)
        }
    }

    /// Get current milestones for a baby
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Array of current milestones
    func getCurrentMilestones(for ageInWeeks: Int) -> [DevelopmentalMilestone] {
        return developmentalMilestones.filter {
            $0.isCurrent(for: ageInWeeks)
        }
    }

    /// Get potential developmental phases for a baby
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Array of potential phases
    func getPotentialPhases(for ageInWeeks: Int) -> [DevelopmentalPhase] {
        return developmentalPhases.filter {
            $0.isPotentiallyActive(for: ageInWeeks)
        }
    }

    /// Create developmental context for a baby
    /// - Parameter baby: The baby to create context for
    /// - Returns: Developmental context
    func createDevelopmentalContext(for baby: Baby) -> DevelopmentalContext {
        let ageInWeeks = calculateAgeInWeeks(baby: baby)

        // Get current stage description
        let currentStage = getCurrentStageDescription(for: ageInWeeks)

        // Get relevant norms
        let relevantNorms = developmentalNorms.filter { $0.isApplicable(for: ageInWeeks) }

        // Get upcoming milestones
        let upcomingMilestones = getUpcomingMilestones(for: ageInWeeks)

        // Get potential phases
        let potentialPhases = getPotentialPhases(for: ageInWeeks)

        return DevelopmentalContext(
            currentStage: currentStage,
            relevantNorm: relevantNorms.first,
            upcomingMilestone: upcomingMilestones.first,
            potentialPhases: potentialPhases
        )
    }

    /// Detect milestone proximity based on recent data
    /// - Parameters:
    ///   - baby: The baby to analyze
    ///   - category: The category to focus on
    /// - Returns: Array of milestones with proximity information
    func detectMilestoneProximity(for baby: Baby, category: Insight.InsightCategory) -> [(milestone: DevelopmentalMilestone, proximity: Double, indicators: [String])] {
        guard let modelContext = modelContext else { return [] }

        let ageInWeeks = calculateAgeInWeeks(baby: baby)

        // Get upcoming and current milestones
        let upcomingMilestones = getUpcomingMilestones(for: ageInWeeks, lookAheadWeeks: 8)
        let currentMilestones = getCurrentMilestones(for: ageInWeeks)

        let relevantMilestones = upcomingMilestones + currentMilestones

        // Filter milestones related to the specified category
        let categoryMilestones = relevantMilestones.filter { milestone in
            milestone.relatedCategories.contains(category)
        }

        var results: [(milestone: DevelopmentalMilestone, proximity: Double, indicators: [String])] = []

        for milestone in categoryMilestones {
            // Analyze recent data for indicators of this milestone
            let (proximity, indicators) = analyzeDataForMilestoneIndicators(
                baby: baby,
                milestone: milestone,
                category: category
            )

            // Only include if we have indicators or high proximity
            if !indicators.isEmpty || proximity > 0.3 {
                results.append((milestone: milestone, proximity: proximity, indicators: indicators))
            }
        }

        // Sort by proximity (highest first)
        return results.sorted { $0.proximity > $1.proximity }
    }

    /// Analyze recent data for indicators of a milestone
    /// - Parameters:
    ///   - baby: The baby to analyze
    ///   - milestone: The milestone to check for
    ///   - category: The category to focus on
    /// - Returns: Tuple of (proximity, indicators)
    private func analyzeDataForMilestoneIndicators(
        baby: Baby,
        milestone: DevelopmentalMilestone,
        category: Insight.InsightCategory
    ) -> (proximity: Double, indicators: [String]) {
        guard let modelContext = modelContext else { return (0, []) }

        var indicators: [String] = []
        var proximityScore = 0.0

        // Base proximity on age
        let ageInWeeks = calculateAgeInWeeks(baby: baby)
        let ageProximity = calculateAgeProximity(ageInWeeks: ageInWeeks, milestone: milestone)
        proximityScore += ageProximity * 0.7 // Age is a strong factor

        // Check for pattern indicators based on category
        switch category {
        case .sleep:
            if let sleepIndicators = checkSleepPatternsForMilestone(baby: baby, milestone: milestone) {
                indicators.append(contentsOf: sleepIndicators)
                proximityScore += 0.1 * Double(sleepIndicators.count)
            }

        case .feeding:
            if let feedingIndicators = checkFeedingPatternsForMilestone(baby: baby, milestone: milestone) {
                indicators.append(contentsOf: feedingIndicators)
                proximityScore += 0.1 * Double(feedingIndicators.count)
            }

        case .diaper:
            if let diaperIndicators = checkDiaperPatternsForMilestone(baby: baby, milestone: milestone) {
                indicators.append(contentsOf: diaperIndicators)
                proximityScore += 0.1 * Double(diaperIndicators.count)
            }

        case .development:
            // Development milestones often show signs across multiple categories
            if let sleepIndicators = checkSleepPatternsForMilestone(baby: baby, milestone: milestone) {
                indicators.append(contentsOf: sleepIndicators)
                proximityScore += 0.05 * Double(sleepIndicators.count)
            }

            if let feedingIndicators = checkFeedingPatternsForMilestone(baby: baby, milestone: milestone) {
                indicators.append(contentsOf: feedingIndicators)
                proximityScore += 0.05 * Double(feedingIndicators.count)
            }

        default:
            break
        }

        // Cap proximity at 1.0
        proximityScore = min(1.0, proximityScore)

        return (proximityScore, indicators)
    }

    /// Calculate age-based proximity to a milestone
    /// - Parameters:
    ///   - ageInWeeks: The baby's age in weeks
    ///   - milestone: The milestone to check
    /// - Returns: Proximity score (0-1)
    private func calculateAgeProximity(ageInWeeks: Int, milestone: DevelopmentalMilestone) -> Double {
        // If the baby is already in the milestone range
        if milestone.typicalAgeWeeks.contains(ageInWeeks) {
            // Calculate how far into the range they are
            let rangeSize = milestone.typicalAgeWeeks.upperBound - milestone.typicalAgeWeeks.lowerBound
            let position = ageInWeeks - milestone.typicalAgeWeeks.lowerBound

            // Return a value between 0.5 and 1.0 based on position in range
            return 0.5 + (Double(position) / Double(max(1, rangeSize)) * 0.5)
        }

        // If the milestone is upcoming
        if ageInWeeks < milestone.typicalAgeWeeks.lowerBound {
            let weeksUntil = milestone.typicalAgeWeeks.lowerBound - ageInWeeks

            // Proximity decreases as weeks until increases
            // Within 4 weeks: 0.3-0.5, within 8 weeks: 0.1-0.3
            if weeksUntil <= 4 {
                return 0.5 - (Double(weeksUntil) / 4.0 * 0.2)
            } else if weeksUntil <= 8 {
                return 0.3 - (Double(weeksUntil - 4) / 4.0 * 0.2)
            } else {
                return 0.0
            }
        }

        // If the milestone has passed
        let weeksSince = ageInWeeks - milestone.typicalAgeWeeks.upperBound
        if weeksSince <= 4 {
            return 0.5 - (Double(weeksSince) / 4.0 * 0.3)
        } else {
            return 0.0
        }
    }

    /// Check sleep patterns for milestone indicators
    /// - Parameters:
    ///   - baby: The baby to analyze
    ///   - milestone: The milestone to check for
    /// - Returns: Array of indicator descriptions, or nil if none found
    private func checkSleepPatternsForMilestone(baby: Baby, milestone: DevelopmentalMilestone) -> [String]? {
        guard let modelContext = modelContext else { return nil }

        // Get recent sleep entries
        let sleepAnalyzer = SleepAnalyzer()
        let recentEntries = sleepAnalyzer.fetchEntries(
            for: baby,
            inTimeRange: .last7Days,
            modelContext: modelContext
        )

        guard !recentEntries.isEmpty else { return nil }

        var indicators: [String] = []

        // Check for common patterns associated with this milestone
        if let sleepDisruptionPattern = milestone.commonPatterns["sleep_disruption"] {
            // Check for sleep disruption
            let recentSleepDuration = recentEntries.reduce(0.0) { total, entry in
                if let duration = entry.duration {
                    return total + Double(duration)
                }
                return total
            }
            let averageDailyDuration = recentSleepDuration / min(7, Double(Set(recentEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count))

            // If average daily sleep is less than expected for age
            if averageDailyDuration < 12 * 60 { // Less than 12 hours
                indicators.append(sleepDisruptionPattern)
            }
        }

        if let increasedWakingsPattern = milestone.commonPatterns["increased_wakings"] {
            // Check for increased night wakings
            let nightWakings = recentEntries.filter { entry in
                let hour = Calendar.current.component(.hour, from: entry.timestamp)
                return hour >= 22 || hour <= 6 // Night hours
            }

            if nightWakings.count >= 3 {
                indicators.append(increasedWakingsPattern)
            }
        }

        return indicators.isEmpty ? nil : indicators
    }

    /// Check feeding patterns for milestone indicators
    /// - Parameters:
    ///   - baby: The baby to analyze
    ///   - milestone: The milestone to check for
    /// - Returns: Array of indicator descriptions, or nil if none found
    private func checkFeedingPatternsForMilestone(baby: Baby, milestone: DevelopmentalMilestone) -> [String]? {
        guard let modelContext = modelContext else { return nil }

        // Get recent feeding entries
        let feedingAnalyzer = FeedingAnalyzer()
        let recentEntries = feedingAnalyzer.fetchEntries(
            for: baby,
            inTimeRange: .last7Days,
            modelContext: modelContext
        )

        guard !recentEntries.isEmpty else { return nil }

        var indicators: [String] = []

        // Check for common patterns associated with this milestone
        if let increasedHungerPattern = milestone.commonPatterns["increased_hunger"] {
            // Check for increased feeding frequency
            let daysInPeriod = min(7, Set(recentEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
            let averageDailyFeedings = Double(recentEntries.count) / Double(max(1, daysInPeriod))

            // If average daily feedings is high
            if averageDailyFeedings > 8 { // More than 8 feedings per day
                indicators.append(increasedHungerPattern)
            }
        }

        if let distractibilityPattern = milestone.commonPatterns["feeding_distractibility"] {
            // Check for shorter feeding sessions
            let averageDuration = recentEntries.compactMap { $0.duration }.reduce(0, +) / max(1, recentEntries.count)

            // If average duration is short
            if averageDuration < 10 { // Less than 10 minutes
                indicators.append(distractibilityPattern)
            }
        }

        return indicators.isEmpty ? nil : indicators
    }

    /// Check diaper patterns for milestone indicators
    /// - Parameters:
    ///   - baby: The baby to analyze
    ///   - milestone: The milestone to check for
    /// - Returns: Array of indicator descriptions, or nil if none found
    private func checkDiaperPatternsForMilestone(baby: Baby, milestone: DevelopmentalMilestone) -> [String]? {
        guard let modelContext = modelContext else { return nil }

        // Get recent diaper entries
        let diaperAnalyzer = DiaperAnalyzer()
        let recentEntries = diaperAnalyzer.fetchEntries(
            for: baby,
            inTimeRange: .last7Days,
            modelContext: modelContext
        )

        guard !recentEntries.isEmpty else { return nil }

        var indicators: [String] = []

        // Check for common patterns associated with this milestone
        if let diaperChangesPattern = milestone.commonPatterns["diaper_changes"] {
            // Check for changes in diaper frequency
            let daysInPeriod = min(7, Set(recentEntries.map { Calendar.current.dateComponents([.day], from: $0.timestamp).day ?? 0 }).count)
            let averageDailyChanges = Double(recentEntries.count) / Double(max(1, daysInPeriod))

            // If average daily changes is unusual
            if averageDailyChanges < 4 || averageDailyChanges > 10 {
                indicators.append(diaperChangesPattern)
            }
        }

        return indicators.isEmpty ? nil : indicators
    }

    // MARK: - Private Methods

    /// Load developmental data from resources
    private func loadDevelopmentalData() {
        // In a real implementation, this would load data from JSON files or a database
        // For now, we'll use hardcoded sample data

        // Sleep norms
        developmentalNorms.append(
            DevelopmentalNorm(
                category: .sleep,
                metric: "total_sleep_hours",
                ageRangeWeeks: 0...4,
                normalRange: 14...18,
                unit: "hours",
                description: "Newborns typically sleep 14-18 hours per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .sleep,
                metric: "total_sleep_hours",
                ageRangeWeeks: 5...12,
                normalRange: 12...16,
                unit: "hours",
                description: "1-3 month olds typically sleep 12-16 hours per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .sleep,
                metric: "total_sleep_hours",
                ageRangeWeeks: 13...26,
                normalRange: 12...14,
                unit: "hours",
                description: "3-6 month olds typically sleep 12-14 hours per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .sleep,
                metric: "night_sleep_hours",
                ageRangeWeeks: 13...26,
                normalRange: 6...8,
                unit: "hours",
                description: "3-6 month olds typically sleep 6-8 hours at night"
            )
        )

        // Feeding norms
        developmentalNorms.append(
            DevelopmentalNorm(
                category: .feeding,
                metric: "feeding_frequency",
                ageRangeWeeks: 0...4,
                normalRange: 8...12,
                unit: "feedings per day",
                description: "Newborns typically feed 8-12 times per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .feeding,
                metric: "feeding_frequency",
                ageRangeWeeks: 5...12,
                normalRange: 6...8,
                unit: "feedings per day",
                description: "1-3 month olds typically feed 6-8 times per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .feeding,
                metric: "formula_volume",
                ageRangeWeeks: 0...4,
                normalRange: 60...90,
                unit: "ml per feeding",
                description: "Newborns typically consume 60-90 ml per feeding"
            )
        )

        // Diaper norms
        developmentalNorms.append(
            DevelopmentalNorm(
                category: .diaper,
                metric: "diaper_frequency",
                ageRangeWeeks: 0...4,
                normalRange: 8...12,
                unit: "diapers per day",
                description: "Newborns typically have 8-12 diaper changes per day"
            )
        )

        developmentalNorms.append(
            DevelopmentalNorm(
                category: .diaper,
                metric: "diaper_frequency",
                ageRangeWeeks: 5...12,
                normalRange: 6...8,
                unit: "diapers per day",
                description: "1-3 month olds typically have 6-8 diaper changes per day"
            )
        )

        // Growth norms
        developmentalNorms.append(
            DevelopmentalNorm(
                category: .growth,
                metric: "weight_gain",
                ageRangeWeeks: 0...12,
                normalRange: 0.15...0.3,
                unit: "kg per week",
                description: "Babies 0-3 months typically gain 150-300g per week"
            )
        )

        // Milestones
        developmentalMilestones.append(
            DevelopmentalMilestone(
                name: "First Social Smile",
                typicalAgeWeeks: 6...8,
                relatedCategories: [.development],
                commonPatterns: [
                    "sleep_disruption": "Temporary sleep disruption as baby processes new social skills",
                    "increased_alertness": "Increased periods of alertness and engagement"
                ]
            )
        )

        developmentalMilestones.append(
            DevelopmentalMilestone(
                name: "Head Control",
                typicalAgeWeeks: 8...12,
                relatedCategories: [.development, .sleep],
                commonPatterns: [
                    "sleep_disruption": "Temporary sleep disruption as baby practices new motor skills",
                    "increased_hunger": "Increased appetite due to energy used for motor development"
                ]
            )
        )

        developmentalMilestones.append(
            DevelopmentalMilestone(
                name: "Rolling Over",
                typicalAgeWeeks: 16...20,
                relatedCategories: [.development, .sleep],
                commonPatterns: [
                    "sleep_disruption": "Waking up after rolling over during sleep",
                    "increased_wakings": "More frequent night wakings due to practicing new skill",
                    "increased_hunger": "Increased appetite due to energy used for motor development"
                ]
            )
        )

        developmentalMilestones.append(
            DevelopmentalMilestone(
                name: "Sitting Unassisted",
                typicalAgeWeeks: 24...28,
                relatedCategories: [.development, .feeding],
                commonPatterns: [
                    "feeding_distractibility": "More distracted during feedings due to interest in surroundings",
                    "increased_hunger": "Increased appetite due to energy used for motor development"
                ]
            )
        )

        // Developmental phases
        developmentalPhases.append(
            DevelopmentalPhase(
                name: "4-Month Sleep Regression",
                typicalAgeWeeks: [16...20],
                sleepImpact: "More frequent night wakings, shorter naps, and difficulty falling asleep",
                feedingImpact: "May show increased hunger due to more awake time",
                behavioralSigns: ["More fussy than usual", "Fighting sleep", "Increased clinginess"],
                duration: 2...6
            )
        )

        developmentalPhases.append(
            DevelopmentalPhase(
                name: "8-Month Sleep Regression",
                typicalAgeWeeks: [32...36],
                sleepImpact: "Increased night wakings, early morning wakings, and nap resistance",
                feedingImpact: "May show decreased interest in solids due to teething or separation anxiety",
                behavioralSigns: ["Separation anxiety", "Stranger anxiety", "Clinginess", "Teething symptoms"],
                duration: 3...6
            )
        )

        developmentalPhases.append(
            DevelopmentalPhase(
                name: "Growth Spurt",
                typicalAgeWeeks: [7...9, 13...15, 23...25, 34...36, 42...44],
                sleepImpact: "May sleep more during the day, but wake more at night",
                feedingImpact: "Significant increase in hunger and feeding frequency",
                behavioralSigns: ["Increased hunger", "Cluster feeding", "Fussiness", "Clinginess"],
                duration: 2...4
            )
        )
    }

    /// Get current stage description for a baby
    /// - Parameter ageInWeeks: The baby's age in weeks
    /// - Returns: Description of the current developmental stage
    private func getCurrentStageDescription(for ageInWeeks: Int) -> String {
        if ageInWeeks < 4 {
            return "Newborn stage (0-1 month)"
        } else if ageInWeeks < 13 {
            return "First trimester (1-3 months)"
        } else if ageInWeeks < 26 {
            return "Second trimester (3-6 months)"
        } else if ageInWeeks < 39 {
            return "Third trimester (6-9 months)"
        } else if ageInWeeks < 52 {
            return "Fourth trimester (9-12 months)"
        } else {
            return "Toddler stage (1+ years)"
        }
    }

    /// Calculate a baby's age in weeks
    /// - Parameter baby: The baby to calculate age for
    /// - Returns: Age in weeks
    private func calculateAgeInWeeks(baby: Baby) -> Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.day], from: baby.birthDate, to: Date())

        guard let days = ageComponents.day else { return 0 }
        return days / 7
    }
}
