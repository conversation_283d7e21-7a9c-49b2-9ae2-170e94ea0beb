//
//  PredictionAccuracyTracker.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Service for tracking and improving prediction accuracy
class PredictionAccuracyTracker {
    // MARK: - Properties
    
    private let modelContext: ModelContext
    private let predictionStore: PredictionStore
    private let feedbackService: PredictionFeedbackService
    
    // Cache of accuracy rates by prediction type
    private var accuracyRates: [String: Double] = [:]
    
    // Cache of error metrics by prediction type
    private var errorMetrics: [String: [String: Double]] = [:]
    
    // MARK: - Initialization
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.predictionStore = PredictionStore(modelContext: modelContext)
        self.feedbackService = PredictionFeedbackService(modelContext: modelContext)
        
        // Load initial accuracy rates
        loadAccuracyRates()
    }
    
    // MARK: - Public Methods
    
    /// Get the accuracy rate for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Accuracy rate (0.0-1.0)
    func getAccuracyRate(for predictionType: String) -> Double {
        if let cachedRate = accuracyRates[predictionType] {
            return cachedRate
        }
        
        let rate = feedbackService.calculateAccuracyRate(for: predictionType)
        accuracyRates[predictionType] = rate
        return rate
    }
    
    /// Get error metrics for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Dictionary of error metrics
    func getErrorMetrics(for predictionType: String) -> [String: Double] {
        if let cachedMetrics = errorMetrics[predictionType] {
            return cachedMetrics
        }
        
        let metrics = calculateErrorMetrics(for: predictionType)
        errorMetrics[predictionType] = metrics
        return metrics
    }
    
    /// Record the outcome of a prediction
    /// - Parameters:
    ///   - predictionId: The ID of the prediction
    ///   - wasCorrect: Whether the prediction was correct
    ///   - actualValue: The actual observed value, if applicable
    func recordPredictionOutcome(predictionId: UUID, wasCorrect: Bool, actualValue: Double? = nil) {
        guard let prediction = predictionStore.getPrediction(id: predictionId) else {
            return
        }
        
        // Update prediction correctness
        predictionStore.updatePredictionCorrectness(id: predictionId, wasCorrect: wasCorrect)
        
        // Record feedback
        let feedback = PredictionFeedback(
            predictionId: predictionId,
            predictionType: prediction.type,
            predictionTimestamp: prediction.timeframe.start,
            predictedValue: prediction.predictedValue,
            actualValue: actualValue,
            wasCorrect: wasCorrect
        )
        
        feedbackService.recordFeedback(feedback)
        
        // Update cached accuracy rate
        accuracyRates[prediction.type] = feedbackService.calculateAccuracyRate(for: prediction.type)
        
        // Update cached error metrics
        errorMetrics[prediction.type] = calculateErrorMetrics(for: prediction.type)
    }
    
    /// Get recommended model parameters for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Dictionary of recommended parameters
    func getRecommendedParameters(for predictionType: String) -> [String: Double] {
        // Get all feedback for this prediction type
        let feedback = feedbackService.getAllFeedback(for: predictionType)
        
        // Default parameters
        var parameters: [String: Double] = [:]
        
        if predictionType.contains("forecast") {
            // For forecasting models
            
            // Calculate accuracy by model
            var accuracyByModel: [String: (correct: Int, total: Int)] = [:]
            
            // Get all predictions of this type
            let predictions = getAllPredictions(ofType: predictionType)
            
            for prediction in predictions {
                if let model = prediction.forecastModel {
                    let wasCorrect = prediction.wasCorrect ?? false
                    var stats = accuracyByModel[model] ?? (correct: 0, total: 0)
                    stats.total += 1
                    if wasCorrect {
                        stats.correct += 1
                    }
                    accuracyByModel[model] = stats
                }
            }
            
            // Find the most accurate model
            var bestModel = ""
            var bestAccuracy = 0.0
            
            for (model, stats) in accuracyByModel {
                if stats.total >= 5 {
                    let accuracy = Double(stats.correct) / Double(stats.total)
                    if accuracy > bestAccuracy {
                        bestAccuracy = accuracy
                        bestModel = model
                    }
                }
            }
            
            // Set recommended model
            if !bestModel.isEmpty {
                parameters["recommended_model"] = 1.0 // Flag to indicate we have a recommendation
                parameters["model_name"] = Double(bestModel.hashValue) // Use hash as a numeric identifier
            }
            
            // Recommend parameters based on the prediction type
            if predictionType.contains("sleep") {
                parameters["alpha"] = 0.3 // Default alpha for sleep forecasts
                parameters["beta"] = 0.1  // Default beta for sleep forecasts
            } else if predictionType.contains("feeding") {
                parameters["alpha"] = 0.4 // Default alpha for feeding forecasts
                parameters["beta"] = 0.2  // Default beta for feeding forecasts
            } else if predictionType.contains("diaper") {
                parameters["alpha"] = 0.3 // Default alpha for diaper forecasts
            }
        } else if predictionType.contains("event") {
            // For event predictions
            
            // Calculate average error in time prediction
            var totalTimeError = 0.0
            var timeErrorCount = 0
            
            for feedbackItem in feedback {
                if let predictionTime = getPredictionTime(predictionId: feedbackItem.predictionId),
                   let actualTime = getActualTime(predictionId: feedbackItem.predictionId) {
                    let timeError = abs(actualTime.timeIntervalSince(predictionTime))
                    totalTimeError += timeError
                    timeErrorCount += 1
                }
            }
            
            if timeErrorCount > 0 {
                let averageTimeError = totalTimeError / Double(timeErrorCount)
                
                // Adjust time window based on average error
                let timeWindowMinutes = min(60, max(15, Int(averageTimeError / 60)))
                parameters["time_window_minutes"] = Double(timeWindowMinutes)
            }
        }
        
        return parameters
    }
    
    /// Get all predictions of a specific type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Array of predictions
    func getAllPredictions(ofType predictionType: String) -> [Prediction] {
        let descriptor = FetchDescriptor<PredictionRecord>(
            predicate: #Predicate { $0.type == predictionType }
        )
        
        do {
            let records = try modelContext.fetch(descriptor)
            return records.map { $0.toPrediction() }
        } catch {
            print("Error fetching predictions: \(error)")
            return []
        }
    }
    
    // MARK: - Private Methods
    
    /// Load accuracy rates for all prediction types
    private func loadAccuracyRates() {
        // Get all prediction types
        let predictionTypes = getAllPredictionTypes()
        
        // Calculate accuracy rate for each type
        for type in predictionTypes {
            let rate = feedbackService.calculateAccuracyRate(for: type)
            accuracyRates[type] = rate
            
            let metrics = calculateErrorMetrics(for: type)
            errorMetrics[type] = metrics
        }
    }
    
    /// Get all prediction types
    /// - Returns: Array of prediction types
    private func getAllPredictionTypes() -> [String] {
        let descriptor = FetchDescriptor<PredictionRecord>()
        
        do {
            let records = try modelContext.fetch(descriptor)
            let types = Set(records.map { $0.type })
            return Array(types)
        } catch {
            print("Error fetching prediction types: \(error)")
            return []
        }
    }
    
    /// Calculate error metrics for a specific prediction type
    /// - Parameter predictionType: The type of prediction
    /// - Returns: Dictionary of error metrics
    private func calculateErrorMetrics(for predictionType: String) -> [String: Double] {
        // Get all feedback for this prediction type
        let feedback = feedbackService.getAllFeedback(for: predictionType)
        
        // Filter feedback with numeric values
        let feedbackWithValues = feedback.filter { $0.predictedValue != nil && $0.actualValue != nil }
        
        guard !feedbackWithValues.isEmpty else {
            return [:]
        }
        
        // Calculate Mean Absolute Error (MAE)
        var sumAbsoluteError = 0.0
        for item in feedbackWithValues {
            if let predictedValue = item.predictedValue, let actualValue = item.actualValue {
                sumAbsoluteError += abs(actualValue - predictedValue)
            }
        }
        let mae = sumAbsoluteError / Double(feedbackWithValues.count)
        
        // Calculate Mean Squared Error (MSE)
        var sumSquaredError = 0.0
        for item in feedbackWithValues {
            if let predictedValue = item.predictedValue, let actualValue = item.actualValue {
                let error = actualValue - predictedValue
                sumSquaredError += error * error
            }
        }
        let mse = sumSquaredError / Double(feedbackWithValues.count)
        
        // Calculate Root Mean Squared Error (RMSE)
        let rmse = sqrt(mse)
        
        // Calculate Mean Absolute Percentage Error (MAPE)
        var sumAbsolutePercentageError = 0.0
        var mapeCount = 0
        
        for item in feedbackWithValues {
            if let predictedValue = item.predictedValue, let actualValue = item.actualValue, predictedValue != 0 {
                sumAbsolutePercentageError += abs((actualValue - predictedValue) / predictedValue)
                mapeCount += 1
            }
        }
        
        let mape = mapeCount > 0 ? (sumAbsolutePercentageError / Double(mapeCount)) * 100 : 0
        
        // Calculate time error metrics for event predictions
        var timeErrorMetrics: [String: Double] = [:]
        
        if predictionType.contains("event") {
            var totalTimeError = 0.0
            var timeErrorCount = 0
            
            for item in feedback {
                if let predictionTime = getPredictionTime(predictionId: item.predictionId),
                   let actualTime = getActualTime(predictionId: item.predictionId) {
                    let timeError = abs(actualTime.timeIntervalSince(predictionTime))
                    totalTimeError += timeError
                    timeErrorCount += 1
                }
            }
            
            if timeErrorCount > 0 {
                let averageTimeError = totalTimeError / Double(timeErrorCount)
                timeErrorMetrics["average_time_error_seconds"] = averageTimeError
                timeErrorMetrics["average_time_error_minutes"] = averageTimeError / 60
            }
        }
        
        var metrics: [String: Double] = [
            "MAE": mae,
            "MSE": mse,
            "RMSE": rmse,
            "MAPE": mape,
            "accuracy_rate": feedbackService.calculateAccuracyRate(for: predictionType)
        ]
        
        // Add time error metrics if available
        for (key, value) in timeErrorMetrics {
            metrics[key] = value
        }
        
        return metrics
    }
    
    /// Get the predicted time for a prediction
    /// - Parameter predictionId: The ID of the prediction
    /// - Returns: The predicted time, or nil if not available
    private func getPredictionTime(predictionId: UUID) -> Date? {
        guard let prediction = predictionStore.getPrediction(id: predictionId) else {
            return nil
        }
        
        // Use the middle of the timeframe as the predicted time
        let startTime = prediction.timeframe.start
        let endTime = prediction.timeframe.end
        
        return startTime.addingTimeInterval(endTime.timeIntervalSince(startTime) / 2)
    }
    
    /// Get the actual time for a prediction
    /// - Parameter predictionId: The ID of the prediction
    /// - Returns: The actual time, or nil if not available
    private func getActualTime(predictionId: UUID) -> Date? {
        // This would require looking up the actual entry that fulfilled this prediction
        // For now, we'll return nil as this would require additional data tracking
        return nil
    }
}
