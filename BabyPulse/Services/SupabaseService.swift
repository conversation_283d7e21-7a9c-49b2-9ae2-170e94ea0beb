import Foundation
import Supabase
import SwiftUI

/// Service for interacting with Supabase
class SupabaseService: ObservableObject {
    /// Shared instance of the service
    static let shared = SupabaseService()

    /// The Supabase client
    private(set) var client: SupabaseClient

    /// Authentication state
    @Published var isAuthenticated = false
    @Published var currentUser: User? = nil
    @Published var authError: Error? = nil

    /// Sync state
    @Published var isSyncEnabled = true // Default to true for simulation

    /// Private initializer for singleton
    private init() {
        // Initialize the Supabase client
        client = SupabaseClient(
            supabaseURL: URL(string: SupabaseConfig.projectURL)!,
            supabaseKey: SupabaseConfig.anonKey
        )

        // Check for existing session
        Task {
            await checkSession()
        }
    }

    /// Check if there's an existing session
    @MainActor
    func checkSession() async -> Bool {
        do {
            let session = try await client.auth.session
            self.currentUser = session.user
            self.isAuthenticated = true
            return true
        } catch {
            self.isAuthenticated = false
            self.currentUser = nil
            return false
        }
    }

    /// Sign up a new user
    @MainActor
    func signUp(email: String, password: String) async -> Bool {
        do {
            let authResponse = try await client.auth.signUp(
                email: email,
                password: password
            )
            self.currentUser = authResponse.user
            self.isAuthenticated = authResponse.user != nil // This will be true if we got here
            self.authError = nil
            return true
        } catch {
            self.authError = error
            print("Sign up error: \(error)")
            return false
        }
    }

    /// Sign in an existing user
    @MainActor
    func signIn(email: String, password: String) async -> Bool {
        do {
            let authResponse = try await client.auth.signIn(
                email: email,
                password: password
            )
            self.currentUser = authResponse.user
            self.isAuthenticated = true // User is authenticated if we got here
            self.authError = nil
            return true
        } catch {
            self.authError = error
            print("Sign in error: \(error)")
            return false
        }
    }

    /// Sign out the current user
    @MainActor
    func signOut() async -> Bool {
        do {
            try await client.auth.signOut()
            self.currentUser = nil
            self.isAuthenticated = false
            self.authError = nil
            return true
        } catch {
            self.authError = error
            print("Sign out error: \(error)")
            return false
        }
    }

    /// Send email verification
    @MainActor
    func sendEmailVerification() async -> Bool {
        // In a real implementation, this would call the Supabase API
        // For now, we'll simulate success
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
            return true
        } catch {
            self.authError = error
            print("Email verification error: \(error)")
            return false
        }
    }

    /// Reset password
    @MainActor
    func resetPassword(email: String) async -> Bool {
        do {
            // In a real implementation, this would call the Supabase API
            // For now, we'll simulate success
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
            return true
        } catch {
            self.authError = error
            print("Password reset error: \(error)")
            return false
        }
    }

    /// Refresh session
    @MainActor
    func refreshSession() async -> Bool {
        do {
            // In a real implementation, this would refresh the Supabase session
            // For now, we'll simulate success
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
            return true
        } catch {
            self.authError = error
            print("Session refresh error: \(error)")
            return false
        }
    }

    /// Upload a baby photo to storage
    func uploadBabyPhoto(babyId: String, imageData: Data) async throws -> String {
        // Simplified version without actual storage
        let fileName = "\(babyId)/\(UUID().uuidString).jpg"
        let filePath = "babies/\(fileName)"
        return filePath
    }

    /// Get a public URL for a baby photo
    func getBabyPhotoURL(path: String) -> URL? {
        // Simplified version without actual storage
        return URL(string: "https://example.com/\(path)")
    }

    /// Call the insights edge function
    func getInsights(babyId: String) async throws -> [SupabaseInsight] {
        // Mock response for now
        return [
            SupabaseInsight(title: "Feeding Pattern", content: "Your baby is feeding well", type: "info"),
            SupabaseInsight(title: "Sleep Pattern", content: "Your baby is sleeping well", type: "info"),
            SupabaseInsight(title: "Diaper Activity", content: "Normal diaper activity", type: "info")
        ]
    }

    /// Call the chat edge function
    func sendChatMessage(babyId: String, message: String, threadId: String? = nil) async throws -> (message: String, threadId: String) {
        // Use the LLMService to get a real response from OpenRouter API
        let threadIdToUse = threadId ?? UUID().uuidString

        // Get baby data for context
        let babyData = await getBabyDataForContext(babyId: babyId)

        // Create an LLMService instance
        let llmService = LLMService()

        // Use a continuation to convert the callback-based API to async/await
        return try await withCheckedThrowingContinuation { continuation in
            llmService.generateResponse(userMessage: message, babyData: babyData) { result in
                switch result {
                case .success(let response):
                    continuation.resume(returning: (message: response, threadId: threadIdToUse))
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    /// Get baby data for context
    private func getBabyDataForContext(babyId: String) async -> String {
        // This is a simplified version - in a real implementation, you would fetch
        // actual data from Supabase or local storage
        do {
            // Try to fetch baby info
            let response = try await client.from("babies")
                .select()
                .eq("id", value: babyId)
                .single()
                .execute()

            // Try to parse the JSON data manually
            if let jsonObject = try? JSONSerialization.jsonObject(with: response.data) as? [String: Any] {
                let name = jsonObject["name"] as? String ?? "Unknown"
                let birthDate = jsonObject["birth_date"] as? String ?? "Unknown"

                // Return a basic context
                return """
                Baby Name: \(name)
                Birth Date: \(birthDate)
                Baby ID: \(babyId)
                """
            }
        } catch {
            print("Error fetching baby data: \(error)")
        }

        // Return a minimal context if we couldn't fetch data
        return "Baby ID: \(babyId)"
    }

    /// Sync local data to Supabase
    func syncData<T: Encodable>(type: String, babyId: String, data: T) async throws {
        // Create a JSON encoder that can handle UUIDs
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601

        // Convert Encodable to dictionary
        let jsonData = try encoder.encode(data)
        guard let dictionary = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] else {
            throw NSError(domain: "SupabaseService", code: 3, userInfo: [NSLocalizedDescriptionKey: "Failed to convert data to dictionary"])
        }

        // Use the dictionary version
        try await syncData(type: type, babyId: babyId, data: dictionary)
    }

    /// Sync dictionary data to Supabase
    func syncData(type: String, babyId: String, data: [String: Any]) async throws {
        // Determine the table name based on the type
        let tableName: String
        switch type {
        case "baby":
            tableName = "babies"
        case "feeding":
            tableName = "feeding_entries"
        case "diaper":
            tableName = "diaper_entries"
        case "sleep":
            tableName = "sleep_entries"
        case "health":
            tableName = "health_entry"
        case "growth":
            tableName = "growth_entry"
        case "chat":
            tableName = "chat_message"
        case "thread":
            tableName = "chat_thread"
        default:
            throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
        }

        do {
            // Ensure user_id is included in the data
            var dataWithUserId = data
            if let userId = currentUser?.id {
                // Make sure to convert UUID to string
                dataWithUserId["user_id"] = userId.uuidString
            }

            // Process the data to ensure all values are JSON-serializable
            let processedData = processDataForJSON(dataWithUserId)

            // Convert dictionary to JSON data
            let jsonData = try JSONSerialization.data(withJSONObject: processedData)

            // Convert JSON data to a string
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                // Upsert the data (insert or update)
                let response = try await client.from(tableName)
                    .upsert(jsonString)
                    .execute()

                print("Successfully synced \(type) data for baby \(babyId)")
            } else {
                throw NSError(domain: "SupabaseService", code: 4, userInfo: [NSLocalizedDescriptionKey: "Failed to convert data to JSON string"])
            }
        } catch {
            print("Error syncing \(type) data for baby \(babyId): \(error)")
            throw error
        }
    }

    /// Process data to ensure all values are JSON-serializable
    private func processDataForJSON(_ data: [String: Any]) -> [String: Any] {
        var result: [String: Any] = [:]

        for (key, value) in data {
            if let uuid = value as? UUID {
                // Convert UUID to string
                result[key] = uuid.uuidString
            } else if let date = value as? Date {
                // Convert Date to ISO8601 string
                result[key] = ISO8601DateFormatter().string(from: date)
            } else if let dict = value as? [String: Any] {
                // Recursively process nested dictionaries
                result[key] = processDataForJSON(dict)
            } else if let array = value as? [Any] {
                // Process arrays
                result[key] = array.map { item in
                    if let uuid = item as? UUID {
                        return uuid.uuidString
                    } else if let date = item as? Date {
                        return ISO8601DateFormatter().string(from: date)
                    } else if let dict = item as? [String: Any] {
                        // We can't return a dictionary directly in a map that expects a specific type
                        // Convert the dictionary to a JSON string
                        if let jsonData = try? JSONSerialization.data(withJSONObject: processDataForJSON(dict)),
                           let jsonString = String(data: jsonData, encoding: .utf8) {
                            return jsonString
                        } else {
                            return "{}" // Return empty JSON object as fallback
                        }
                    } else if let string = item as? String {
                        return string
                    } else {
                        return "{}"
                    }
                }
            } else {
                // Pass through other values
                result[key] = value
            }
        }

        return result
    }

    /// Fetch data from Supabase
    func fetchData<T: Decodable>(type: String, babyId: String) async throws -> [T] {
        // Determine the table name based on the type
        let tableName: String
        switch type {
        case "baby":
            tableName = "babies"
        case "feeding":
            tableName = "feeding_entries"
        case "diaper":
            tableName = "diaper_entries"
        case "sleep":
            tableName = "sleep_entries"
        case "health":
            tableName = "health_entry"
        case "growth":
            tableName = "growth_entry"
        case "chat":
            tableName = "chat_message"
        case "thread":
            tableName = "chat_thread"
        default:
            throw NSError(domain: "SupabaseService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Unknown entry type: \(type)"])
        }

        do {
            // Fetch data for the specified baby
            let response = try await client.from(tableName)
                .select()
                .eq("baby_id", value: babyId)
                .execute()

            // Decode the response
            return try JSONDecoder().decode([T].self, from: response.data)
        } catch {
            print("Error fetching \(type) data for baby \(babyId): \(error)")
            throw error
        }
    }

    /// Sync a health entry to Supabase
    func syncHealthEntry(healthEntry: HealthEntry) async throws {
        guard let baby = healthEntry.baby else {
            throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Health entry has no associated baby"])
        }

        var entryData: [String: Any] = [
            "id": healthEntry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "kind": healthEntry.type.rawValue,
            "ts": ISO8601DateFormatter().string(from: healthEntry.timestamp),
            "notes": healthEntry.notes ?? NSNull()
        ]

        // Add type-specific fields
        switch healthEntry.type {
        case .temperature:
            if let temperature = healthEntry.temperature, let unit = healthEntry.temperatureUnit {
                // Convert to Celsius if needed
                let tempCelsius = unit == .fahrenheit ? (temperature - 32) * 5/9 : temperature
                entryData["temp_c"] = tempCelsius
            }
        case .medication:
            entryData["medication"] = healthEntry.medicationName ?? NSNull()
            entryData["dosage"] = healthEntry.medicationDosage ?? NSNull()
        case .symptom:
            let symptoms = healthEntry.getSymptoms()
            if !symptoms.isEmpty {
                entryData["symptom"] = symptoms[0].rawValue

                // Determine severity from notes
                if let notes = healthEntry.notes {
                    if notes.lowercased().contains("severe") {
                        entryData["severity"] = "severe"
                    } else if notes.lowercased().contains("moderate") {
                        entryData["severity"] = "moderate"
                    } else {
                        entryData["severity"] = "mild"
                    }
                } else {
                    entryData["severity"] = "mild"
                }
            }
        case .vaccination:
            entryData["vaccine"] = healthEntry.vaccineName ?? NSNull()
        case .appointment:
            entryData["place"] = healthEntry.appointmentProvider ?? NSNull()
            entryData["appt_reason"] = healthEntry.appointmentReason ?? NSNull()
        }

        // Convert to a JSON string and use syncData
        try await syncData(type: "health", babyId: baby.id.uuidString, data: entryData)
    }

    /// Sync a growth entry to Supabase
    func syncGrowthEntry(growthEntry: GrowthEntry) async throws {
        guard let baby = growthEntry.baby else {
            throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Growth entry has no associated baby"])
        }

        // Calculate percentiles if not already done
        growthEntry.calculatePercentiles()

        let entryData: [String: Any] = [
            "id": growthEntry.id.uuidString,
            "baby_id": baby.id.uuidString,
            "ts": ISO8601DateFormatter().string(from: growthEntry.timestamp),
            "weight_kg": growthEntry.weight ?? NSNull(),
            "height_cm": growthEntry.height ?? NSNull(),
            "head_cm": growthEntry.headCircumference ?? NSNull(),
            "weight_pct": growthEntry.weightPercentile ?? NSNull(),
            "height_pct": growthEntry.heightPercentile ?? NSNull(),
            "head_pct": growthEntry.headCircumferencePercentile ?? NSNull(),
            "notes": growthEntry.notes ?? NSNull()
        ]

        // Convert to a JSON string and use syncData
        try await syncData(type: "growth", babyId: baby.id.uuidString, data: entryData)
    }
}

/// Insight model for the insights edge function
struct SupabaseInsight: Codable, Identifiable {
    var id: String { title }
    let title: String
    let content: String
    let type: String
}
