import Foundation
import SwiftUI
import OSLog

// MARK: - String Extensions for Emoji Detection

extension Character {
    /// Check if the character is an emoji
    var isEmoji: Bool {
        // <PERSON>'s native way to check for emoji
        if let firstScalar = unicodeScalars.first, firstScalar.properties.isEmoji {
            return true
        }
        return false
    }
}

extension String {
    /// Check if the string contains any emoji
    var containsEmoji: Bool {
        return contains { $0.isEmoji }
    }
}

struct Message: Codable {
    let role: String
    let content: [MessageContent]
}

struct MessageContent: Codable {
    let type: String
    let text: String?
    let image_url: ImageURL?
}

struct ImageURL: Codable {
    let url: String
}

struct ChatRequest: Codable {
    let model: String
    let messages: [Message]
    var stream: Bool? = nil
}

struct ChatResponse: Codable {
    let id: String
    let choices: [Choice]
    let created: Int
    let model: String
    let usage: Usage
}

struct Choice: Codable {
    let index: Int
    let message: ResponseMessage
    let finish_reason: String
}

struct ResponseMessage: Codable {
    let role: String
    let content: String
}

struct Usage: Codable {
    let prompt_tokens: Int
    let completion_tokens: Int
    let total_tokens: Int
}

// Streaming response structures
struct StreamResponse: Codable {
    let id: String
    let choices: [StreamChoice]
    let created: Int
    let model: String
}

struct StreamChoice: Codable {
    let index: Int
    let delta: DeltaContent
    let finish_reason: String?
}

struct DeltaContent: Codable {
    let role: String?
    let content: String?
}

class LLMService {
    private let apiKey: String?
    private let baseURL = "https://openrouter.ai/api/v1/chat/completions"
    private let model = "deepseek/deepseek-chat-v3-0324:free"
    private let siteURL = "https://babypulse.ai"
    private let siteName = "BabyPulse"

    private let logger = Logger(subsystem: "com.babypulse", category: "LLMService")

    init(apiKey: String? = nil) {
        self.apiKey = apiKey
    }

    // MARK: - Streaming API

    /// Generate a streaming response using AsyncThrowingStream
    /// - Parameters:
    ///   - userMessage: The user's message to send to the LLM
    ///   - babyData: Context data about the baby
    /// - Returns: An AsyncThrowingStream that emits partial response tokens
    /// Error types specific to LLM service
    enum LLMServiceError: Error, LocalizedError {
        case invalidURL
        case invalidResponse
        case httpError(Int, String?)
        case parsingError(String)
        case timeout
        case networkError(Error)

        var errorDescription: String? {
            switch self {
            case .invalidURL:
                return "Invalid API URL"
            case .invalidResponse:
                return "Invalid HTTP response"
            case .httpError(let code, let message):
                return "HTTP error \(code): \(message ?? "Unknown error")"
            case .parsingError(let details):
                return "Error parsing response: \(details)"
            case .timeout:
                return "Request timed out"
            case .networkError(let error):
                return "Network error: \(error.localizedDescription)"
            }
        }
    }

    /// Generate a streaming response using AsyncThrowingStream
    /// - Parameters:
    ///   - userMessage: The user's message to send to the LLM
    ///   - babyData: Context data about the baby
    ///   - timeoutSeconds: Optional timeout in seconds (defaults to 30)
    /// - Returns: An AsyncThrowingStream that emits partial response tokens
    func generateStreamingResponse(
        userMessage: String,
        babyData: String,
        timeoutSeconds: Double = 30
    ) -> AsyncThrowingStream<String, Error> {
        return AsyncThrowingStream { continuation in
            // Create a task that can be cancelled if timeout occurs
            let task = Task {
                do {
                    let startTime = Date()
                    var fullResponse = ""
                    var lastActivityTime = Date()

                    // Create URL and request
                    guard let url = URL(string: baseURL) else {
                        throw LLMServiceError.invalidURL
                    }

                    // Get API key - use provided key or default to the one in APIKeys
                    let apiKey = self.apiKey ?? APIKeys.openRouter

                    // Create system message with baby data context and markdown formatting instructions
                    let systemContent = """
                    You are a helpful assistant for a baby tracking app called BabyPulse. You help parents understand their baby's patterns and provide advice.

                    Here is the current data about the baby:
                    \(babyData)

                    Keep your responses concise, informative, and parent-friendly. Avoid medical diagnoses and always suggest consulting healthcare providers for medical concerns.
                    """

                    // Create the request body with stream=true
                    var requestBody = ChatRequest(
                        model: model,
                        messages: [
                            Message(
                                role: "system",
                                content: [MessageContent(type: "text", text: systemContent, image_url: nil)]
                            ),
                            Message(
                                role: "user",
                                content: [MessageContent(type: "text", text: userMessage, image_url: nil)]
                            )
                        ]
                    )

                    // Add stream parameter
                    requestBody.stream = true

                    var request = URLRequest(url: url)
                    request.httpMethod = "POST"
                    request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
                    request.addValue(siteURL, forHTTPHeaderField: "HTTP-Referer")
                    request.addValue(siteName, forHTTPHeaderField: "X-Title")
                    request.addValue("application/json", forHTTPHeaderField: "Content-Type")

                    // Set timeout interval
                    request.timeoutInterval = timeoutSeconds

                    request.httpBody = try JSONEncoder().encode(requestBody)

                    // Log request details
                    logger.debug("Starting streaming request: \(userMessage.prefix(50))...")

                    // Use URLSession.bytes for streaming
                    let (bytes, response) = try await URLSession.shared.bytes(for: request)

                    guard let httpResponse = response as? HTTPURLResponse else {
                        throw LLMServiceError.invalidResponse
                    }

                    // Check for HTTP errors and extract error message if available
                    guard httpResponse.statusCode == 200 else {
                        // Try to extract error message from response
                        var errorMessage: String? = nil
                        if let errorData = try? await bytes.reduce(into: Data()) { $0.append($1) },
                           let errorJson = try? JSONSerialization.jsonObject(with: errorData) as? [String: Any],
                           let error = errorJson["error"] as? [String: Any],
                           let message = error["message"] as? String {
                            errorMessage = message
                        }
                        throw LLMServiceError.httpError(httpResponse.statusCode, errorMessage)
                    }

                    // Process the streaming response using a more efficient approach that preserves UTF-8 sequences
                    var buffer = ""
                    var byteAccumulator = Data() // For accumulating bytes until we have complete UTF-8 sequences
                    let decoder = JSONDecoder()

                    // Configure the decoder for better string handling
                    decoder.nonConformingFloatDecodingStrategy = .throw

                    // Create a task to check for timeout
                    let timeoutTask = Task {
                        while !Task.isCancelled {
                            // Check if we've exceeded the timeout since last activity
                            if Date().timeIntervalSince(lastActivityTime) > timeoutSeconds {
                                throw LLMServiceError.timeout
                            }
                            try await Task.sleep(nanoseconds: 1_000_000_000) // Check every second
                        }
                    }

                    // Process incoming bytes
                    for try await byte in bytes {
                        // Update last activity time
                        lastActivityTime = Date()

                        // Accumulate bytes until we have complete UTF-8 sequences
                        byteAccumulator.append(byte)

                        // Try to convert accumulated bytes to a string
                        if let newText = String(data: byteAccumulator, encoding: .utf8) {
                            // Successfully decoded the bytes to a string
                            buffer.append(newText)
                            byteAccumulator.removeAll() // Clear the accumulator

                            // Process complete lines from buffer
                            while let newlineIndex = buffer.firstIndex(of: "\n") {
                                let line = String(buffer[..<newlineIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                                buffer = String(buffer[buffer.index(after: newlineIndex)...])

                                // Skip empty lines
                                if line.isEmpty {
                                    continue
                                }

                                // Process data lines
                                if line.hasPrefix("data: ") {
                                    let data = line.dropFirst(6).trimmingCharacters(in: .whitespacesAndNewlines)

                                    // Check for end of stream
                                    if data == "[DONE]" {
                                        logger.debug("Received [DONE] message")
                                        continue
                                    }

                                    // Parse JSON data
                                    do {
                                        if let jsonData = data.data(using: .utf8) {
                                            // First, try to decode as a StreamResponse
                                            let streamResponse = try decoder.decode(StreamResponse.self, from: jsonData)
                                            if let content = streamResponse.choices.first?.delta.content {
                                                // The content might contain escaped characters that need proper decoding
                                                // Since the JSON decoder already handled the escaping, we can use it directly
                                                fullResponse += content
                                                continuation.yield(content)

                                                // Log a sample of received content for debugging (only first few tokens)
                                                if fullResponse.count < 50 {
                                                    logger.debug("Sample token received: '\(content)'")

                                                    // Special logging for emoji detection
                                                    if content.containsEmoji {
                                                        logger.debug("🎉 Emoji detected in token: '\(content)'")

                                                        // Log the UTF-8 code points for debugging
                                                        let codePoints = content.unicodeScalars.map { "U+\(String($0.value, radix: 16, uppercase: true))" }.joined(separator: " ")
                                                        logger.debug("Unicode code points: \(codePoints)")
                                                    }
                                                }
                                            }
                                        }
                                    } catch {
                                        // Try to extract more detailed error information
                                        logger.error("Error parsing SSE message: \(error.localizedDescription)")

                                        // Try to log the raw data for debugging
                                        if let rawString = data.data(using: .utf8).flatMap({ String(data: $0, encoding: .utf8) }) {
                                            logger.debug("Raw JSON that failed to parse: \(rawString.prefix(100))")

                                            // Fallback: Try to extract content directly using JSONSerialization
                                            // This can sometimes handle JSON that the Codable decoder struggles with
                                            do {
                                                if let rawData = rawString.data(using: .utf8),
                                                   let jsonObject = try JSONSerialization.jsonObject(with: rawData) as? [String: Any],
                                                   let choices = jsonObject["choices"] as? [[String: Any]],
                                                   let firstChoice = choices.first,
                                                   let delta = firstChoice["delta"] as? [String: Any],
                                                   let content = delta["content"] as? String {

                                                    // We found content using manual JSON parsing
                                                    fullResponse += content
                                                    continuation.yield(content)
                                                    logger.debug("Recovered content using JSONSerialization: '\(content)'")
                                                }
                                            } catch {
                                                logger.error("Fallback JSON parsing also failed: \(error.localizedDescription)")
                                            }
                                        }

                                        // Continue processing other messages even if one fails
                                    }
                                }
                            }
                        }
                    }

                    // Cancel the timeout task since we're done
                    timeoutTask.cancel()

                    // Log completion metrics
                    let duration = Date().timeIntervalSince(startTime)
                    logger.debug("Streaming completed in \(String(format: "%.2f", duration))s, \(fullResponse.count) chars")

                    // Finish the stream
                    continuation.finish()

                } catch {
                    // Map standard errors to our custom error types
                    let mappedError: Error
                    if let urlError = error as? URLError {
                        switch urlError.code {
                        case .timedOut:
                            mappedError = LLMServiceError.timeout
                        default:
                            mappedError = LLMServiceError.networkError(error)
                        }
                    } else if let llmError = error as? LLMServiceError {
                        mappedError = llmError
                    } else {
                        mappedError = LLMServiceError.networkError(error)
                    }

                    logger.error("Streaming error: \(mappedError.localizedDescription)")
                    continuation.finish(throwing: mappedError)
                }
            }

            // Set up cancellation handler
            continuation.onTermination = { _ in
                task.cancel()
            }
        }
    }

    // Legacy non-streaming method for backward compatibility
    func generateResponse(userMessage: String, babyData: String, timeoutSeconds: Double = 30, completion: @escaping (Result<String, Error>) -> Void) {
        Task {
            do {
                var fullResponse = ""
                let stream = generateStreamingResponse(userMessage: userMessage, babyData: babyData, timeoutSeconds: timeoutSeconds)

                for try await token in stream {
                    fullResponse += token
                }

                DispatchQueue.main.async {
                    completion(.success(fullResponse))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    /// Generate insight using a prompt template and variables
    /// - Parameters:
    ///   - template: The prompt template to use
    ///   - variables: Variables to inject into the template
    ///   - timeoutSeconds: Optional timeout in seconds (defaults to 45 for insights)
    ///   - completion: Completion handler with the result
    func generateInsight(using template: PromptTemplate, with variables: [String: String], timeoutSeconds: Double = 45, completion: @escaping (Result<String, Error>) -> Void) {
        // Format the template with variables
        let formattedPrompt = formatTemplate(template.template, with: variables)

        // Create baby context data from variables
        let babyContext = """
        Baby Name: \(variables["baby_name"] ?? "Unknown")
        Age: \(variables["baby_age"] ?? "Unknown") (\(variables["baby_age_weeks"] ?? "Unknown") weeks)
        Timestamp: \(variables["timestamp"] ?? "N/A")
        Category: \(variables["insight_topic"] ?? "N/A")
        """

        // Generate response using formatted prompt with specified timeout
        // Insights may need more time than regular chat messages
        generateResponse(userMessage: formattedPrompt, babyData: babyContext, timeoutSeconds: timeoutSeconds, completion: completion)
    }

    /// Replace template variables with actual values
    private func formatTemplate(_ template: String, with variables: [String: String]) -> String {
        var result = template
        for (key, value) in variables {
            let placeholder = "{{\(key)}}"
            result = result.replacingOccurrences(of: placeholder, with: value)
        }
        return result
    }
}
