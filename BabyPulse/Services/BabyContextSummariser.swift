import Foundation
import SwiftData

/// Service to generate a condensed summary of baby data for use in chat context
class BabyContextSummariser {
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// Generate a condensed summary of baby data (≤1k tokens)
    /// - Parameter babyId: The ID of the baby to summarize data for
    /// - Returns: A string containing the summarized baby data context
    func generateSummary(for babyId: UUID) -> String {
        do {
            // Fetch the baby
            let descriptor = FetchDescriptor<Baby>(predicate: #Predicate { $0.id == babyId })
            guard let baby = try modelContext.fetch(descriptor).first else {
                return "Baby data not available"
            }
            
            // Create the base summary with baby info
            var summary = """
            Baby name: \(baby.name)
            Age: \(baby.ageDescription)
            Gender: \(baby.gender.rawValue)
            """
            
            // Add latest growth data if available
            if let latestGrowth = fetchLatestGrowth(for: babyId) {
                summary += "\n\nLatest Growth:"
                if let weight = latestGrowth.weight {
                    summary += "\nWeight: \(String(format: "%.2f", weight)) kg"
                }
                if let height = latestGrowth.height {
                    summary += "\nHeight: \(String(format: "%.1f", height)) cm"
                }
            }
            
            // Add 24-hour stats
            summary += "\n\n--- Last 24 Hours Summary ---"
            
            // Feeding stats
            let feedingStats = generateFeedingStats(for: babyId)
            if !feedingStats.isEmpty {
                summary += "\n\n" + feedingStats
            }
            
            // Sleep stats
            let sleepStats = generateSleepStats(for: babyId)
            if !sleepStats.isEmpty {
                summary += "\n\n" + sleepStats
            }
            
            // Diaper stats
            let diaperStats = generateDiaperStats(for: babyId)
            if !diaperStats.isEmpty {
                summary += "\n\n" + diaperStats
            }
            
            // Health events
            let healthEvents = generateHealthSummary(for: babyId)
            if !healthEvents.isEmpty {
                summary += "\n\n" + healthEvents
            }
            
            // Add notable events
            summary += "\n\n--- Notable Recent Events ---"
            let notableEvents = generateNotableEvents(for: babyId)
            if !notableEvents.isEmpty {
                summary += "\n" + notableEvents
            } else {
                summary += "\nNo notable events in the recent history."
            }
            
            return summary
            
        } catch {
            print("Error generating baby context summary: \(error)")
            return "Error generating baby data summary"
        }
    }
    
    // MARK: - Helper Methods
    
    private func fetchLatestGrowth(for babyId: UUID) -> GrowthEntry? {
        do {
            var descriptor = FetchDescriptor<GrowthEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = 1
            
            let entries = try modelContext.fetch(descriptor)
            return entries.first { $0.baby?.id == babyId }
        } catch {
            print("Error fetching growth data: \(error)")
            return nil
        }
    }
    
    private func generateFeedingStats(for babyId: UUID) -> String {
        do {
            let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            
            // Fixed predicate to avoid mixing parameter styles
            let descriptor = FetchDescriptor<FeedingEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .forward)]
            )
            
            let allFeedings = try modelContext.fetch(descriptor)
            let feedings = allFeedings.filter {
                $0.baby?.id == babyId && $0.timestamp >= yesterday
            }
            
            if feedings.isEmpty {
                return ""
            }
            
            var totalBreastfeedings = 0
            var totalBottleFeedings = 0
            var totalBottleVolume = 0
            var totalSolidFoodFeedings = 0
            
            for feeding in feedings {
                switch feeding.type {
                case .breastfeeding:
                    totalBreastfeedings += 1
                case .bottleFeeding:
                    totalBottleFeedings += 1
                    if let volume = feeding.volume {
                        totalBottleVolume += Int(volume)
                    }
                case .solidFood:
                    totalSolidFoodFeedings += 1
                }
            }
            
            var stats = "Feeding (24h):"
            
            if totalBreastfeedings > 0 {
                stats += "\n• \(totalBreastfeedings) breastfeeding session\(totalBreastfeedings == 1 ? "" : "s")"
            }
            
            if totalBottleFeedings > 0 {
                stats += "\n• \(totalBottleFeedings) bottle feeding\(totalBottleFeedings == 1 ? "" : "s")"
                if totalBottleVolume > 0 {
                    stats += " (total \(totalBottleVolume) ml)"
                }
            }
            
            if totalSolidFoodFeedings > 0 {
                stats += "\n• \(totalSolidFoodFeedings) solid food meal\(totalSolidFoodFeedings == 1 ? "" : "s")"
            }
            
            return stats
            
        } catch {
            print("Error generating feeding stats: \(error)")
            return ""
        }
    }
    
    private func generateSleepStats(for babyId: UUID) -> String {
        do {
            let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            
            // Fixed predicate approach
            let descriptor = FetchDescriptor<SleepEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .forward)]
            )
            
            let allSleeps = try modelContext.fetch(descriptor)
            let sleeps = allSleeps.filter {
                $0.baby?.id == babyId && $0.timestamp >= yesterday
            }
            
            if sleeps.isEmpty {
                return ""
            }
            
            var totalSleepMinutes = 0
            var sleepCount = 0
            
            for sleep in sleeps {
                if let endTime = sleep.endTime {
                    let duration = Int(endTime.timeIntervalSince(sleep.timestamp) / 60)
                    totalSleepMinutes += duration
                    sleepCount += 1
                }
            }
            
            var stats = "Sleep (24h):"
            
            if sleepCount > 0 {
                let hours = totalSleepMinutes / 60
                let minutes = totalSleepMinutes % 60
                
                stats += "\n• \(sleepCount) sleep session\(sleepCount == 1 ? "" : "s")"
                stats += "\n• Total sleep: \(hours)h \(minutes)m"
                
                if sleepCount > 0 {
                    let avgMinutes = totalSleepMinutes / sleepCount
                    let avgHours = avgMinutes / 60
                    let avgRemainingMinutes = avgMinutes % 60
                    
                    stats += "\n• Average nap: \(avgHours)h \(avgRemainingMinutes)m"
                }
            } else {
                stats += "\n• No completed sleep sessions recorded"
            }
            
            return stats
            
        } catch {
            print("Error generating sleep stats: \(error)")
            return ""
        }
    }
    
    private func generateDiaperStats(for babyId: UUID) -> String {
        do {
            let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            
            // Fixed predicate approach
            let descriptor = FetchDescriptor<DiaperEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .forward)]
            )
            
            let allDiapers = try modelContext.fetch(descriptor)
            let diapers = allDiapers.filter {
                $0.baby?.id == babyId && $0.timestamp >= yesterday
            }
            
            if diapers.isEmpty {
                return ""
            }
            
            var wetCount = 0
            var dirtyCount = 0
            var mixedCount = 0
            
            for diaper in diapers {
                switch diaper.type {
                case .wet:
                    wetCount += 1
                case .dirty:
                    dirtyCount += 1
                case .mixed:
                    mixedCount += 1
                }
            }
            
            var stats = "Diapers (24h):"
            stats += "\n• Total: \(diapers.count) diaper change\(diapers.count == 1 ? "" : "s")"
            
            if wetCount > 0 {
                stats += "\n• \(wetCount) wet"
            }
            
            if dirtyCount > 0 {
                stats += "\n• \(dirtyCount) dirty"
            }
            
            if mixedCount > 0 {
                stats += "\n• \(mixedCount) mixed (wet & dirty)"
            }
            
            return stats
            
        } catch {
            print("Error generating diaper stats: \(error)")
            return ""
        }
    }
    
    private func generateHealthSummary(for babyId: UUID) -> String {
        do {
            let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            
            // Fixed predicate approach
            let descriptor = FetchDescriptor<HealthEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .forward)]
            )
            
            let allHealthEntries = try modelContext.fetch(descriptor)
            let healthEntries = allHealthEntries.filter {
                $0.baby?.id == babyId && $0.timestamp >= yesterday
            }
            
            if healthEntries.isEmpty {
                return ""
            }
            
            var stats = "Health (24h):"
            
            for (index, entry) in healthEntries.enumerated() {
                stats += "\n• "
                
                switch entry.type {
                case .temperature:
                    if let temp = entry.temperature, let unit = entry.temperatureUnit {
                        stats += "Temperature: \(String(format: "%.1f", temp))\(unit.symbol)"
                    } else {
                        stats += "Temperature checked"
                    }
                case .medication:
                    if let name = entry.medicationName {
                        stats += "Medication: \(name)"
                        if let dosage = entry.medicationDosage {
                            stats += " (\(dosage))"
                        }
                    } else {
                        stats += "Medication given"
                    }
                case .symptom:
                    let symptoms = entry.getSymptoms()
                    if !symptoms.isEmpty {
                        stats += "Symptoms: " + symptoms.map { $0.rawValue }.joined(separator: ", ")
                    } else {
                        stats += "Symptoms recorded"
                    }
                case .vaccination:
                    if let name = entry.vaccineName {
                        stats += "Vaccination: \(name)"
                    } else {
                        stats += "Vaccination"
                    }
                case .appointment:
                    if let reason = entry.appointmentReason {
                        stats += "Appointment: \(reason)"
                    } else {
                        stats += "Medical appointment"
                    }
                }
            }
            
            return stats
            
        } catch {
            print("Error generating health summary: \(error)")
            return ""
        }
    }
    
    private func generateNotableEvents(for babyId: UUID) -> String {
        var notableEvents: [String] = []
        
        // Check for any high temperature in the last 3 days
        if let highTemp = checkForHighTemperature(babyId: babyId) {
            notableEvents.append(highTemp)
        }
        
        // Check for any concerning symptoms in the last 3 days
        if let symptoms = checkForConcerningSymptoms(babyId: babyId) {
            notableEvents.append(symptoms)
        }
        
        // Check for feeding pattern changes
        if let feedingPattern = checkForFeedingPatternChanges(babyId: babyId) {
            notableEvents.append(feedingPattern)
        }
        
        // Check for sleep pattern changes
        if let sleepPattern = checkForSleepPatternChanges(babyId: babyId) {
            notableEvents.append(sleepPattern)
        }
        
        return notableEvents.joined(separator: "\n")
    }
    
    private func checkForHighTemperature(babyId: UUID) -> String? {
        do {
            let threeDaysAgo = Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date()
            
            // Fixed predicate approach
            let descriptor = FetchDescriptor<HealthEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            
            let allEntries = try modelContext.fetch(descriptor)
            let entries = allEntries.filter {
                $0.baby?.id == babyId &&
                $0.timestamp >= threeDaysAgo &&
                $0.type == .temperature
            }
            
            for entry in entries {
                if let temp = entry.temperature, let unit = entry.temperatureUnit {
                    // Check if temperature is high (> 38°C or > 100.4°F)
                    let isHigh = (unit == .celsius && temp > 38.0) || (unit == .fahrenheit && temp > 100.4)
                    
                    if isHigh {
                        let timeAgo = formatTimeAgo(entry.timestamp)
                        return "• High temperature of \(String(format: "%.1f", temp))\(unit.symbol) recorded \(timeAgo)"
                    }
                }
            }
            
            return nil
            
        } catch {
            print("Error checking for high temperature: \(error)")
            return nil
        }
    }
    
    private func checkForConcerningSymptoms(babyId: UUID) -> String? {
        do {
            let threeDaysAgo = Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date()
            
            // Fixed predicate approach
            let descriptor = FetchDescriptor<HealthEntry>(
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            
            let allEntries = try modelContext.fetch(descriptor)
            let entries = allEntries.filter {
                $0.baby?.id == babyId &&
                $0.timestamp >= threeDaysAgo &&
                $0.type == .symptom
            }
            
            for entry in entries {
                let symptoms = entry.getSymptoms()
                let concerningSymptoms = symptoms.filter {
                    ["Rash", "Vomiting", "Diarrhea", "Fever", "Difficulty Breathing"].contains($0.rawValue)
                }
                
                if !concerningSymptoms.isEmpty {
                    let timeAgo = formatTimeAgo(entry.timestamp)
                    let symptomList = concerningSymptoms.map { $0.rawValue }.joined(separator: ", ")
                    return "• Concerning symptoms: \(symptomList) recorded \(timeAgo)"
                }
            }
            
            return nil
            
        } catch {
            print("Error checking for concerning symptoms: \(error)")
            return nil
        }
    }
    
    private func checkForFeedingPatternChanges(babyId: UUID) -> String? {
        // This would require more complex analysis of feeding patterns over time
        // Simplified version for implementation
        return nil
    }
    
    private func checkForSleepPatternChanges(babyId: UUID) -> String? {
        // This would require more complex analysis of sleep patterns over time
        // Simplified version for implementation
        return nil
    }
    
    private func formatTimeAgo(_ date: Date) -> String {
        let now = Date()
        let components = Calendar.current.dateComponents([.minute, .hour, .day], from: date, to: now)
        
        if let day = components.day, day > 0 {
            return "\(day) day\(day == 1 ? "" : "s") ago"
        } else if let hour = components.hour, hour > 0 {
            return "\(hour) hour\(hour == 1 ? "" : "s") ago"
        } else if let minute = components.minute, minute > 0 {
            return "\(minute) minute\(minute == 1 ? "" : "s") ago"
        } else {
            return "just now"
        }
    }
}
