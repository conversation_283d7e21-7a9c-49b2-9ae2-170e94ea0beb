//
//  CorrelationAnalysisService.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

/// Service for performing cross-category correlation analysis
class CorrelationAnalysisService {
    // MARK: - Properties

    private let modelContext: ModelContext

    // MARK: - Initialization

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    // MARK: - Public Methods

    /// Analyze correlations between two categories
    /// - Parameters:
    ///   - primaryCategory: The primary category to analyze
    ///   - secondaryCategory: The secondary category to correlate with
    ///   - baby: The baby whose data is being analyzed
    ///   - timeframe: The timeframe to analyze
    /// - Returns: Array of correlation analyses
    func analyzeCorrelation(
        primaryCategory: Insight.InsightCategory,
        secondaryCategory: Insight.InsightCategory,
        for baby: Baby,
        timeframe: AnalysisTimeframe
    ) -> [CorrelationAnalysis] {
        // Get data for both categories
        let primaryData = fetchCategoryData(category: primaryCategory, for: baby, timeframe: timeframe)
        let secondaryData = fetchCategoryData(category: secondaryCategory, for: baby, timeframe: timeframe)

        // Ensure we have enough data points
        guard primaryData.count >= 5, secondaryData.count >= 5 else {
            return []
        }

        var correlations: [CorrelationAnalysis] = []

        // Analyze direct correlation
        if let directCorrelation = analyzeDirectCorrelation(
            primaryCategory: primaryCategory,
            primaryData: primaryData,
            secondaryCategory: secondaryCategory,
            secondaryData: secondaryData
        ) {
            correlations.append(directCorrelation)
        }

        // Analyze lag correlation (one category leading the other)
        if let lagCorrelation = analyzeLagCorrelation(
            primaryCategory: primaryCategory,
            primaryData: primaryData,
            secondaryCategory: secondaryCategory,
            secondaryData: secondaryData
        ) {
            correlations.append(lagCorrelation)
        }

        return correlations
    }

    // MARK: - Private Methods

    /// Fetch data for a specific category
    /// - Parameters:
    ///   - category: The category to fetch data for
    ///   - baby: The baby whose data is being analyzed
    ///   - timeframe: The timeframe to analyze
    /// - Returns: Array of (timestamp, value) tuples
    private func fetchCategoryData(
        category: Insight.InsightCategory,
        for baby: Baby,
        timeframe: AnalysisTimeframe
    ) -> [(timestamp: Date, value: Double)] {
        let timeRange = timeframe.asTimeRange

        switch category {
        case .sleep:
            let sleepAnalyzer = SleepAnalyzer()
            let entries = sleepAnalyzer.fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)
            return entries.compactMap { entry -> (timestamp: Date, value: Double)? in
                if let duration = entry.duration {
                    return (timestamp: entry.timestamp, value: Double(duration))
                }
                return nil
            }

        case .feeding:
            let feedingAnalyzer = FeedingAnalyzer()
            let entries = feedingAnalyzer.fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)
            return entries.compactMap { entry in
                if let volume = entry.volume {
                    return (timestamp: entry.timestamp, value: volume)
                } else if let duration = entry.duration {
                    return (timestamp: entry.timestamp, value: Double(duration))
                } else {
                    return nil
                }
            }

        case .diaper:
            let diaperAnalyzer = DiaperAnalyzer()
            let entries = diaperAnalyzer.fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)
            // Group by day and count
            let calendar = Calendar.current
            var dailyCounts: [Date: Int] = [:]

            for entry in entries {
                let day = calendar.startOfDay(for: entry.timestamp)
                dailyCounts[day, default: 0] += 1
            }

            return dailyCounts.map { (timestamp: $0.key, value: Double($0.value)) }

        case .growth:
            let growthAnalyzer = GrowthAnalyzer()
            let entries = growthAnalyzer.fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)
            return entries.compactMap { entry in
                if let weight = entry.weight {
                    return (timestamp: entry.timestamp, value: weight)
                } else if let height = entry.height {
                    return (timestamp: entry.timestamp, value: height)
                } else if let headCircumference = entry.headCircumference {
                    return (timestamp: entry.timestamp, value: headCircumference)
                } else {
                    return nil
                }
            }

        case .health:
            let healthAnalyzer = HealthAnalyzer()
            let entries = healthAnalyzer.fetchEntries(for: baby, inTimeRange: timeRange, modelContext: modelContext)
            return entries.compactMap { entry in
                if let temperature = entry.temperature {
                    return (timestamp: entry.timestamp, value: temperature)
                } else {
                    return nil
                }
            }

        default:
            return []
        }
    }

    /// Analyze direct correlation between two categories
    /// - Parameters:
    ///   - primaryCategory: The primary category
    ///   - primaryData: Data points for the primary category
    ///   - secondaryCategory: The secondary category
    ///   - secondaryData: Data points for the secondary category
    /// - Returns: Correlation analysis if significant correlation found, nil otherwise
    private func analyzeDirectCorrelation(
        primaryCategory: Insight.InsightCategory,
        primaryData: [(timestamp: Date, value: Double)],
        secondaryCategory: Insight.InsightCategory,
        secondaryData: [(timestamp: Date, value: Double)]
    ) -> CorrelationAnalysis? {
        // Group data by day for comparison
        let primaryByDay = groupDataByDay(primaryData)
        let secondaryByDay = groupDataByDay(secondaryData)

        // Find common days
        let commonDays = Set(primaryByDay.keys).intersection(Set(secondaryByDay.keys))

        // Need at least 3 common days for correlation
        guard commonDays.count >= 3 else {
            return nil
        }

        // Create arrays of values for common days
        var primaryValues: [Double] = []
        var secondaryValues: [Double] = []

        for day in commonDays.sorted() {
            if let primaryValue = primaryByDay[day], let secondaryValue = secondaryByDay[day] {
                primaryValues.append(primaryValue)
                secondaryValues.append(secondaryValue)
            }
        }

        // Calculate correlation coefficient
        let correlationCoefficient = calculateCorrelationCoefficient(primaryValues, secondaryValues)

        // Only return if correlation is significant
        guard abs(correlationCoefficient) >= 0.3 else {
            return nil
        }

        // Create description based on correlation strength and direction
        let description = createCorrelationDescription(
            primaryCategory: primaryCategory,
            secondaryCategory: secondaryCategory,
            correlationStrength: correlationCoefficient,
            lagPeriod: nil
        )

        return CorrelationAnalysis(
            primaryCategory: primaryCategory,
            secondaryCategory: secondaryCategory,
            correlationStrength: correlationCoefficient,
            lagPeriod: nil,
            description: description,
            exceedsThreshold: abs(correlationCoefficient) >= 0.5
        )
    }

    /// Analyze lag correlation between two categories
    /// - Parameters:
    ///   - primaryCategory: The primary category
    ///   - primaryData: Data points for the primary category
    ///   - secondaryCategory: The secondary category
    ///   - secondaryData: Data points for the secondary category
    /// - Returns: Correlation analysis if significant lag correlation found, nil otherwise
    private func analyzeLagCorrelation(
        primaryCategory: Insight.InsightCategory,
        primaryData: [(timestamp: Date, value: Double)],
        secondaryCategory: Insight.InsightCategory,
        secondaryData: [(timestamp: Date, value: Double)]
    ) -> CorrelationAnalysis? {
        // Sort data by timestamp
        let sortedPrimaryData = primaryData.sorted { $0.timestamp < $1.timestamp }
        let sortedSecondaryData = secondaryData.sorted { $0.timestamp < $1.timestamp }

        // Need enough data points
        guard sortedPrimaryData.count >= 5, sortedSecondaryData.count >= 5 else {
            return nil
        }

        // Try different lag periods
        let lagPeriods: [TimeInterval] = [
            1 * 3600,     // 1 hour
            2 * 3600,     // 2 hours
            4 * 3600,     // 4 hours
            8 * 3600,     // 8 hours
            12 * 3600,    // 12 hours
            24 * 3600     // 24 hours
        ]

        var bestCorrelation = 0.0
        var bestLagPeriod: TimeInterval = 0

        for lagPeriod in lagPeriods {
            // Try primary leading secondary
            let primaryLeadingCorrelation = calculateLagCorrelation(
                leadingData: sortedPrimaryData,
                followingData: sortedSecondaryData,
                lagPeriod: lagPeriod
            )

            // Try secondary leading primary
            let secondaryLeadingCorrelation = calculateLagCorrelation(
                leadingData: sortedSecondaryData,
                followingData: sortedPrimaryData,
                lagPeriod: lagPeriod
            )

            if abs(primaryLeadingCorrelation) > abs(bestCorrelation) {
                bestCorrelation = primaryLeadingCorrelation
                bestLagPeriod = lagPeriod
            }

            if abs(secondaryLeadingCorrelation) > abs(bestCorrelation) {
                bestCorrelation = secondaryLeadingCorrelation
                bestLagPeriod = -lagPeriod // Negative indicates secondary leading primary
            }
        }

        // Only return if correlation is significant
        guard abs(bestCorrelation) >= 0.4 else {
            return nil
        }

        // Determine which category leads
        let (leadingCategory, followingCategory, actualLagPeriod) = bestLagPeriod >= 0
            ? (primaryCategory, secondaryCategory, bestLagPeriod)
            : (secondaryCategory, primaryCategory, -bestLagPeriod)

        // Create description
        let description = createLagCorrelationDescription(
            leadingCategory: leadingCategory,
            followingCategory: followingCategory,
            correlationStrength: bestCorrelation,
            lagPeriod: actualLagPeriod
        )

        return CorrelationAnalysis(
            primaryCategory: primaryCategory,
            secondaryCategory: secondaryCategory,
            correlationStrength: bestCorrelation,
            lagPeriod: actualLagPeriod,
            description: description,
            exceedsThreshold: abs(bestCorrelation) >= 0.6
        )
    }

    /// Group data by day
    /// - Parameter data: Array of (timestamp, value) tuples
    /// - Returns: Dictionary mapping days to average values
    private func groupDataByDay(_ data: [(timestamp: Date, value: Double)]) -> [Date: Double] {
        let calendar = Calendar.current
        var dailyValues: [Date: [Double]] = [:]

        for dataPoint in data {
            let day = calendar.startOfDay(for: dataPoint.timestamp)
            dailyValues[day, default: []].append(dataPoint.value)
        }

        // Calculate average for each day
        var result: [Date: Double] = [:]
        for (day, values) in dailyValues {
            result[day] = values.reduce(0, +) / Double(values.count)
        }

        return result
    }

    /// Calculate correlation coefficient between two arrays
    /// - Parameters:
    ///   - x: First array of values
    ///   - y: Second array of values
    /// - Returns: Pearson correlation coefficient
    private func calculateCorrelationCoefficient(_ x: [Double], _ y: [Double]) -> Double {
        guard x.count == y.count, x.count > 1 else {
            return 0
        }

        let n = Double(x.count)

        // Calculate means
        let meanX = x.reduce(0, +) / n
        let meanY = y.reduce(0, +) / n

        // Calculate covariance and variances
        var covariance = 0.0
        var varianceX = 0.0
        var varianceY = 0.0

        for i in 0..<x.count {
            let diffX = x[i] - meanX
            let diffY = y[i] - meanY

            covariance += diffX * diffY
            varianceX += diffX * diffX
            varianceY += diffY * diffY
        }

        // Avoid division by zero
        if varianceX == 0 || varianceY == 0 {
            return 0
        }

        return covariance / sqrt(varianceX * varianceY)
    }

    /// Calculate lag correlation between two time series
    /// - Parameters:
    ///   - leadingData: Data points for the leading category
    ///   - followingData: Data points for the following category
    ///   - lagPeriod: The lag period to test
    /// - Returns: Correlation coefficient
    private func calculateLagCorrelation(
        leadingData: [(timestamp: Date, value: Double)],
        followingData: [(timestamp: Date, value: Double)],
        lagPeriod: TimeInterval
    ) -> Double {
        var pairedValues: [(leading: Double, following: Double)] = []

        for leadingPoint in leadingData {
            // Find the closest following point after the lag period
            let targetTime = leadingPoint.timestamp.addingTimeInterval(lagPeriod)

            if let closestFollowingPoint = findClosestDataPoint(to: targetTime, in: followingData) {
                pairedValues.append((leading: leadingPoint.value, following: closestFollowingPoint.value))
            }
        }

        // Need at least 3 paired values for correlation
        guard pairedValues.count >= 3 else {
            return 0
        }

        let leadingValues = pairedValues.map { $0.leading }
        let followingValues = pairedValues.map { $0.following }

        return calculateCorrelationCoefficient(leadingValues, followingValues)
    }

    /// Find the closest data point to a target time
    /// - Parameters:
    ///   - targetTime: The target time
    ///   - data: Array of data points
    /// - Returns: The closest data point, or nil if none is within a reasonable window
    private func findClosestDataPoint(
        to targetTime: Date,
        in data: [(timestamp: Date, value: Double)]
    ) -> (timestamp: Date, value: Double)? {
        // Define a reasonable window (e.g., ±2 hours)
        let windowSize: TimeInterval = 2 * 3600

        // Filter data points within the window
        let candidatePoints = data.filter {
            abs($0.timestamp.timeIntervalSince(targetTime)) <= windowSize
        }

        // Find the closest point
        return candidatePoints.min(by: {
            abs($0.timestamp.timeIntervalSince(targetTime)) < abs($1.timestamp.timeIntervalSince(targetTime))
        })
    }

    /// Create a description for a direct correlation
    /// - Parameters:
    ///   - primaryCategory: The primary category
    ///   - secondaryCategory: The secondary category
    ///   - correlationStrength: The correlation coefficient
    ///   - lagPeriod: The lag period, if any
    /// - Returns: Human-readable description
    private func createCorrelationDescription(
        primaryCategory: Insight.InsightCategory,
        secondaryCategory: Insight.InsightCategory,
        correlationStrength: Double,
        lagPeriod: TimeInterval?
    ) -> String {
        let strengthDescription: String
        let absStrength = abs(correlationStrength)

        if absStrength >= 0.8 {
            strengthDescription = "strong"
        } else if absStrength >= 0.5 {
            strengthDescription = "moderate"
        } else {
            strengthDescription = "weak"
        }

        let directionDescription = correlationStrength > 0 ? "positive" : "negative"

        if let lagPeriod = lagPeriod {
            let formattedLag = formatTimeInterval(lagPeriod)
            return "A \(strengthDescription) \(directionDescription) correlation between \(primaryCategory.description.lowercased()) and \(secondaryCategory.description.lowercased()) with a \(formattedLag) lag"
        } else {
            return "A \(strengthDescription) \(directionDescription) correlation between \(primaryCategory.description.lowercased()) and \(secondaryCategory.description.lowercased())"
        }
    }

    /// Create a description for a lag correlation
    /// - Parameters:
    ///   - leadingCategory: The category that leads
    ///   - followingCategory: The category that follows
    ///   - correlationStrength: The correlation coefficient
    ///   - lagPeriod: The lag period
    /// - Returns: Human-readable description
    private func createLagCorrelationDescription(
        leadingCategory: Insight.InsightCategory,
        followingCategory: Insight.InsightCategory,
        correlationStrength: Double,
        lagPeriod: TimeInterval
    ) -> String {
        let strengthDescription: String
        let absStrength = abs(correlationStrength)

        if absStrength >= 0.8 {
            strengthDescription = "strong"
        } else if absStrength >= 0.5 {
            strengthDescription = "moderate"
        } else {
            strengthDescription = "weak"
        }

        let directionDescription = correlationStrength > 0 ? "increase" : "decrease"
        let formattedLag = formatTimeInterval(lagPeriod)

        return "Changes in \(leadingCategory.description.lowercased()) tend to be followed by a \(directionDescription) in \(followingCategory.description.lowercased()) after \(formattedLag)"
    }

    /// Format a time interval in a human-readable way
    /// - Parameter interval: The time interval to format
    /// - Returns: A human-readable string representation of the interval
    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval / 3600)
        let minutes = Int((interval.truncatingRemainder(dividingBy: 3600)) / 60)

        if hours > 0 {
            if minutes > 0 {
                return "\(hours) hour\(hours == 1 ? "" : "s") and \(minutes) minute\(minutes == 1 ? "" : "s")"
            } else {
                return "\(hours) hour\(hours == 1 ? "" : "s")"
            }
        } else {
            return "\(minutes) minute\(minutes == 1 ? "" : "s")"
        }
    }
}
