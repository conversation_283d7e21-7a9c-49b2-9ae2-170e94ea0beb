//
//  LogEntryObserver.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData
import Combine

/// Observer for log entries that triggers insight generation
class LogEntryObserver {
    private let modelContext: ModelContext
    private var insightService: EnhancedInsightService?
    private var cancellables = Set<AnyCancellable>()

    init(modelContext: ModelContext) {
        self.modelContext = modelContext

        // Initialize the insight service on the main actor
        Task { @MainActor in
            self.insightService = EnhancedInsightService(modelContext: modelContext)
        }

        // Set up notification observers
        setupObservers()
    }

    private func setupObservers() {
        // Observe feeding entries
        NotificationCenter.default.publisher(for: .feedingEntryAdded)
            .sink { [weak self] notification in
                guard let self = self,
                      let entry = notification.object as? FeedingEntry,
                      let baby = entry.baby else {
                    return
                }

                self.generateInsightsForNewEntry(entry, baby: baby)
            }
            .store(in: &cancellables)

        // Observe sleep entries
        NotificationCenter.default.publisher(for: .sleepEntryAdded)
            .sink { [weak self] notification in
                guard let self = self,
                      let entry = notification.object as? SleepEntry,
                      let baby = entry.baby else {
                    return
                }

                self.generateInsightsForNewEntry(entry, baby: baby)
            }
            .store(in: &cancellables)

        // Observe diaper entries
        NotificationCenter.default.publisher(for: .diaperEntryAdded)
            .sink { [weak self] notification in
                guard let self = self,
                      let entry = notification.object as? DiaperEntry,
                      let baby = entry.baby else {
                    return
                }

                self.generateInsightsForNewEntry(entry, baby: baby)
            }
            .store(in: &cancellables)

        // Observe growth entries
        NotificationCenter.default.publisher(for: .growthEntryAdded)
            .sink { [weak self] notification in
                guard let self = self,
                      let entry = notification.object as? GrowthEntry,
                      let baby = entry.baby else {
                    return
                }

                self.generateInsightsForNewEntry(entry, baby: baby)
            }
            .store(in: &cancellables)

        // Observe health entries
        NotificationCenter.default.publisher(for: .healthEntryAdded)
            .sink { [weak self] notification in
                guard let self = self,
                      let entry = notification.object as? HealthEntry,
                      let baby = entry.baby else {
                    return
                }

                self.generateInsightsForNewEntry(entry, baby: baby)
            }
            .store(in: &cancellables)
    }

    private func generateInsightsForNewEntry(_ entry: Any, baby: Baby) {
        Task { @MainActor in
            guard let insightService = self.insightService else { return }

            insightService.generateInsightsForNewEntry(entry, baby: baby) { insights in
                // If insights were generated, post a notification
                if !insights.isEmpty {
                    Task { @MainActor in
                        NotificationCenter.default.post(
                            name: .insightsGenerated,
                            object: insights
                        )
                    }
                }
            }
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let feedingEntryAdded = Notification.Name("feedingEntryAdded")
    static let sleepEntryAdded = Notification.Name("sleepEntryAdded")
    static let diaperEntryAdded = Notification.Name("diaperEntryAdded")
    static let growthEntryAdded = Notification.Name("growthEntryAdded")
    static let healthEntryAdded = Notification.Name("healthEntryAdded")
    static let generalActivityAdded = Notification.Name("generalActivityAdded")
    static let insightsGenerated = Notification.Name("insightsGenerated")

    // Notifications for updated entries
    static let feedingEntryUpdated = Notification.Name("feedingEntryUpdated")
    static let sleepEntryUpdated = Notification.Name("sleepEntryUpdated")
    static let diaperEntryUpdated = Notification.Name("diaperEntryUpdated")
    static let growthEntryUpdated = Notification.Name("growthEntryUpdated")
    static let healthEntryUpdated = Notification.Name("healthEntryUpdated")
    static let generalActivityEntryUpdated = Notification.Name("generalActivityEntryUpdated")
}
