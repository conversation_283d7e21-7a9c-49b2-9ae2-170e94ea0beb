//
//  ShadowModifier.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

/// Applies the BabyPulse design-system shadow.
struct ShadowModifier: ViewModifier {
    /// Optional design-system shadow configuration.
    let configuration: BabyPulseShadows.Shadow?

    func body(content: Content) -> some View {
        // Use a local that won't collide with SwiftUI.shadow(…)
        if let s = configuration {
            content.shadow(color: s.color,
                           radius: s.radius,
                           x: s.x,
                           y: s.y)
        } else {
            content
        }
    }
}

extension View {
    @inline(__always)          // keep if you like; it’s fine
    func applyShadow(_ configuration: BabyPulseShadows.Shadow?) -> some View {
        modifier(ShadowModifier(configuration: configuration))
    }
}
