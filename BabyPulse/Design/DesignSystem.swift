//
//  DesignSystem.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI

struct BabyPulseColors {
    // Main colors
    static let primary = Color(hex: "4CE1B6") // Mint primary color
    static let secondary = Color(hex: "223254") // Dark navy for headlines
    static let accent = Color(hex: "FF6B6B") // Coral accent color

    // Gradients
    static let primaryGradient = LinearGradient(
        gradient: Gradient(colors: [Color(hex: "4CE1B6"), Color(hex: "3CCCB4")]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let secondaryGradient = LinearGradient(
        gradient: Gradient(colors: [Color(hex: "223254"), Color(hex: "192841")]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // MARK: - Semantic Colors for Dark Mode Support

    // Backgrounds - Adaptive colors that work in both light and dark mode
    static let background = Color(.systemBackground)
    static let secondaryBackground = Color(.secondarySystemBackground)
    static let tertiaryBackground = Color(.tertiarySystemBackground)
    static let cardBackground = Color(.systemBackground)

    // Surface colors for cards and elevated content
    static let surfacePrimary = Color(.systemBackground)
    static let surfaceSecondary = Color(.secondarySystemBackground)
    static let surfaceTertiary = Color(.tertiarySystemBackground)

    // Text colors - Adaptive for dark mode
    static let text = Color(.label)
    static let textSecondary = Color(.secondaryLabel)
    static let textTertiary = Color(.tertiaryLabel)
    static let textQuaternary = Color(.quaternaryLabel)

    // Grays - Adaptive system grays
    static let lightGray = Color(.systemGray6)
    static let mediumGray = Color(.systemGray4)
    static let darkGray = Color(.systemGray2)

    // Separator colors
    static let separator = Color(.separator)
    static let opaqueSeparator = Color(.opaqueSeparator)

    // Feedback colors
    static let error = Color(hex: "EF4444")
    static let success = Color(hex: "10B981")
    static let warning = Color(hex: "F59E0B")
    static let info = Color(hex: "3B82F6")

    // Category colors
    static let feeding = Color(hex: "3B82F6") // Blue
    static let diaper = Color(hex: "10B981") // Green
    static let sleep = Color(hex: "8B5CF6") // Purple
    static let growth = Color(hex: "F59E0B") // Amber/Orange
    static let health = Color(hex: "EF4444") // Red
    static let generalActivity = Color(hex: "6B7280") // Gray

    // MARK: - Legacy Colors (Deprecated - Use semantic colors above)
    @available(*, deprecated, message: "Use semantic background colors instead")
    static let legacyBackground = Color(hex: "F9FAFB")

    @available(*, deprecated, message: "Use semantic text colors instead")
    static let legacyText = Color(hex: "1F2937")
}

struct BabyPulseTypography {
    struct FontSize {
        static let xs: CGFloat = 12
        static let sm: CGFloat = 14
        static let base: CGFloat = 16
        static let lg: CGFloat = 18
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 30
        static let display: CGFloat = 36
    }

    // Display and Headings - Using system fonts for better accessibility
    static func display(_ size: CGFloat = FontSize.display) -> Font {
        Font.system(size: size, weight: .bold, design: .rounded)
            .leading(.tight)
    }

    static func title1() -> Font {
        Font.system(size: FontSize.xxxl, weight: .bold, design: .rounded)
            .leading(.tight)
    }

    static func title2() -> Font {
        Font.system(size: FontSize.xxl, weight: .bold, design: .rounded)
    }

    static func title3() -> Font {
        Font.system(size: FontSize.xl, weight: .semibold, design: .rounded)
    }

    // Body text
    static func bodyLarge() -> Font {
        Font.system(size: FontSize.lg, weight: .regular, design: .rounded)
    }

    static func body() -> Font {
        Font.system(size: FontSize.base, weight: .regular, design: .rounded)
    }

    static func bodyBold() -> Font {
        Font.system(size: FontSize.base, weight: .semibold, design: .rounded)
    }

    // Small text
    static func footnote() -> Font {
        Font.system(size: FontSize.sm, weight: .regular, design: .rounded)
    }

    static func callout() -> Font {
        Font.system(size: FontSize.base, weight: .medium, design: .rounded)
    }

    static func caption() -> Font {
        Font.system(size: FontSize.xs, weight: .regular, design: .rounded)
    }

    static func captionBold() -> Font {
        Font.system(size: FontSize.xs, weight: .semibold, design: .rounded)
    }
}

struct BabyPulseLayout {
    // Spacing
    static let spacingXS: CGFloat = 4
    static let spacingSM: CGFloat = 8
    static let spacingMD: CGFloat = 16
    static let spacingLG: CGFloat = 24
    static let spacingXL: CGFloat = 32
    static let spacingXXL: CGFloat = 48

    // Padding
    static let paddingXS: CGFloat = 4
    static let paddingSM: CGFloat = 8
    static let paddingMD: CGFloat = 16
    static let paddingLG: CGFloat = 24
    static let paddingXL: CGFloat = 32

    // Corner radius
    static let cornerRadiusSM: CGFloat = 8
    static let cornerRadiusMD: CGFloat = 12
    static let cornerRadiusLG: CGFloat = 16
    static let cornerRadiusXL: CGFloat = 24
    static let cornerRadiusFull: CGFloat = 9999

    // Icon sizes
    static let iconSizeSM: CGFloat = 16
    static let iconSizeMD: CGFloat = 24
    static let iconSizeLG: CGFloat = 32
    static let iconSizeXL: CGFloat = 48

    // Touch targets - Ensuring minimum 44pt touch targets
    static let minTouchTarget: CGFloat = 44
    static let buttonHeightSM: CGFloat = 36
    static let buttonHeightMD: CGFloat = 44
    static let buttonHeightLG: CGFloat = 52

    // For backward compatibility
    static let spacing: CGFloat = spacingMD
    static let cornerRadius: CGFloat = cornerRadiusLG
    static let padding: CGFloat = paddingMD
    static let iconSize: CGFloat = iconSizeMD
}

// MARK: - Standardized Shadow System
struct BabyPulseShadows {
    // Light mode shadows
    private static let lightShadowColor = Color.black

    // Dark mode shadows (more subtle)
    private static let darkShadowColor = Color.black

    // Adaptive shadow color based on color scheme
    private static func shadowColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? darkShadowColor : lightShadowColor
    }

    // Standard shadow definitions
    static func small(colorScheme: ColorScheme = .light) -> Shadow {
        Shadow(
            color: shadowColor(for: colorScheme).opacity(colorScheme == .dark ? 0.3 : 0.05),
            radius: 2,
            x: 0,
            y: 1
        )
    }

    static func medium(colorScheme: ColorScheme = .light) -> Shadow {
        Shadow(
            color: shadowColor(for: colorScheme).opacity(colorScheme == .dark ? 0.4 : 0.08),
            radius: 4,
            x: 0,
            y: 2
        )
    }

    static func large(colorScheme: ColorScheme = .light) -> Shadow {
        Shadow(
            color: shadowColor(for: colorScheme).opacity(colorScheme == .dark ? 0.5 : 0.12),
            radius: 8,
            x: 0,
            y: 4
        )
    }

    static func extraLarge(colorScheme: ColorScheme = .light) -> Shadow {
        Shadow(
            color: shadowColor(for: colorScheme).opacity(colorScheme == .dark ? 0.6 : 0.15),
            radius: 16,
            x: 0,
            y: 8
        )
    }

    public struct Shadow {
        public let color: Color
        public let radius: CGFloat
        public let x: CGFloat
        public let y: CGFloat

        func apply<T: View>(_ view: T) -> some View {
            view.shadow(color: color, radius: radius, x: x, y: y)
        }
    }

    // Legacy shadows for backward compatibility
    @available(*, deprecated, message: "Use the new adaptive shadow methods instead")
    public static let small = Shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    @available(*, deprecated, message: "Use the new adaptive shadow methods instead")
    public static let medium = Shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    @available(*, deprecated, message: "Use the new adaptive shadow methods instead")
    public static let large = Shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
}

// Extension to create colors from hex values
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Enhanced Button Component with Proper Touch Targets
struct BabyPulseButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    let style: BPButtonStyle
    let size: ButtonSize
    let isFullWidth: Bool
    let isLoading: Bool

    @Environment(\.colorScheme) private var colorScheme

    enum BPButtonStyle {
        case primary
        case secondary
        case tertiary
        case destructive

        func background(colorScheme: ColorScheme) -> Color {
            switch self {
            case .primary:
                return BabyPulseColors.primary
            case .secondary:
                return BabyPulseColors.surfacePrimary
            case .tertiary:
                return Color.clear
            case .destructive:
                return BabyPulseColors.error
            }
        }

        func foregroundColor(colorScheme: ColorScheme) -> Color {
            switch self {
            case .primary, .destructive:
                return .white
            case .secondary:
                return BabyPulseColors.primary
            case .tertiary:
                return BabyPulseColors.text
            }
        }

        func border(colorScheme: ColorScheme) -> Color? {
            switch self {
            case .secondary:
                return BabyPulseColors.primary
            case .tertiary:
                return BabyPulseColors.separator
            default:
                return nil
            }
        }
    }

    enum ButtonSize {
        case small
        case medium
        case large

        var height: CGFloat {
            switch self {
            case .small: return BabyPulseLayout.buttonHeightSM
            case .medium: return BabyPulseLayout.buttonHeightMD
            case .large: return BabyPulseLayout.buttonHeightLG
            }
        }

        var horizontalPadding: CGFloat {
            switch self {
            case .small: return 12
            case .medium: return 16
            case .large: return 24
            }
        }

        var font: Font {
            switch self {
            case .small: return BabyPulseTypography.footnote()
            case .medium: return BabyPulseTypography.body()
            case .large: return BabyPulseTypography.bodyLarge()
            }
        }

        var iconSize: CGFloat {
            switch self {
            case .small: return 14
            case .medium: return 18
            case .large: return 20
            }
        }
    }

    init(
        title: String,
        icon: String? = nil,
        style: BPButtonStyle = .primary,
        size: ButtonSize = .medium,
        isFullWidth: Bool = false,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.size = size
        self.isFullWidth = isFullWidth
        self.isLoading = isLoading
        self.action = action
    }

    var body: some View {
        Button(action: {
            // Add haptic feedback
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
            action()
        }) {
            HStack(spacing: 8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: style.foregroundColor(colorScheme: colorScheme)))
                        .scaleEffect(0.8)
                }

                if let icon = icon, !isLoading {
                    Image(systemName: icon)
                        .font(.system(size: size.iconSize, weight: .semibold))
                }

                Text(title)
                    .font(size.font)
                    .fontWeight(.semibold)
            }
            .foregroundColor(style.foregroundColor(colorScheme: colorScheme))
            .frame(height: size.height)
            .frame(maxWidth: isFullWidth ? .infinity : nil)
            .padding(.horizontal, size.horizontalPadding)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(style.background(colorScheme: colorScheme))
            )
            .overlay(
                Group {
                    if let borderColor = style.border(colorScheme: colorScheme) {
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                            .stroke(borderColor, lineWidth: 1.5)
                    }
                }
            )
            .applyShadow(style == .primary ? BabyPulseShadows.medium(colorScheme: colorScheme) : BabyPulseShadows.small(colorScheme: colorScheme))
        }
        .buttonStyle(EnhancedButtonStyle())
        .disabled(isLoading)
        .accessibilityLabel(title)
        .accessibilityHint(isLoading ? "Loading" : "Double tap to activate")
    }
}

// MARK: - Enhanced Button Style with Better Press States
struct EnhancedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Enhanced Card Component
struct BabyPulseCard<Content: View>: View {
    let title: String?
    let icon: String?
    let content: Content
    let style: CardStyle

    @Environment(\.colorScheme) private var colorScheme

    enum CardStyle {
        case elevated
        case outlined
        case plain

        func backgroundColor(colorScheme: ColorScheme) -> Color {
            switch self {
            case .elevated, .outlined:
                return BabyPulseColors.surfacePrimary
            case .plain:
                return Color.clear
            }
        }

        var shadow: BabyPulseShadows.Shadow? {
            switch self {
            case .elevated:
                return nil // Will be applied based on color scheme
            case .outlined, .plain:
                return nil
            }
        }

        func border(colorScheme: ColorScheme) -> (color: Color, width: CGFloat)? {
            switch self {
            case .outlined:
                return (BabyPulseColors.separator, 1.0)
            default:
                return nil
            }
        }
    }

    init(
        title: String? = nil,
        icon: String? = nil,
        style: CardStyle = .elevated,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
            if let title = title {
                HStack(spacing: BabyPulseLayout.spacingSM) {
                    if let icon = icon {
                        Image(systemName: icon)
                            .font(.system(size: BabyPulseLayout.iconSizeMD, weight: .semibold))
                            .foregroundColor(BabyPulseColors.primary)
                    }

                    Text(title)
                        .font(BabyPulseTypography.title3())
                        .foregroundColor(BabyPulseColors.text)
                }
                .padding(.bottom, BabyPulseLayout.spacingSM)
            }

            content
        }
        .padding(BabyPulseLayout.paddingMD)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(style.backgroundColor(colorScheme: colorScheme))
        )
        .overlay(
            Group {
                if let border = style.border(colorScheme: colorScheme) {
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .stroke(border.color, lineWidth: border.width)
                }
            }
        )
        .applyShadow(style == .elevated ? BabyPulseShadows.medium(colorScheme: colorScheme) : BabyPulseShadows.small(colorScheme: colorScheme))
    }
}

// MARK: - Enhanced Input Field
struct BabyPulseTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let isRequired: Bool

    @FocusState private var isFocused: Bool
    @Environment(\.colorScheme) private var colorScheme

    init(
        title: String,
        placeholder: String,
        text: Binding<String>,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        isRequired: Bool = false
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.isRequired = isRequired
    }

    var body: some View {
        VStack(alignment: .leading, spacing: BabyPulseLayout.spacingSM) {
            HStack {
                Text(title)
                    .font(BabyPulseTypography.bodyBold())
                    .foregroundColor(BabyPulseColors.text)

                if isRequired {
                    Text("*")
                        .foregroundColor(BabyPulseColors.error)
                }
            }

            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .font(BabyPulseTypography.body())
            .foregroundColor(BabyPulseColors.text)
            .keyboardType(keyboardType)
            .focused($isFocused)
            .padding(BabyPulseLayout.paddingMD)
            .frame(minHeight: BabyPulseLayout.minTouchTarget)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .fill(BabyPulseColors.surfaceSecondary)
            )
            .overlay(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                    .stroke(
                        isFocused ? BabyPulseColors.primary : BabyPulseColors.separator,
                        lineWidth: isFocused ? 2 : 1
                    )
            )
            .animation(.easeInOut(duration: 0.2), value: isFocused)
        }
    }
}

// MARK: - Empty State Component
struct BabyPulseEmptyState: View {
    let title: String
    let message: String
    let icon: String
    let buttonTitle: String?
    let action: (() -> Void)?

    @Environment(\.colorScheme) private var colorScheme

    init(
        title: String,
        message: String,
        icon: String,
        buttonTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.icon = icon
        self.buttonTitle = buttonTitle
        self.action = action
    }

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            Spacer()

            Image(systemName: icon)
                .font(.system(size: 60, weight: .light))
                .foregroundColor(BabyPulseColors.primary.opacity(0.6))
                .padding(.bottom, BabyPulseLayout.spacingSM)

            VStack(spacing: BabyPulseLayout.spacingSM) {
                Text(title)
                    .font(BabyPulseTypography.title2())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)
                    .multilineTextAlignment(.center)

                Text(message)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }
            .padding(.horizontal, BabyPulseLayout.paddingLG)

            if let buttonTitle = buttonTitle, let action = action {
                BabyPulseButton(
                    title: buttonTitle,
                    icon: "plus",
                    style: .primary,
                    action: action
                )
                .padding(.top, BabyPulseLayout.spacingSM)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity)
        .padding(BabyPulseLayout.paddingLG)
    }
}